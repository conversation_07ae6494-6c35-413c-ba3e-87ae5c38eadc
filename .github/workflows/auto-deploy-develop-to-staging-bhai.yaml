name: Trigger Staging Deployment

on:
  push:
    branches:
      - develop

jobs:
  run-curl:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0  # Fetch full history to ensure git diff works correctly

      - name: Check if specific directories were modified
        id: changes
        run: |
          # List changed files between the previous commit and the current commit
          git diff --name-only ${{ github.event.before }} ${{ github.sha }} > changed_files.txt
          
          # Check if any of the specified directories were modified
          if grep -qE 'frontend/packages/@eesel/ui-core|frontend/packages/@eesel/webapp-dashboard' changed_files.txt; then
            echo "deploy=true" >> $GITHUB_OUTPUT
          else
            echo "deploy=false" >> $GITHUB_OUTPUT
          fi

      - name: Run cURL if changes were detected
        if: steps.changes.outputs.deploy == 'true'
        run: |
          curl "https://api.vercel.com/v1/integrations/deploy/prj_oZ6HFEiqIcxeVdlGMDQ22f8QC84y/7I80Fxeu1l"
