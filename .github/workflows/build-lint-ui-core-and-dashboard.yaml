name: B<PERSON> & Lint - @eesel/ui-core, @eesel/webapp-dashboard

on:
  pull_request:
    branches:
      - '**'       # Triggers on any branch
  push:
    branches:
      - develop

jobs:
  build-and-lint:
    runs-on: ubuntu-latest
    env:
      NODE_OPTIONS: '--max_old_space_size=4096'

    steps:
      # 1. Checkout the repository
      - name: Checkout repository
        uses: actions/checkout@v3

      # 2. Check for changes in @eesel/webapp-dashboard or @eesel/ui-core
      - name: Check for changes in @eesel/webapp-dashboard or @eesel/ui-core
        id: changes
        uses: dorny/paths-filter@v2
        with:
          filters: |
            webapp_dashboard:
              - 'frontend/packages/@eesel/webapp-dashboard/**'
            ui_core:
              - 'frontend/packages/@eesel/ui-core/**'

      # 3. Set up Node.js with Yarn caching
      - name: Set up Node.js
        if: steps.changes.outputs.webapp_dashboard == 'true' || steps.changes.outputs.ui_core == 'true'
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'yarn'    
          cache-dependency-path: |
            frontend/yarn.lock
            
      # 4. Install dependencies from the ./frontend directory
      - name: Install dependencies
        if: steps.changes.outputs.webapp_dashboard == 'true' || steps.changes.outputs.ui_core == 'true'
        run: yarn install --frozen-lockfile
        working-directory: frontend

      # 5. Run lint and build for @eesel/webapp-dashboard if changes are detected
      - name: Lint and build @eesel/webapp-dashboard
        if: steps.changes.outputs.webapp_dashboard == 'true'
        run: |
          yarn lint
          yarn build
        working-directory: frontend/packages/@eesel/webapp-dashboard

      # 6. Run lint and build for @eesel/ui-core if changes are detected
      - name: Lint and build @eesel/ui-core
        if: steps.changes.outputs.ui_core == 'true'
        run: |
          yarn lint
          yarn build
        working-directory: frontend/packages/@eesel/ui-core
