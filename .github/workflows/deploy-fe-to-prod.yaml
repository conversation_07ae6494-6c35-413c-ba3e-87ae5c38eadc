name: Daily Frontend Production Deploy

on:
  schedule:
    # Runs at 10 AM AEST (00:00 UTC)
    - cron: '0 0 * * *'
  workflow_dispatch:
    inputs:
      reason:
        description: 'Reason for manual deployment'
        required: true
        type: string

jobs:
  deploy:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/master'  # Only run on master branch

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Needed to ensure all branches are fetched

      - name: Configure Git
        run: |
          git config --global user.name 'GitHub Actions'
          git config --global user.email '<EMAIL>'

      - name: Notify Slack of deployment start
        run: |
          DEPLOY_REASON="${{ github.event.inputs.reason || 'Scheduled deployment' }}"
          WORKFLOW_URL="$GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID"
          curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"🚀 Frontend Production deployment started via GitHub Actions\nReason: ${DEPLOY_REASON}\nWorkflow: ${WORKFLOW_URL}\"}" \
            "*******************************************************************************"

      - name: Deploy to production
        run: |
          cd frontend/packages/@eesel/webapp-dashboard
          yarn release:production
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Trigger Vercel Deployment
        run: |
          curl -X POST "https://api.vercel.com/v1/integrations/deploy/prj_oZ6HFEiqIcxeVdlGMDQ22f8QC84y/zMASz7kA9B"
