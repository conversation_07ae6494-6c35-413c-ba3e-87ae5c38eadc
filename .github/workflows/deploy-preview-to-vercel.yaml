name: Deploy preview to Ver<PERSON>

on:
  push:
    branches:
      - '*'
  pull_request:
    branches:
      - '*'

jobs:
  deploy-preview:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Trigger Vercel preview deployment
        id: deploy
        env:
          VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
          VERCEL_PROJECT_NAME: ${{ secrets.VERCEL_PROJECT_NAME }}
          VERCEL_REPO_ID: ${{ secrets.VERCEL_REPO_ID }}
          BRANCH_NAME: ${{ github.ref }}
        run: |
          echo "VERCEL_TOKEN: $VERCEL_TOKEN"
          echo "VERCEL_PROJECT_NAME: $VERCEL_PROJECT_NAME"
          echo "VERCEL_REPO_ID: $VERCEL_REPO_ID"
          echo "BRANCH_NAME: $BRANCH_NAME"
          BODY=$(jq -n \
            --arg name "$VERCEL_PROJECT_NAME" \
            --arg repo "$VERCEL_REPO_ID" \
            --arg ref "$BRANCH_NAME" \
            '{name: $name, gitSource: {type: "github", repoId: $repo, ref: $ref}, target: "staging"}')
          RESPONSE=$(curl -s -X POST 'https://api.vercel.com/v13/deployments?teamId=eeselapp' \
            -H "Authorization: Bearer $VERCEL_TOKEN" \
            -H 'Content-Type: application/json' \
            -d "$BODY")
          echo "$RESPONSE"
          echo "preview_url=$(echo "$RESPONSE" | jq -r '.url')" >> "$GITHUB_OUTPUT"

      - name: Comment preview URL on PR
        if: github.event_name == 'pull_request'
        run: |
          gh pr comment ${{ github.event.pull_request.number }} --body "Preview deployment ready: https://${{ steps.deploy.outputs.preview_url }}"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
