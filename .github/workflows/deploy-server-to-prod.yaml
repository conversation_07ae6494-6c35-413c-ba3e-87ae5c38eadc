name: Deploy Server to Prod

on:
  workflow_dispatch:
    inputs:
      git_hash:
        description: 'Git hash to deploy (defaults to latest master)'
        required: false
        type: string

jobs:
  deploy:
    name: SSM Deploy
    runs-on: ubuntu-latest

    # only allow these actors
    if: contains(fromJSON('["<PERSON><PERSON><PERSON><PERSON><PERSON>","Alton999","rob-err"]'), github.actor)

    # compute the fallback Git hash once
    env:
      GIT_HASH: ${{ inputs.git_hash != '' && inputs.git_hash || 'origin/master' }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Get actual commit hash
        id: get_hash
        run: |
          if [[ "${{ env.GIT_HASH }}" == "origin/master" ]]; then
            echo "ACTUAL_HASH=$(git rev-parse origin/master)" >> $GITHUB_ENV
          else
            echo "ACTUAL_HASH=${{ env.GIT_HASH }}" >> $GITHUB_ENV
          fi

      - name: Notify deployment start
        if: always()
        uses: slackapi/slack-github-action@v1.24.0
        with:
          payload: |
            {
              "text": "CI: 🚀 Server deployment started by `${{ github.actor }}` (${{ inputs.git_hash != '' && 'custom hash' || 'latest master' }}: `${{ env.GIT_HASH }}` → `${{ env.ACTUAL_HASH }}`)"
            }
        env:
          SLACK_WEBHOOK_URL: *******************************************************************************

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.SERVER_DEPLOYMENT_AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.SERVER_DEPLOYMENT_AWS_SECRET_ACCESS_TOKEN }}
          aws-region: us-east-1

      - name: Send SSM deploy command
        id: ssm
        env:
          GIT_HASH: ${{ env.GIT_HASH }}
          GITHUB_ACTOR: ${{ github.actor }}
          SLACK_WEBHOOK_URL: *******************************************************************************
          # in your GH Action step
        run: |
          cat <<'EOF' > raw-params.json
          {
            "commands": [
              "sudo -u deployer bash -lc \"cd /home/<USER>/src/eeselai && git fetch --all && git checkout --force ${GIT_HASH}\"",
              "echo \"Running rsync in dry-run mode to simulate deployment…\"",
              "sudo -u deployer bash -lc \"cd /home/<USER>/src/eeselai/server && rsync -avz --chmod=D775,F775 --checksum --chown=ec2-user:deploygroup  --exclude '.env' --exclude 'Dockerfile' --exclude 'docker-compose.yml' --exclude='*.pyc' --exclude='.*' ./ /home/<USER>/app_prod_us/\""
            ]
          }
          EOF

          # now expand only the env vars you want
          envsubst < raw-params.json > params.json

          aws ssm send-command \
            --instance-ids i-0724a94b13a12246f \
            --document-name AWS-RunShellScript \
            --comment "Deploy ${GIT_HASH} via GitHub Actions" \
            --parameters file://params.json