name: Extension v2 - Lin<PERSON> and Format

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'frontend/packages/@eesel/extension-eeselai-v2/**'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'frontend/packages/@eesel/extension-eeselai-v2/**'

jobs:
  lint-and-format:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]

    defaults:
      run:
        working-directory: frontend/packages/@eesel/extension-eeselai-v2

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'yarn'
        cache-dependency-path: frontend/packages/@eesel/extension-eeselai-v2/yarn.lock

    - name: Install dependencies
      run: yarn install --frozen-lockfile

    # - name: Run ESLint
    #   run: yarn lint

    - name: Check Prettier formatting
      run: yarn format:check

    - name: Run tests
      run: yarn test:ci

    - name: Build extension
      run: yarn build

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: extension-build-${{ matrix.node-version }}
        path: frontend/packages/@eesel/extension-eeselai-v2/dist/
        retention-days: 7

  auto-format:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request' && github.actor != 'dependabot[bot]'
    
    defaults:
      run:
        working-directory: frontend/packages/@eesel/extension-eeselai-v2

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        ref: ${{ github.head_ref }}

    - name: Use Node.js 18.x
      uses: actions/setup-node@v4
      with:
        node-version: 18.x
        cache: 'yarn'
        cache-dependency-path: frontend/packages/@eesel/extension-eeselai-v2/yarn.lock

    - name: Install dependencies
      run: yarn install --frozen-lockfile

    - name: Run Prettier
      run: yarn format

    - name: Check for changes
      id: verify-changed-files
      run: |
        if [ -n "$(git status --porcelain)" ]; then
          echo "changed=true" >> $GITHUB_OUTPUT
        else
          echo "changed=false" >> $GITHUB_OUTPUT
        fi

    - name: Commit and push changes
      if: steps.verify-changed-files.outputs.changed == 'true'
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add .
        git commit -m "Auto-format code with Prettier [skip ci]" || exit 0
        git push
