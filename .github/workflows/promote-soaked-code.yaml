name: Promote Soaked Code

on:
  schedule:
    # Run at 11:30 PM UTC (30 mins before frontend deploy)
    - cron: '30 23 * * *'
  # Allow manual triggering with force option
  workflow_dispatch:
    inputs:
      force:
        description: 'Force promote develop to master (maintainers only)'
        type: boolean
        default: false
        required: false

jobs:
  promote-to-master:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Git
        run: |
          git config user.name "GitHub Actions Bot"
          git config user.email "<EMAIL>"

      - name: Check maintainer status if force promoting
        if: github.event.inputs.force == 'true'
        run: |
          MAINTAINER_STATUS=$(gh api repos/${{ github.repository }}/collaborators/${{ github.actor }}/permission | jq -r .permission)
          if [[ ! "$MAINTAINER_STATUS" =~ ^(admin|maintain)$ ]]; then
            echo "Error: Force promote is only available to maintainers and admins"
            exit 1
          fi
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Find and promote soaked commits
        run: |
          git fetch origin

          # If force promoting, skip the soak time check
          if [[ "${{ github.event.inputs.force }}" == "true" ]]; then
            git checkout master
            git merge --ff-only origin/develop
            git push origin master
            echo "Force promoted develop to master"
            exit 0
          fi

          TARGET_COMMIT=$(git rev-list -1 --before="24 hours ago" origin/develop)

          if [ -z "$TARGET_COMMIT" ]; then
            echo "No commits on develop are older than 24 hours"
            exit 0
          fi

          if git merge-base --is-ancestor "$TARGET_COMMIT" origin/master; then
            echo "Master already contains the latest soaked commit"
            exit 0
          fi

          git checkout master
          git merge --ff-only "$TARGET_COMMIT"
          git push origin master

