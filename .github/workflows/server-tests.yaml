name: Server Python tests

on:
  pull_request:
    branches:
      - '**'
  push:
    branches:
      - develop

jobs:
  server-tests:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Check for server changes
        id: changes
        uses: dorny/paths-filter@v2
        with:
          filters: |
            server:
              - 'server/**'

      - name: Set up Python
        if: steps.changes.outputs.server == 'true'
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

        - name: Compile Python files
          if: steps.changes.outputs.server == 'true'
          run: python -m compileall server

        - name: Make run_tests executable
          if: steps.changes.outputs.server == 'true'
          run: chmod +x server/scripts/run_tests.sh

        - name: Run server tests
          if: steps.changes.outputs.server == 'true'
          run: ./server/scripts/run_tests.sh
