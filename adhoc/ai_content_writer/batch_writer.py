import os
from writer import generate_content, VARIABLES_DIR, reset_output_files
import shutil
import logging
import time
from httpx import HTTPStatusError
from datetime import datetime, timedelta

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RateLimiter:
    def __init__(self, requests_per_minute=950):  # Setting slightly below the 1000 limit
        self.requests_per_minute = requests_per_minute
        self.requests = []
        
    def wait_if_needed(self):
        now = datetime.now()
        # Remove requests older than 1 minute
        self.requests = [req_time for req_time in self.requests if now - req_time < timedelta(minutes=1)]
        
        if len(self.requests) >= self.requests_per_minute:
            # Wait until the oldest request is more than 1 minute old
            sleep_time = 61 - (now - self.requests[0]).total_seconds()
            if sleep_time > 0:
                logger.info(f"Rate limit approaching, waiting for {sleep_time:.2f} seconds")
                time.sleep(sleep_time)
        
        self.requests.append(now)

def process_with_retry(func, max_retries=5, initial_wait=1):
    """Execute a function with exponential backoff retry logic"""
    rate_limiter = RateLimiter()
    
    for attempt in range(max_retries):
        try:
            rate_limiter.wait_if_needed()
            return func()
        except HTTPStatusError as e:
            if e.response.status_code == 429:  # Too Many Requests
                wait_time = initial_wait * (2 ** attempt)
                logger.warning(f"Rate limit hit. Waiting {wait_time} seconds before retry {attempt + 1}/{max_retries}")
                time.sleep(wait_time)
            else:
                raise
        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}")
            raise
    
    raise Exception(f"Failed after {max_retries} retries")

def process_markdown_files(input_dir='batch_input', output_dir='batch_output'):
    """
    Process all markdown files in the input directory and generate content for each.
    
    Args:
        input_dir: Directory containing input markdown files
        output_dir: Directory to store output files
    """
    # Create directories if they don't exist
    os.makedirs(input_dir, exist_ok=True)
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(VARIABLES_DIR, exist_ok=True)

    # Get all markdown files from input directory
    md_files = [f for f in os.listdir(input_dir) if f.endswith('.md')]
    total_files = len(md_files)

    for idx, md_file in enumerate(md_files, 1):
        try:
            # Get the title (filename without extension)
            title = os.path.splitext(md_file)[0]
            logger.info(f"Processing {title} ({idx}/{total_files})")

            # Read the content of the markdown file
            with open(os.path.join(input_dir, md_file), 'r', encoding='utf-8') as f:
                content = f.read()

            # Reset all variables
            reset_output_files()

            # Write topic.md and context.md
            with open(os.path.join(VARIABLES_DIR, 'topic.md'), 'w', encoding='utf-8') as f:
                f.write(title)
            with open(os.path.join(VARIABLES_DIR, 'context.md'), 'w', encoding='utf-8') as f:
                f.write(content)

            # Generate content with retry logic
            def generate():
                return generate_content(mode='full')
            
            process_with_retry(generate)

            # Copy the final outputs to the output directory
            output_files = {
                'blog_draft.md': f'output_{title}_draft.md',
                'blog_final.md': f'output_{title}_final.md'
            }

            for source, dest in output_files.items():
                source_path = os.path.join(VARIABLES_DIR, source)
                if os.path.exists(source_path):
                    shutil.copy2(
                        source_path,
                        os.path.join(output_dir, dest)
                    )
                    logger.info(f"Created {dest}")

        except Exception as e:
            logger.error(f"Error processing {md_file}: {str(e)}")
            continue

if __name__ == "__main__":
    process_markdown_files() 