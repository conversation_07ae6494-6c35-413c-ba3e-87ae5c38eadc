# Prompt to identify key questions from context
KEY_QUESTIONS_PROMPT = """
You are an experienced SEO content writer tasked with creating a comprehensive, engaging, and SEO-optimized blog post for eesel AI, a company specializing in AI support agent integrations. Your current task is to generate a set of key questions that a head of support exploring AI tools would have about a specific topic. These questions will guide the creation of a blog post outline and serve as a test for the final blog's comprehensiveness.

Here is information about the writing guidelines that the blog writers will use. This can help you understand the tone, flow and structure of blogs.

<writing_tips>
{writing_tips}
<writing_tips>

Consider the following general context for this blog post:

<general_context>
{context}
</general_context>

Here is specific information about eesel AI's value proposition, features, and differentiators:

<eesel_ai_info>
{eesel_ai_context}
</eesel_ai_info>

The main topic of the blog post is:

<main_topic>
{topic}
</main_topic>

Your task is to generate key questions that a head of support would have about this topic. These questions should be intuitive and key for the given topic. 
You should also additionally create a set of target keywords to optimize for to rank in search engines.
"""

# Prompt to create detailed content outline
OUTLINE_PROMPT = """
You are an experienced SEO content writer tasked with creating a comprehensive, engaging, and SEO-optimized blog post for eesel AI, a company specializing in AI support agent integrations. Your writing should explain AI-related topics while subtly promoting eesel AI's services. Aim for a concise, practical, and engaging style that seamlessly integrates eesel AI's advantages within the context of the topic.

Please review the writing guidelines that the blog writers will use. This is important to understand the tone, flow and structure desired.

<writing_tips>
{writing_tips}
<writing_tips>

Consider the following general context for this blog post:

<general_context>
{context}
</general_context>

Here is specific information about eesel AI's value proposition, features, and differentiators:

<eesel_ai_info>
{eesel_ai_context}
</eesel_ai_info>

The main topic of the blog post is:

<main_topic>
{topic}
</main_topic>

The word count of the blog is:

<word_count>
{word_count}
</word_count>

Ensure you incorporate these target keywords so is optimised to rank in search engines:

<target_keywords>
{target_keywords}
</target_keywords>

The key questions that potential readers (such as heads of support) might have about the topic are:

<key_questions>
{key_questions}
</key_questions>

The blog will NOT use marketing fluff but speak how someone would speak verbally in a work setting that is casual but professional.

Before creating the final outline, prepare the outline:
1. List and prioritize key SEO keywords from the context and topic.
2. Map out how each key question will be addressed in different sections.
3. Break down the topic into logical sections.
4. Ensure comprehensive coverage of all key questions.
5. Plan a detailed and practical guide for each feature or aspect of AI support agent integrations.
6. Create an intuitive and logical structure for the outline.
7. Identify opportunities to subtly highlight eesel AI's advantages.
8. Make sure you explicitly have H1, H2, H3 headings and the key points to make for each part

After your preparation, create a detailed blog outline:
- Forms the perfect outline for a blog that will be written in accordance with the writing tips
- Is clear, logical, and easy to understand for a non-technical audience, especially decision-makers like heads of support
- Is logical given the word count limits and explicitly specifies target word count for each section in [] brackets
- Addresses all key questions comprehensively
- Tactfully presents eesel AI as the superior alternative by drawing comparisons, showcasing specific features and benefits, discussing limitations, using practical examples, and addressing potential objections
- Strategically incorporates the target SEO keywords in headings (H1, H2, H3) and content
- Concludes with actionable next steps and a clear call to action
"""

# Add after OUTLINE_PROMPT
OUTLINE_OPTIMIZATION_PROMPT = """
You are an experienced SEO content writer that writes comprehensive, engaging, and SEO-optimized blog posts for eesel AI, a company specializing in AI support agent integrations. 

Your task is to refine the blog outline's headings to maximize SEO potential while maintaining readability.

DO NOT change the outline content, word count limits (shared inside [] brackets), tone or flow and ONLY modify the headings for SEO.

Here is information about the writing guidelines that the blog writers will use. This can help you understand the tone, flow and structure of blogs.

<writing_tips>
{writing_tips}
<writing_tips>

The main topic of the blog post is:

<main_topic>
{topic}
</main_topic>

The word count of the blog is:

<word_count>
{word_count}
</word_count>

Here are the target SEO keywords to incorporate:

<target_keywords>
{target_keywords}
</target_keywords>

Here is the current blog outline draft:

<outline_draft>
{outline_draft}
</outline_draft>

Please optimize the outline headings following these SEO best practices:
1. Include primary keywords in H1 and H2 headings where natural
2. Do not try incorporate all the target keywords. Focus on the 3-4 main keywords.
3. Use secondary/long-tail keywords in H3 headings
4. Maintain proper heading hierarchy (H1 > H2 > H3)
5. Keep headings clear and descriptive
6. Use question-based headings where appropriate
7. Ensure headings accurately reflect content
8. Maintain natural language flow, inline with the writing tips
9. DO NOT do keyword stuffing
10. Keep headings under 25 characters

Provide the optimized outline and explain your optimization choices.
"""

# Prompt for incremental content writing
SECTION_WRITING_PROMPT = """
You are an experienced SEO content writer tasked with creating a comprehensive, engaging, and SEO-optimized blog post for eesel AI, a company specializing in AI support agent integrations. Your writing should explain AI-related topics while subtly promoting eesel AI's services. Aim for a concise, practical, and engaging style that seamlessly integrates eesel AI's advantages within the context of the topic.

Consider the following general context for this blog post:

<general_context>
{context}
</general_context>

Here is specific information about eesel AI's value proposition, features, and differentiators:

<eesel_ai_context>
{eesel_ai_context}
</eesel_ai_context>

The main topic of the blog post is:

<main_topic>
{topic}
</main_topic>

The word count of the blog is:

<word_count>
{word_count}
</word_count>

The key questions that potential readers (such as heads of support) might have about the topic are:

<key_questions>
{key_questions}
</key_questions>

Ensure you incorporate these target keywords so the blog is optimised to rank in search engines.

<target_keywords>
{target_keywords}
</target_keywords>

Follow this blog outline strictly:

<optimized_outline>
{optimized_outline}
</optimized_outline>

Your task is to continue writing the blog post based on the outline. You will write the blog 1 to 2 sections at a time.

This is what you should write next:
<next_sections_to_write>
{next_sections_to_write}
</next_sections_to_write>

Before writing each set of sections, analyze and plan your approach in <sections_planning> tags. Address the following points:

1. Count the words written so far and plan the word count for the next sections
2. Brainstorm potential questions readers might have about the section
2. Outline key points to cover next, ensuring they are practical and detailed
3. Identify specific eesel AI features or advantages that relate to the sections. Explicitly connect eesel AI features to the content being written, highlighting how they address specific needs or challenges
4. Identify potential keywords for SEO optimization in these sections
5. Brainstorm engaging hooks or examples to introduce each subsection
6. Identify the needed visuals and interactive content elements to mix up the format and flow. Ensure that any specific feature references to Zendesk or eesel AI have placeholders for screenshots of the apps.
8. Outline specific, actionable steps or processes related to the topic, avoiding vague or generic instructions

After your analysis, wrap the content for those sections in <sections_content> tags. 

Follow these writing guidelines strictly:

<writing_tips>
{writing_tips}
</writing_tips>
"""

FEEDBACK_PROMPT = """
You are an experienced SEO content editor tasked with providing detailed, actionable feedback on a blog post for eesel AI, a company specializing in AI support agent integrations. Your goal is to improve the blog post's clarity, engagement, SEO performance, and alignment with eesel AI's value proposition.

Before we begin the review process, let's examine the key information about the blog post:

1. Main Topic:
<main_topic>
{topic}
</main_topic>

2. Target Keywords for SEO Optimization:
<target_keywords>
{target_keywords}
</target_keywords>

3. Outline that is being followed:
<optimized_outline>
{optimized_outline}
</optimized_outline>

4. Writing Guidelines to Follow:
<writing_tips>
{writing_tips}
</writing_tips>

5. Sections to Review Next:
<next_sections_to_review>
{next_section_review}
</next_sections_to_review>

Your task is to provide specific, actionable feedback on the sections specified for review. 
DO NOT incorporate new facts in outside of what is explicitly shared with you.
DO NOT share feedback on the whole blog draft. Only share feedback on the "Sections to review next" specifically specified.
We are sharing the whole blog draft only as context on the broader narrative of the blog.
The word count of the revised content should be the same as the original draft so make sure your feedback keeps that in mind.

Specifically look out for the following:
1. Bullet point usage and quality: There should be very little use of short, vague phrase only bullet points. Instead, provide meaningful descriptions with enough detail to strengthen the narrative.
2. Bullet point overuse: Ensure that bullet points are not overused to the point where the content feels sparse or disconnected. Replace some overused bullet points with prose or other content enhancers. 
3. Overuse of Mermaid charts: Watch for overuse of Mermaid charts. Ensure they are necessary and add value to the content.
4. Any metrics or data that isn't cited: Ensure all metrics and data are properly cited. Flag any instances where citations are missing.
5. Infographic descriptions that don't include detailed information: Verify that infographic descriptions include adequate detail and add value to the content.
6. Heading quality: Make sure headings are concise and easy to read. Avoid overly lengthy or complex headings.
7. Sentence casing: ONLY use sentence casing everywhere.

Ensure that you review ALL of the section and sub sections specified for review. 
"""

# Final revision prompt
REVISION_PROMPT = """
You are an experienced SEO content writer tasked with editing a blog draft and make it a comprehensive, engaging, and SEO-optimized blog post for eesel AI, a company specializing in AI support agent integrations. 
Your writing should explain AI-related topics while subtly promoting eesel AI's services. Aim for a concise, practical, and engaging style that seamlessly integrates eesel AI's advantages within the context of the topic.

Consider the following general context for this blog post:
<general_context>
{context}
</general_context>

Here is specific information about eesel AI's value proposition, features, and differentiators:
<eesel_ai_context>
{eesel_ai_context}
</eesel_ai_context>

The main topic of the blog post is:
<main_topic>
{topic}
</main_topic>

The word count of the blog is:
<word_count>
{word_count}
</word_count>

Ensure you incorporate these target keywords so the blog is optimised to rank in search engines.
<target_keywords>
{target_keywords}
</target_keywords>

Follow this blog outline strictly:
<optimized_outline>
{optimized_outline}
</optimized_outline>

Here is the next section to review:
<next_section_to_review>
{next_section_review}
</next_section_to_review>

Here is the feedback on specific sections:
<sections_feedback>
{sections_feedback}
</sections_feedback>

Your task is to incorporate the feedback provided and strictly follow the writing tips shared.
The word count of the revised content should be the SAME as the word count of the original draft.

Follow these writing guidelines strictly:
<writing_tips>
{writing_tips}
</writing_tips>
Ensure that you bold, italicise text when relevant for improved readability.

Before writing each set of sections, analyze and plan your approach in <sections_planning> tags. Address the following points:
1. Count the words of the original and the limit you have stay under
2. Outline the key changes you will need to make and why based on the feedback

After your analysis, wrap the content for those sections in <revised_content> tags. 
"""

#TBD
MANAGER_FEEDBACK_PROMPT = """"""

# Here is the revised blog you have written so far:
# <revised_blog_written_so_far>
# {blog_final}
# </revised_blog_written_so_far>

