import requests
from trafilatura import fetch_url, extract
from dotenv import load_dotenv
import os
from langchain_anthropic import <PERSON><PERSON><PERSON><PERSON><PERSON>
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.output_parsers import StrOutputParser
from dotenv import load_dotenv
from langchain_core.prompts import <PERSON>t<PERSON><PERSON>pt<PERSON><PERSON>plate
from prompt import SYSTEM_PROMPT

# Automatically load environment variables from the .env file
load_dotenv()

# Access the environment variables
GOOGLE_API_KEY = os.getenv('GOOGLE_API_KEY')

def google_search(api_key, keyword, search_engine_id):
    print(f"Starting search for keyword: {keyword}")

    # Step 1: Perform the search query
    search_url = f"https://customsearch.googleapis.com/customsearch/v1?key={api_key}&cx={search_engine_id}&q={keyword}"
    print(f"Search URL: {search_url}")
    
    search_response = requests.get(search_url)
    print(f"Search response status code: {search_response.status_code}")

    if search_response.status_code == 200:
        search_results = search_response.json().get('items', [])
        print(f"Number of results found: {len(search_results)}")
        result_list = []

        for idx, result in enumerate(search_results):
            title = result.get('title')
            url = result.get('link')
            print(f"\nProcessing result {idx + 1}: {title} ({url})")

            # Step 2: Use Trafilatura to fetch and extract content as Markdown
            try:
                markdown_content = fetch_and_convert_to_markdown(url)
                if markdown_content:
                    result_list.append({
                        "title": title,
                        "url": url,
                        "content": markdown_content
                    })
                else:
                    print(f"Failed to extract content from {url}")

            except Exception as e:
                print(f"Exception occurred while processing {url}: {str(e)}")

        return result_list
    else:
        print(f"Failed to retrieve search results. Status code: {search_response.status_code}")
        return []




def fetch_and_convert_to_markdown(url):
    print(f"Fetching content from {url} using Trafilatura...")

    # Step 1: Fetch the content using Trafilatura
    downloaded = fetch_url(url)
    if downloaded is None:
        print(f"Failed to fetch content from {url}")
        return None

    # Step 2: Extract the content and convert to Markdown
    markdown_content = extract(downloaded, output_format="markdown", with_metadata=True)
    
    if markdown_content is None or markdown_content.strip() == "":
        print(f"No content extracted from {url}")
        return None
    
    return markdown_content

def parse_markdown_to_string(results):
    markdown_string = ""
    for result in results:
        markdown_string += f"# {result['title']}\n"
        markdown_string += f"**URL**: {result['url']}\n\n"
        markdown_string += result['content']
        markdown_string += "\n\n---\n\n"

    return markdown_string

# Example usage
if __name__ == "__main__":
    api_key = GOOGLE_API_KEY
    search_engine_id = "07b271f6dd72849af"
    keyword = input("Enter the keyword to search: ")

    search_results = google_search(api_key, keyword, search_engine_id)
    
    if search_results:
        context = parse_markdown_to_string(search_results)
        
        prompt_template = ChatPromptTemplate.from_messages(
            [("system", SYSTEM_PROMPT), ("human", "Please draft an article titled: {title}")]
        )
        print(f"Printing prompt_template {prompt_template}:")

        # Access the ANTHROPIC_API_KEY environment variable
        api_key = os.getenv('ANTHROPIC_API_KEY')

        if api_key is None:
            raise ValueError("ANTHROPIC_API_KEY is not set in the environment variables")

        # Set the ANTHROPIC_API_KEY environment variable
        os.environ["ANTHROPIC_API_KEY"] = api_key

        model = ChatAnthropic(model="claude-3-5-sonnet-20240620")
        parser = StrOutputParser()
        chain = prompt_template | model | parser

        print(chain.invoke({ "context": context, "title": "How Zendesk AI pricing works and is it worth it?"}))

