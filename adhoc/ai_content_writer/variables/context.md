<context_blog>
Blog title: AI-powered ticketing automation: A complete guide for 2025
URL: https://www.zendesk.com/blog/ai-powered-ticketing/
Content:
# 
**AI-powered ticketing automation: A complete guide for 2025**


Read our guide to learn how AI-powered ticketing automation can save agents time and reduce costs by helping your business do more with less.


By <PERSON>, Staff Writer


Last updated February 24, 2025



- [ ] 
- [ ] 
- [ ] 
- [ ] 

## 
**What is AI-powered ticketing?**


**AI-powered ticketing** refers to using artificial intelligence (AI) and machine learning (ML) to automate and enhance various aspects of the ticketing process.


[AI help desks](https://www.zendesk.com/service/help-desk-software/ai-help-desk/) use AI algorithms to streamline workflows, making ticket management smoother and more efficient. With AI-powered ticketing systems, tasks like sorting tickets and prioritizing conversations happen automatically. These systems can even suggest solutions and spot trends in customer questions, enhancing the customer and [employee experience (EX)](https://www.zendesk.com/blog/employee-experience/).


[Ticketing systems](https://www.zendesk.com/service/ticketing-system/) have long been the standard for efficiently categorizing, routing, and handling customer service requests. With the evolution of AI, ticketing systems are becoming much more dynamic and powerful.


In conjunction with your [customer service software](https://www.zendesk.com/service/ticketing-system/customer-service-management-software/), AI-powered ticket automation uses artificial intelligence to make handling support tickets smoother and faster, increasing both agent and customer satisfaction. In this guide, we’ll dive into artificial intelligence tickets, what AI ticketing is all about, why it’s better than traditional methods, how it works, and the perks it brings to businesses of all sizes.


**More in this guide:**

* [Why is AI ticketing automation important?](https://www.zendesk.com/blog/ai-powered-ticketing/#Why%20is%20AI%20ticketing%20automation%20important?)
* [How an AI ticketing system works](https://www.zendesk.com/blog/ai-powered-ticketing/#How%20an%20AI%20ticketing%20system%20works)
* [Challenges of ticket systems without AI ticketing](https://www.zendesk.com/blog/ai-powered-ticketing/#Challenges%20of%20ticket%20systems%20without%20AI%20ticketing)
* [Benefits of AI ticketing systems](https://www.zendesk.com/blog/ai-powered-ticketing/#Benefits%20of%20AI%20ticketing%20systems)
* [Examples of AI ticketing system use cases](https://www.zendesk.com/blog/ai-powered-ticketing/#Examples%20of%20AI%20ticketing%20system%20use%20cases)
* [Frequently asked questions](https://www.zendesk.com/blog/ai-powered-ticketing/#Frequently%20asked%20questions)
* [Punch your ticket to a better CX](https://www.zendesk.com/blog/ai-powered-ticketing/#Punch%20your%20ticket%20to%20a%20better%20CX)

## 
**Why is AI ticketing automation important?**


AI ticketing automation provides advantages over traditional ticketing systems. It automates tasks so businesses can do more with less, boosting efficiency and reducing costs. AI improves [customer satisfaction](https://www.zendesk.com/blog/3-steps-achieving-customer-satisfaction-loyalty/) through fast response times, quick ticket resolution, and personalized support. It also improves over time by learning from each interaction.


## 
**How an AI ticketing system works**


Let’s peek behind the curtain and learn how AI ticketing systems work. Natural language processing (NLP) and [machine learning (ML)](https://www.zendesk.com/blog/machine-learning-new-potential-customer-service/) enable your AI-powered ticketing system to automate and optimize various tasks throughout the ticketing process.


By analyzing the text, NLP helps AI systems interpret and understand what customers mean in their inquiries and ticket descriptions. It identifies keywords and understands [customer sentiment](https://www.zendesk.com/blog/customer-sentiment/) to intelligently route and triage tickets.


[Machine learning algorithms](https://www.zendesk.com/blog/machine-learning-and-deep-learning/) analyze past ticketing data to become more efficient. These algorithms help AI prioritize tickets based on urgency or customer history, suggest solutions tailored to each case, and predict future trends to help teams allocate resources more effectively.


AI ticketing systems combine these technologies to understand [customer needs](https://www.zendesk.com/blog/customer-needs/) and automate processes for a more efficient and personalized ticketing experience.


## 
**Challenges of ticket systems without AI ticketing**


Conventional ticketing systems without AI add extra challenges to ticket management. Here are a few drawbacks associated with a manual ticketing process.


### 
**Difficulty classifying and assigning tickets to the right agent**


Classifying and assigning [support tickets](https://www.zendesk.com/blog/what-is-a-support-ticket/) to the right agent is tough without AI. With traditional ticketing systems, agents must read each ticket and manually classify and assign it. This process can be time-consuming, especially when there are a lot of tickets. Agents can also misread or misinterpret the meaning of ticket requests, which can lead to mistakes, delays, and frustration.


Plus, traditional systems aren’t necessarily equipped to provide helpful information during ticket resolution, so agents waste time looking for answers, and customers don’t always get the help they need. Without AI, this process is slow, mistake-prone, and can lead to [bad customer service](https://www.zendesk.com/blog/what-is-bad-customer-service/).


**How AI ticketing helps:** AI uses intent detection, language detection, and sentiment analysis to automatically classify incoming requests, prioritize them, and route them to the best-suited agent or department.


### 
**Lack of personalization**


Without AI, traditional ticketing systems may struggle—or find it nearly impossible—to personalize customer conversations at scale. Integrating bots with your tech stack can help deliver highly personalized customer responses. AI can recognize past customer interactions and preferences, providing agents with insights to personalize the experience. For example, AI can summarize tickets and surface the intent and sentiment of an interaction to get agents up to speed faster.


**How AI ticketing helps:** AI ticketing systems can analyze customer sentiment within conversations, giving agents the insight and context necessary to tailor their responses and proactively adapt their approach.


### 
**Repetitive tickets**


Support agents often find themselves handling the same common problems. Repetitive tickets can slow down issue resolutions, frustrating both agents and customers. Traditional systems may have ways for customers to find answers independently, but they don’t offer digital agents to handle these recurring requests.


[AI chatbots](https://www.zendesk.com/service/messaging/chatbot/), on the other hand, can provide [24/7 support](https://www.zendesk.com/blog/247-support-without-247-staff/), answer frequently asked questions, and resolve common issues. Chatbots can also pull consistent and accurate information from your [knowledge base](https://www.zendesk.com/blog/knowledge-base/) to help customers solve their problems independently. This means fewer repetitive tickets for your agents and quicker resolutions for your customers.


**How AI ticketing helps:** AI can speed up agent reply times by providing recommended responses based on the context of the conversation.


### 
**Limited insights**


Every day, support desk requests create a massive amount of data. Traditional ticketing systems might struggle to analyze information efficiently, extract meaningful data, or identify trends or patterns.


Without AI-powered insights, management teams can miss opportunities to proactively address potential issues, optimize processes, and personalize future interactions based on customer preferences. As a result, support teams take a reactive approach, waiting for issues to arise before taking action.


**How AI ticketing helps:** AI-generated insights can power intelligent workflows and enable management teams to discover new ways to optimize operations.


### 
**Inconsistent brand voice**


New agents might use different tones or [communication styles](https://www.zendesk.com/blog/the-communication-styles-customer-service-teams-need-to-know/) as they learn your brand voice. Without AI, delivering that consistent brand tone and voice may be a slower process. AI-powered tools help agents by providing suggested replies and enabling them to adjust the tone, resulting in faster responses that align with the brand personality.


**How AI ticketing helps:** Agents can also use [generative AI](https://www.zendesk.com/blog/generative-ai-guide/) tools, like Tone Shift from Zendesk, to adapt their messages and maintain a consistent brand voice.


### 
**        Unlock the power of AI ticketing with Zendesk AI**


          Welcome to the future of CX. See how businesses are scaling customer service operations with an intelligent system that’s easy to deploy.


[Check out Zendesk AI](https://www.zendesk.com/service/ai/)


## 
**Benefits of AI ticketing systems**


AI-powered tools aren’t just a passing fad—they’re becoming the standard in [customer service](https://www.zendesk.com/blog/customer-service-vs-customer-experience-heres-difference/). Here are a few key benefits businesses can enjoy with AI ticketing systems.


### 
**Reduce operational costs**


With AI, you can deliver high-quality customer service without increasing headcount. For example, instead of hiring more agents for the holiday season, you can use a bot to handle the surge of ticket requests. AI chatbots can also serve customers around the clock with [conversational AI](https://www.zendesk.com/blog/customers-really-feel-conversational-ai/), so businesses don’t need to staff a late-night support team.


Phone channels are more costly than digital channels because agents can only serve one customer at a time. However, by utilizing AI technology, you can offer support over digital channels, allowing agents to serve multiple customers simultaneously. This leads to lower operational costs.


### 
**Boost agent productivity and efficiency**


AI can handle repetitive tasks so support agents can focus on more important tickets that need a human touch. It efficiently sorts and routes tickets to the appropriate agents based on expertise, availability, and capacity. AI can also suggest knowledge base content to agents within the ticket to help them resolve issues quickly.


Plus, AI can offer helpful tips and suggestions, like prompts on what to say or how to navigate tricky situations. With AI, agents can respond faster and keep customers happy, all while staying on brand. For example, with Zendesk AI, support teams save an average of 45 seconds per ticket compared to manual triage.


### 
**Improve scalability**


AI systems allow businesses to meet higher support volumes and scale up or down as needed. For example, you can use an AI bot during busy seasons to handle the extra tickets rather than hiring a temporary support team. These bots keep ticket volumes manageable without sacrificing quality.


Additionally, AI-powered [knowledge management systems](https://www.zendesk.com/service/help-center/knowledge-management-system/) can help customers self-serve at scale. For example, a bot can surface help center articles when a customer is on a checkout page and may need assistance. You can also use generative AI to streamline content creation, helping you meet the growing demand for self-service content.


### 
**Increase customer satisfaction**


According to the [Zendesk Customer Experience Trends Report](https://cxtrends.zendesk.com/), 51 percent of consumers prefer interacting with bots over humans for immediate assistance. With the help of AI-powered bots, agents can focus on providing a personalized, immersive customer experience to meet consumer expectations.


[Ticketing systems](https://www.zendesk.com/service/ticketing-system/) with advanced AI capabilities, like Zendesk, facilitate faster customer service. With intelligent routing and triage, customer requests are quickly routed to the appropriate agents or departments based on customer needs, language, and sentiment. This quick and effective service can result in higher customer satisfaction.


### 
**Accelerate agent onboarding**


AI streamlines the agent onboarding process by suggesting similar tickets and summarizing ticket information, which helps new hires find solutions faster. Additionally, generative AI can instantly change their tone and expand their responses from a few words to complete messages, reducing the learning curve and accelerating onboarding time.


AI ticketing systems can source knowledge base articles and troubleshooting guides, ensuring new agents can immediately access resources and efficiently address customer queries. Plus, AI-powered Zendesk QA and [workforce management (WFM)](https://www.zendesk.com/internal-help-desk/workforce-management-software/) tools automate processes that help pinpoint [customer service training](https://www.zendesk.com/blog/customer-service-training-important/) and coaching opportunities.


### 
**Forecast staffing needs**


AI leverages historical data and predictive analytics to anticipate peak periods and forecast future ticket volumes. These real-time insights into agent workload and ticket volume enable managers and decision-makers to make data-driven decisions to proactively adjust staffing levels and reallocate resources to optimize customer support. With help from AI, managers can ensure agents are working where and when they’re needed most.


Implementing AI ticketing systems with WFM software, like Zendesk WFM, enables automated scheduling, forecasting, and reporting, all of which can optimize agent productivity and service speed. The WFM software provides insights into agent utilization and schedule adherence, facilitating quick adjustments for faster service. With AI forecasting, businesses can accurately predict staffing needs, while custom dashboards offer real-time visibility into agent activity and attendance. This helps you improve scheduling processes, analyze WFM data, and optimize support operations for scalability.


### 
**Improve quality assurance**


Automating the [quality assurance (QA)](https://www.zendesk.com/blog/customer-service-quality-assurance/) process ensures consistent service quality as businesses grow. By automating QA, businesses can identify conversations that must be reviewed for quality and significantly reduce manual admin work. This approach engages agents in continuous feedback and provides visibility into support quality, enabling consistently better answers and fostering agent improvement. With AI [issue tracking software](https://www.zendesk.com/blog/issue-tracker/) pinpointing problematic cases, businesses can prioritize areas that require attention.


## 
**Examples of AI ticketing system use cases**


AI ticketing systems are used by businesses of all sizes across various industries. Here are some industries that can significantly benefit from AI ticketing systems.


### 
**Travel and hospitality**


AI ticketing systems make the ticketing process smoother for customers in the travel and hospitality industry. AI-powered travel chatbots act as personal assistants, offering personalized recommendations and suggesting upgrades or add-ons based on customer preferences. Beyond simplifying the booking process, [AI-powered chatbots](https://www.zendesk.com/service/ai/travel-chatbots/) provide 24/7 support in multiple languages.


[Baleària](https://www.zendesk.com/customer/balearia/), a maritime transportation company, implemented a Zendesk travel chatbot to answer common customer questions, preventing ticket creation. This resulted in a 96 percent customer satisfaction (CSAT) score.


### 
**Healthcare**


The healthcare industry uses AI ticketing automation to simplify scheduling appointments, which can benefit patients and staff. AI chatbots can help patients book or change doctor’s visits anytime, day or night, freeing up administrative staff for other important tasks. Additionally, automated appointment reminders sent via text or email reduce missed appointments and improve clinic efficiency.


[Medline](https://www.zendesk.com/customer/medline-industries-l-p/), a medical product distributor, uses Zendesk to help agents manage more than 800,000 tickets per year across numerous channels. Medline deploys bots to increase employee productivity and plans to implement Zendesk Advanced AI to help existing agents be more thoughtful about problem-solving.


“The insights coming in through AI give us the chance to be better customer service agents and provide a better customer experience,” says Billy Abrams, executive vice president of distribution at Medline.


### 
**Education**


The education industry can use AI-powered tools to improve the teacher and student experience. AI chatbots can answer questions about courses, scheduling, and professor availability for students, simplifying the registration process and deflecting tickets from support teams. Bots can also analyze student data to suggest courses based on strengths, interests, and focus.


[Khan Academy](https://www.zendesk.com/customer/khan-academy/), an online education platform, leverages automated triage within the Zendesk ticketing system. This intelligent process helps teams prioritize support requests and assign the right agent to help. Thanks to Zendesk, Khan Academy has achieved a 92 percent customer satisfaction score.


### 
**Retail and e-commerce**


AI ticketing benefits shoppers as well as businesses in the retail and e-commerce industry. It enhances the shopping experience for the customer by making it more personalized and seamless. AI acts as a virtual assistant, helping shoppers find what they need and suggesting new items based on their preferences.


Businesses like [Grove Collaborative](https://www.zendesk.com/customer/grove/) use Zendesk AI for intelligent triage to streamline ticket requests. It captures request types and automatically assigns tickets to agents, eliminating the need for manual assignment and boosting efficiency.


### 
**Recruitment and human resources**


Businesses are using AI to improve recruitment, hiring, and onboarding processes. Smart assistants can automate communications, keeping candidates updated and informed throughout the process, improving their experience, and deflecting tickets to keep workflows manageable. Additionally, AI can be used as a workforce management tool to help manage staffing needs.


[Peek](https://www.tymeshift.com/customer-story/erik-jansen-peek) uses Zendesk WFM to ensure staffing resources are allocated properly for seasonal support. With help from Zendesk, manual processes that previously took four to five hours to complete now take just five minutes.


## 
**Frequently asked questions**


### 
**What are AI tickets?**


AI ticketing systems use artificial intelligence to streamline and improve different parts of the ticketing process. This involves automating repetitive tasks—such as ticket routing, categorization, and initial responses—and offering around-the-clock support via AI chatbots. AI handles routine, time-consuming tasks and boosts [agent efficiency](https://www.zendesk.com/blog/improve-agent-productivity-better-cx/), giving them more time to tackle complex issues.


### 
**Is AI-powered ticket automation suitable for all types of businesses?**


AI-powered ticket automation offers significant benefits for businesses of all sizes, including startups, small businesses, midsize companies, and large enterprises. Businesses with high ticket volumes may find AI ticketing the most beneficial, particularly in industries like customer service, IT support, education, HR, healthcare, retail, and e-commerce. To get the most from your system, it’s best to follow best practices and [ticketing system tips](https://www.zendesk.com/blog/ticketing-system-tips/).


### 
**Can AI ticket systems replace human agents?**


AI ticketing systems are meant to serve as intelligent assistants to human agents, not replace them. AI is better suited to handle repetitive tasks and provide 24/7 support, but it can also make suggestions to agents regarding the best course of action to resolve an issue. This enables agents to deliver faster and more [personalized customer service](https://www.zendesk.com/blog/start-providing-personalized-customer-service/).


### 
**How long does it take to implement AI ticket automation?**


The implementation time for AI ticketing systems varies based on factors like the complexity of your needs, data integration requirements, your [AI as a service (AIaaS)](https://www.zendesk.com/blog/ai-as-a-service/) vendor, and internal training. For example, Zendesk AI is accessible to all, sets up quickly, and doesn’t require developers or extensive IT investment. It comes pre-trained with billions of real customer service interaction datasets and integrates seamlessly into your customer service workflows, increasing productivity from day one.


### 
**Is AI-powered ticketing secure?**


Not all AI-powered ticketing systems come with security features in place to protect sensitive data and transactions, but Zendesk AI does. Zendesk offers security measures, including protocols for encryption, authentication, and access controls. Plus, AI algorithms are trained to detect and prevent fraudulent activities, making Zendesk a secure and reliable solution for ticket management.


However, businesses should regularly update their AI-powered systems, conduct security audits, and provide internal training to minimize potential security risks and vulnerabilities to ensure [data privacy](https://www.zendesk.com/blog/customer-data-privacy/).


## 
**Punch your ticket to a better CX**


AI-powered ticket automation can set your business up for success. With Zendesk AI, tickets are sorted and routed faster, customers receive personalized help, and businesses can resolve issues before they become significant problems. By using AI for ticketing, businesses can keep customers happy, help employees work better, and boost their overall success.


As more businesses benefit from [AI in customer service](https://www.zendesk.com/blog/ai-customer-service/), integrating an innovative technology like Zendesk AI into your help desk ticketing system is an intelligent choice to keep up with the competition, now and in the future.

</context_blog>
------------------------------------------------------------------------------------
<context_blog>
Blog title: Routing and automation options for incoming tickets
URL: https://support.zendesk.com/hc/en-us/articles/*************-Routing-and-automation-options-for-incoming-tickets
Content:
# 
**Routing and automation options for incoming tickets**


---


Follow


Follow


---



![alt_text](images/image1.jpg "image_tooltip")



**[Aimee Spanier](https://support.zendesk.com/hc/en-us/profiles/*************)**


Zendesk Documentation Team


Edited Mar 06, 2025


[What's my plan?](https://support.zendesk.com/hc/en-us/articles/*************-plan)



![alt_text](images/image2.png "image_tooltip")

![alt_text](images/image3.png "image_tooltip")



One of the best ways to increase agent efficiency and streamline your support tasks is to use routing options to manage your ticket workflows. Zendesk provides a number of business rules and routing options to make sure your tickets get to the right agent as quickly as possible. You can either configure a "push" routing model, which assigns tickets to agents, or a "pull" model, where agents assign work to themselves. This article explains these options and how they can be combined to meet your unique needs.


This article contains the following topics:



* [About the standard Zendesk ticket routing framework](https://support.zendesk.com/hc/en-us/articles/*************-Routing-and-automation-options-for-incoming-tickets#topic_wl4_cwk_wxb)
* [Understanding the routing models](https://support.zendesk.com/hc/en-us/articles/*************-Routing-and-automation-options-for-incoming-tickets#topic_fxc_1zk_wxb)
* [Using business rules and configuration options to automate ticket workflows and routing](https://support.zendesk.com/hc/en-us/articles/*************-Routing-and-automation-options-for-incoming-tickets#topic_pd3_d4n_cyb)

Before choosing which routing solutions or business rules to use, see [Best practices: Planning your routing workflow](https://support.zendesk.com/hc/en-us/articles/*************).


## 
About the standard Zendesk ticket routing framework


When you create an account, you can start receiving support requests right away using the default support email address created during the registration process. All support requests sent to your account, via email or another channel, automatically become tickets in your system. Zendesk provides standard [views](https://support.zendesk.com/hc/en-us/articles/*************#topic_gnx_2tm_vt) and [triggers](https://support.zendesk.com/hc/en-us/articles/*************) that work together to create a basic routing framework for tickets.


Without any configuration beyond the initial registration process, the standard triggers and views work together so that every new and updated ticket appears in at least one view and triggers at least one notification.


When a new ticket is submitted to your support address, it appears in the following standard views and could appear in others depending on your plan type and other agents on your account.

* Your unsolved tickets
* All unsolved tickets
* Recently updated tickets

When a ticket is created or updated, the following standard triggers fire:

* Notify requester and CCs of received request
* Notify all agents of received request

In this basic framework, agents can use the trigger notifications and views to assign tickets to themselves or tickets can be assigned to them manually.


## 
Understanding the routing models


In the simplest workflows, fine-tuning the standard views and triggers might provide sufficient routing. However, even simple workflows can benefit from more advanced routing logic. There are two types of routing models you can configure: "push" models, which assign tickets to agents, or a "pull" models, where agents assign work to themselves.


### 
Routing models


You can configure the following routing models:

* Push routing models:
    * **Omnichannel routing**: Route tickets from [email (including web form, side conversations, and API)](https://support.zendesk.com/hc/en-us/articles/*************#topic_psx_hxk_3yb), calls, and messaging conversations based on agent status and capacity. On Professional and Enterprise plans, you can also route tickets based on priority and skills. On all plans, you can run triggers against new tickets from all channels, and you can use triggers to set and manage skills on tickets. See [About omnichannel routing with unified agent status](https://support.zendesk.com/hc/en-us/articles/*************) and [Using skills with omnichannel routing](https://support.zendesk.com/hc/en-us/articles/*************#topic_d2l_bnx_txb).
    * **Round robin**: A way to assign tickets by rotating through your agents. Round robin ticket management ensures tickets are distributed evenly among agents, but doesn't consider ticket complexity or expertise requirements. You can configure omnichannel routing to assign based on round robin rather than spare capacity. See [Using round robin routing for email, messaging conversations, and call tickets](https://support.zendesk.com/hc/en-us/articles/*************).
    * **Manual ticket assignment**: Manually triage tickets and assign them to your agents. This works best for small teams with low-volume ticket queues. This routing model is best used in conjunction with [other business rules and configuration options](https://support.zendesk.com/hc/en-us/articles/*************-Routing-and-automation-options-for-incoming-tickets#topic_pd3_d4n_cyb).
* Pull routing models
    * **Play mode**: Play mode allows agents to click through a view using the play button and be assigned to the next available ticket automatically. Play mode can be restricted to only display tickets from a specific view and, on Enterprise plans, admins can configure [Guided mode](https://support.zendesk.com/hc/en-us/articles/*************) so agents are only allowed to access tickets using the Play button. See [Using the Play button](https://support.zendesk.com/hc/en-us/articles/*************-Working-with-tickets#topic_avj_hfg_vt) and [Using a Play button-centered workflow](https://support.zendesk.com/hc/en-us/community/posts/*********).
    * **Standalone skills-based routing**: Skills are agent attributes that determine an agent's suitability to work a ticket that requires them. Skills can be used as a standalone routing method, where agents use filtered views to assign tickets with matching skills to themselves, or as part of omnichannel routing (push method). [See About using skills to route tickets](https://support.zendesk.com/hc/en-us/articles/*************).
    * **Manually self-assigning tickets from views**: Allow agents to assign tickets directly to themselves from views. Letting agents choose their own work at their own pace can work well for some small teams, but can quickly become unmanageable.

### 
Deciding whether to use omnichannel routing


Omnichannel routing is Zendesk's most sophisticated and complete routing solution. It provides consistent routing logic across the email (including web form, side conversations, and API), Messaging, and Talk channels. Tickets are assigned to agents based on their [availability](https://support.zendesk.com/hc/en-us/articles/*************) and [capacity](https://support.zendesk.com/hc/en-us/articles/*************). On Professional plans and above, tickets can also be routed based on priority and [skills](https://support.zendesk.com/hc/en-us/articles/*************). Using omnichannel routing also means agents can set a single unified status for all channels rather than setting statuses for each channel separately.


When using omnichannel routing, tickets are created as soon as a request is received from any channel. This means triggers can be run on all channels, including Talk. Additionally, omnichannel routing enables you to use triggers to assign skills to tickets. This means skills can be automatically updated when tickets are created or updated, not just upon creation.


With omnichannel routing, you can also organize your tickets into [multiple queues](https://support.zendesk.com/hc/en-us/articles/*************). Creating additional queues allows you to use all of the omnichannel routing logic but route tickets to different groups of agents and even specify secondary (also known as fallback) groups for each queue.


So, is omnichannel routing the right routing solution for you? Consider the following:

* Omnichannel routing has some [requirements and limitations](https://support.zendesk.com/hc/en-us/articles/*************) to be aware of before choosing this routing solution.
* What Zendesk account plan do you have? See the [Summary of features by plan](https://support.zendesk.com/hc/en-us/articles/*************#topic_r5j_c3p_m5b) to ensure you'd have access to the functionality you want to use.
* The size of your organization. Omnichannel routing will provide increased efficiency at any scale, but especially for larger and more complex organizations.
* The [channels](https://support.zendesk.com/hc/en-us/articles/*************) through which you receive tickets. Even if you only use one of the channels supported by omnichannel routing, it can still be a great fit as your routing solution. However, it also means simpler routing solutions could also work well for you.
* The business rules you already have in place to help route tickets. Standard triggers and views provide a simple, but effective routing framework. You may have added new ones or modified the originals. Are those business rules accomplishing what you want? What, specifically, are you looking to address by considering alternative routing solutions?

After considering all of this, if you've decided to use omnichannel routing, see [About omnichannel routing](https://support.zendesk.com/hc/en-us/articles/*************), [Turning on omnichannel routing](https://support.zendesk.com/hc/en-us/articles/*************), and [Managing your omnichannel routing configuration](https://support.zendesk.com/hc/en-us/articles/*************).


If, on the other hand, you want to use routing solutions such as standalone skills-based routing, play mode, or some custom solution, revisit the list of [tools and configuration options](https://support.zendesk.com/hc/en-us/articles/*************-Routing-and-automation-options-for-incoming-tickets#topic_fxc_1zk_wxb) you can use.


## 
Using business rules and configuration options to automate ticket workflows and routing


The following business rules and configuration options can also be used to define and automate ticket routing behavior within Zendesk's routing solutions or in a custom configuration. Different tools are most helpful at different points in the ticket workflow, so they're organized accordingly:

* [Kicking off the ticket workflow](https://support.zendesk.com/hc/en-us/articles/*************-Routing-and-automation-options-for-incoming-tickets#topic_dcd_pvh_qbc)
* [Performing automated actions to keep things moving](https://support.zendesk.com/hc/en-us/articles/*************-Routing-and-automation-options-for-incoming-tickets#topic_h5j_dwh_qbc)
* [Monitoring your ticket workflows](https://support.zendesk.com/hc/en-us/articles/*************-Routing-and-automation-options-for-incoming-tickets#topic_azx_wyh_qbc)

Additionally, you can refer to [Recipes for automated ticket workflows](https://support.zendesk.com/hc/en-us/articles/*************-Routing-and-automation-options-for-incoming-tickets#topic_ym1_p13_qbc) to see some examples of how these business rules and configurations can be used together.


### 
Kicking off the ticket workflow


The following business rules and configuration options are commonly used early in the ticket workflow because they can be leveraged by other business rules later in the workflow.

* **Support addresses**: By default, you have one email address for users to submit tickets to. Using multiple Support email addresses provides a way to route tickets based on the email address the customer used to contact support. See [A complete guide to understanding email in Zendesk: How the email channel works](https://support.zendesk.com/hc/en-us/articles/*************) and [Adding support addresses for users to submit tickets](https://support.zendesk.com/hc/en-us/articles/*************).
* **Ticket forms**: A predefined set of ticket fields for support requests. Using multiple ticket forms provides a way to collect more specialized information from customers. This helps ensure agents have the information they need to assist the customer, but also makes it possible to route tickets based on the ticket form that the customer used. See [Creating multiple ticket forms](https://support.zendesk.com/hc/en-us/articles/4408846520858), [Presenting ticket forms to end users](https://support.zendesk.com/hc/en-us/articles/4408842873498), and [Adding custom fields to your tickets and support requests forms](https://support.zendesk.com/hc/en-us/articles/4408883152794). (Suite Growth and above or Support Enterprise)
* **Tags**: A word or phrase you can use to add more context to ticket and incorporate into business rule logic. You can also search for tickets by tags and use tags when configuring views. See [About tags](https://support.zendesk.com/hc/en-us/articles/4408888664474) and [Working with ticket tags](https://support.zendesk.com/hc/en-us/articles/4408835059482).
* **Channels**: The way you communicate with your customers and can be used as a condition in triggers to route tickets based on the channel through which it was received. See [About Zendesk channels](https://support.zendesk.com/hc/en-us/articles/*************).
* **Service Level Agreements (SLAs)**: An agreed upon measure of the response and resolution times that your support team delivers to your customer. SLAs are great stand-alone tools to help you attain your service goals. However, they can also be used as conditions in views and automations to reroute and prioritize tickets based on service promises made to customers. See [About SLA policies and how they work](https://support.zendesk.com/hc/en-us/articles/5600997516058) and [Defining SLA policies](https://support.zendesk.com/hc/en-us/articles/4408829459866). (Professional and Enterprise)
* **Custom ticket fields**: You can customize your ticket forms and web forms to require certain information from customers before a ticket is accepted into the queue or you can add custom ticket fields to the tickets themselves. Then you can use that information as conditions in business rules to route tickets. Information stored in custom ticket fields can also help agents have necessary context for tickets. Examples of how you can use custom ticket fields include: a *Language* field to automate routing to an agent who speaks that language or asset management fields such as *order number* or *product type* to help surface key information.
* **Copilot add-on**: A set of artificial intelligence features built to enhance and streamline the customer service experience. The Zendesk Advanced AI features include intelligent triage, generative AI for agents while working in tickets, and unlocks additional trigger actions to automatically reply in a ticket comment with article suggestions or a pre-defined response. See [About Zendesk Advanced AI](https://support.zendesk.com/hc/en-us/articles/*************).

### 
Performing automated actions to keep things moving


The following business rules are used to actually perform automated actions when tickets meet certain criteria. You can define ticket criteria based on the business rules and configuration options listed under [Kicking off the ticket workflow](https://support.zendesk.com/hc/en-us/articles/*************-Routing-and-automation-options-for-incoming-tickets#topic_dcd_pvh_qbc).

* **Triggers**: Event-based business rules that perform actions any time a ticket is created or updated. They can send notifications and modify ticket properties. See [About Zendesk triggers and how they work](https://support.zendesk.com/hc/en-us/articles/*************), [About the standard ticket triggers](https://support.zendesk.com/hc/en-us/articles/*************), and Creating ticket triggers.
* **Automations**: Time-based business rules that perform actions based on time elapsed. Similar to triggers, they can send notifications and modify ticket properties. See [About automations and how they work](https://support.zendesk.com/hc/en-us/articles/*************) and [About the standard Support automations](https://support.zendesk.com/hc/en-us/articles/*************).
* **Macros**: Predefined set of actions that agents apply to a ticket with one click. Macros can be used to automatically fill in ticket fields and provide consistent responses, saving your agents time and effort for common questions and scenarios.

### 
Monitoring your ticket workflows


The following business rules and configuration options can help you monitor the ticket workflow and ensure tickets keep moving through it.

* **Views**: A way to organize tickets based on certain criteria. You can think of them as containers for your tickets. A single ticket can appear in multiple views or no views at all. When you begin using Support, every ticket appears in at least one [standard view](https://support.zendesk.com/hc/en-us/articles/*************#topic_gnx_2tm_vt). You can also define targeted views, which allows agents to self-assign tickets from a pre-filtered set of tickets in their view. See [Creating views to build customized lists of tickets](https://support.zendesk.com/hc/en-us/articles/*************).
* **Explore reporting**: [Explore](https://support.zendesk.com/hc/en-us/articles/*************) data sets now include a lot of information about channel, routing, and agent capacity and availability. See [Analyzing your Support ticket activity and agent performance](https://support.zendesk.com/hc/en-us/articles/*************).

### 
Recipes for automated ticket workflows and routing


These articles provide some examples of how you can use these Zendesk business rules and configuration options together to automate ticket workflows and routing:

* [Streamlining your Support workflow](https://support.zendesk.com/hc/en-us/articles/*************)
* [Workflow: Using omnichannel routing to achieve first reply time SLAs](https://support.zendesk.com/hc/en-us/articles/*************)
* [Workflow: Using skills to route calls to specific agents with omnichannel routing](https://support.zendesk.com/hc/en-us/articles/*************)
* [Workflow recipe: Manage outages using SLA policies](https://support.zendesk.com/hc/en-us/articles/*************)
* [Workflow recipe: Using triggers to manage requests from important customers](https://support.zendesk.com/hc/en-us/articles/*************)
* [Workflow recipe: Funneling orders through Zendesk Support](https://support.zendesk.com/hc/en-us/articles/*************)
* [Workflow recipe: Creating an approval process](https://support.zendesk.com/hc/en-us/articles/*************)
* [Workflow recipe: Sending automated ticket reminders to customers](https://support.zendesk.com/hc/en-us/articles/*************-Sending-automated-ticket-reminders-to-customers)


---

</context_blog>
------------------------------------------------------------------------------------
<context_blog>
Blog title: Creating and managing automations for time-based events
URL: https://support.zendesk.com/hc/en-us/articles/*************-Creating-and-managing-automations-for-time-based-events
Content:
# 
**Creating and managing automations for time-based events**


---


Follow


Follow


---


**[Anton de Young](https://support.zendesk.com/hc/en-us/profiles/1263082071689)**


Edited Jun 22, 2024


[What's my plan?](https://support.zendesk.com/hc/en-us/articles/*************-plan)



![alt_text](images/image1.png "image_tooltip")

![alt_text](images/image2.png "image_tooltip")



 **Quick Look: **Admin Center > Objects and rules > Business rules > Automations


Automations are similar to triggers because both define conditions and actions that modify ticket properties and optionally send email notifications to customers and the support staff. Where they differ is that automations execute when a time event occurs after a ticket property was set or updated, rather than immediately after a ticket is created or updated.


Administrators can create, edit, and manage default and custom automations.


Topics covered in this article:



* [Creating automations](https://support.zendesk.com/hc/en-us/articles/*************-Creating-and-managing-automations-for-time-based-events#topic_adj_pzy_tb)
* [Editing and cloning automations](https://support.zendesk.com/hc/en-us/articles/*************-Creating-and-managing-automations-for-time-based-events#topic_rsh_miv_ub)
* [Reordering your automations](https://support.zendesk.com/hc/en-us/articles/*************-Creating-and-managing-automations-for-time-based-events#topic_agt_ojv_ub)
* [Deleting and deactivating automations](https://support.zendesk.com/hc/en-us/articles/*************-Creating-and-managing-automations-for-time-based-events#topic_wsq_xjv_ub)

More information:

* [About automations and how they work](https://support.zendesk.com/hc/en-us/articles/*************)
* [About the standard Support automations](https://support.zendesk.com/hc/en-us/articles/*************)
* [Automation conditions and actions reference](https://support.zendesk.com/hc/en-us/articles/4408885654298)

**Tip:** To learn more about setting up automations, check out the [On demand: Automations](https://training.zendesk.com/on-demand-automations) training video.


## 
Creating automations


Administrators can create automations from scratch, as shown here, or create copies of existing automations to modify and use (see [Editing and cloning automations](https://support.zendesk.com/hc/en-us/articles/*************-Creating-and-managing-automations-for-time-based-events#topic_rsh_miv_ub)).


Before creating automations, review the [Essential facts for automations](https://support.zendesk.com/hc/en-us/articles/*************#topic_ft5_n3z_sm). You can have up to 500 active automations at a time. Each automation must be less than 65kb.


**To add an automation**

1. In [Admin Center](https://support.zendesk.com/hc/en-us/articles/4581766374554#topic_hfg_dyz_1hb), click 
![alt_text](images/image3.png "image_tooltip")
 **Objects and rules** in the sidebar, then select **Business rules > Automations**.
2. Select **Add Automation**.
3. Enter a title for your automation.
4. Add the conditions and actions for your automation (see [Automation conditions and actions reference](https://support.zendesk.com/hc/en-us/articles/4408885654298-Automation-conditions-and-actions-reference)). \
An automation is made up of three parts:
    * Conditions to be met for the automation to run
    * Actions to perform when the conditions are met
    * At least one of the following: one action that cancels a condition after the conditions are met or a condition than can only be true once (see [Ensuring your automation only runs once](https://support.zendesk.com/hc/en-us/articles/*************#topic_mbl_q4f_tm))
5. Test your automation by clicking **Preview match for the conditions** to preview tickets that match the conditions you specified. \
This lists the tickets that match the conditions you specified. The automation will have no effect on any closed ticket listed in your preview. For more information, see [Understanding when automations run](https://support.zendesk.com/hc/en-us/articles/*************#topic_h3z_svn_fm).
6. Save your new automation by clicking **Create automation**.

## 
Editing and cloning automations


You can edit and clone automations. Cloning an automation creates a copy that you can modify and use for some other purpose.


**To edit an automation**

1. In [Admin Center](https://support.zendesk.com/hc/en-us/articles/4581766374554#topic_hfg_dyz_1hb), click 
![alt_text](images/image4.png "image_tooltip")
 **Objects and rules** in the sidebar, then select **Business rules > Automations**.
2. Locate the automation you want to edit.
3. Hover your mouse over the automation to display the options menu icon (
![alt_text](images/image5.png "image_tooltip")
), then click the icon and select **Edit** from the options menu.
4. Modify the title, conditions, and actions as needed.
5. Select **Update** and **Submit** your changes.

You can create a copy of an existing automation to use as the basis of a new automation.


**To clone an automation**

1. In [Admin Center](https://support.zendesk.com/hc/en-us/articles/4581766374554#topic_hfg_dyz_1hb), click 
![alt_text](images/image6.png "image_tooltip")
 **Objects and rules** in the sidebar, then select **Business rules > Automations**.
2. Locate the automation you want to clone.
3. Hover your mouse over the automation to display the options menu icon (
![alt_text](images/image7.png "image_tooltip")
), then click the icon and select **Clone** from the options menu.
4. Enter a new name for your automation and modify the conditions and actions as needed. Note that all active automations can have some overlapping conditions, but they can't be identical.
5. Click **Create automation**.

## 
Reordering your automations


You can reorder your automations, but keep in mind that the order of your automations is important because all automations run (first to last) every hour. Actions in one automation may affect the actions in another.


**To reorder the list of automations**

1. In [Admin Center](https://support.zendesk.com/hc/en-us/articles/4581766374554#topic_hfg_dyz_1hb), click 
![alt_text](images/image8.png "image_tooltip")
 **Objects and rules** in the sidebar, then select **Business rules > Automations**.
2. Open the options menu icon (
![alt_text](images/image9.png "image_tooltip")
 ) at the top of the list of active automations, then click **Reorder page**.
3. Click and drag automations to new locations as needed.
4. Click **Save**.

## 
Deleting and deactivating automations


If you decide that you no longer need an automation you can either delete it or deactivate it. Deleting it of course means that it’s gone and can’t be retrieved. You can instead deactivate automations. Deactivated automations are listed in a separate table on the **Automations** page and can be reactivated if needed.


**To delete an automation**

1. In [Admin Center](https://support.zendesk.com/hc/en-us/articles/4581766374554#topic_hfg_dyz_1hb), click 
![alt_text](images/image10.png "image_tooltip")
 **Objects and rules** in the sidebar, then select **Business rules > Automations**.
2. Locate the automation you want to delete.
3. Hover your mouse over the automation to display the options menu icon (
![alt_text](images/image11.png "image_tooltip")
), then click the icon and select **Edit** from the options menu.
4. Choose **Delete** from the actions menu at the bottom of the page, then click **Submit**. \

![alt_text](images/image12.png "image_tooltip")


**To deactivate/activate an automation**

1. In [Admin Center](https://support.zendesk.com/hc/en-us/articles/4581766374554#topic_hfg_dyz_1hb), click 
![alt_text](images/image13.png "image_tooltip")
 **Objects and rules** in the sidebar, then select **Business rules > Automations**.
2. Locate the automation you want to deactivate.
3. Hover your mouse over the automation to display the options menu icon (
![alt_text](images/image14.png "image_tooltip")
), then click the icon and select **Deactivate** from the options menu. \
The automation is deactivated and displayed in the list of inactive automations.
4. To reactivate the automation, select it from the list of inactive automations and select **Activate** from the options menu.


---
</context_blog>
------------------------------------------------------------------------------------
<context_blog>
Blog title: How to Use Zendesk Automation to Manage Your Business Efficiently
URL: https://getadelante.com/blog/how-to-use-zendesk-automation-to-manage-your-business-efficiently
Content:
# **How to Use Zendesk Automation to Manage Your Business Efficiently**

In simple terms, Zendesk automations are sets of rules that are triggered by specific events, such as a new ticket or a project due date. Zendesk provides you with many tools to create automations that can help your team save time and improve productivity. While Zendesk automations have many uses, they typically fall into two categories: single-action and multi-action automations. Single-action automations take one action, such as sending a welcome email or creating a ticket. Zendesk multi-action automations can take multiple actions, for example creating a new ticket, sending a notification email, then updating the ticket.

Adelante

May 13, 2023


![alt_text](images/image1.jpg "image_tooltip")


Zendesk is a comprehensive customer support platform that offers businesses a wide range of features. Apart from assisting customers,[ Zendesk](https://www.zendesk.com/) helps companies manage their tickets and inquiries more efficiently.

To accomplish this, many businesses use Zendesk automation tools to make processes simpler and faster. With the help of automation, you can respond to your customers faster and create an automated workflow that meets their needs. Read on to find out how!


## **Section 1: What is Zendesk Automation?**

In simple terms, Zendesk automations are sets of rules that are triggered by specific events, such as a new ticket or a project due date. Zendesk provides you with many tools to create automations that can help your team save time and improve productivity. While Zendesk automations have many uses, they typically fall into two categories: single-action and multi-action automations. Single-action automations take one action, such as sending a welcome email or creating a ticket. Zendesk multi-action automations can take multiple actions, for example creating a new ticket, sending a notification email, then updating the ticket.


## **Section 2: Set up triggers to automate your most frequent tasks**

One of the best ways to get started with Zendesk automation is to create simple triggers that solve your most frequent problems. Automating your most frequent tasks is a great way to get the most out of Zendesk. For example, let’s assume that you receive many support tickets from customers who can’t log in to the app. You can set a trigger so that whenever a new ticket is created with the subject “Login Error,” a Zendesk agent is notified with a prewritten message. The message should include a link to the login error article that you’ve previously written. This way, your team can quickly resolve the issue without making the customer wait.


## **Section 3: Create automatic responses for common tickets**

Customer satisfaction surveys reveal that the most common customer request is to receive faster support. Automation can help you speed up the ticket resolution process. Follow these steps to create automatic responses for recurring questions:



- [ ] First, identify the questions that you’re commonly asked.
- [ ] Next, create an automatic response for each of these questions.
- [ ] Finally, target your automatic response to specific tickets based on keywords.

Let’s say that you frequently receive tickets from customers who want to upgrade their account. You can target your automatic response to these tickets by adding “upgrade account” as a keyword. Then, create a message that states that a representative will get in touch with them shortly to discuss their upgrade, and set a notification to the next available agent to call the customer.


## **Section 4: Create an automated workflow with Zendesk tools**

Though you can automate many tasks with single-action automations, Zendesk tools can help you create multi-action automations that are more complex.

These tools are designed to help you automate your workflow and take your business to the next level. For example:



- [ ] Surfacing self-service – You can use Zendesk email to create templates for emails and automate these messages. For example, let’s say that you want to let customers know that you have self-service options for their questions. You can create a trigger to detect the request subject, and then create a template for this message and use Zendesk to quickly send it to customers.
- [ ] Project management – You can use Zendesk project management add-ons to create tasks and assign them to your team members. This way, you know who is working on what and can track all projects in one place.


## **Section 5: Conclusion**

Zendesk automation can help you improve your business in many ways. It can help you save time by streamlining customer service and ticket management, and it can also help you increase customer satisfaction by responding to tickets more quickly. To get the most out of your Zendesk automation, set up triggers for common problems and create automatic responses for frequent inquiries. You can also use Zendesk tools to create more complex automations. With[ Zendesk automation](https://www.getadelante.com/zendesk-reignite/), you can respond to customers faster, create an automated workflow that saves your team time, and improve customer satisfaction.

</context_blog>
------------------------------------------------------------------------------------
<context_blog>
Blog title: How To Automate Tickets on Zendesk (for Free)
URL: https://www.nextmatter.com/blog/automate-zendesk
Content:
# 
            **How To Automate Tickets on Zendesk (for Free)**


            Pablo Garcia


            April 22, 2024


            Updated:


            April 22, 2024


            9


            minutes

Zendesk is a behemoth of a CRM. It’s a robust platform for managing customer interactions that provides a myriad of features, which in turn creates *many *opportunities for automation.

As your customers increase over time, the volume of Zendesk support tickets you receive grows too. Automating those tickets is a game changer for keeping your customer success agents efficient and motivated, instead of stretched thin and with zero bandwidth.

This guide helps you automate most Zendesk support tickets to handle more requests without affecting customer satisfaction. It even covers how to automate [complex support tickets](https://www.nextmatter.com/blog/complexity-poor-customer-service) and escalations, with the help of Next Matter.


## **What can you automate on Zendesk?**

For this article, we’ll focus on a real-world example: a simple ticket, like a customer that wants to check their order status, which could steal valuable time from your CS agents if it happens at scale.

*Suggested macros allow agents to run macros that have successfully solved similar tickets in the past. Source: [Zendesk](https://support.zendesk.com/hc/en-us/articles/4408826078362-Using-suggested-macros)*

Later on, you’ll learn how to automate a more intricate example: a complex ticket, like a customer who wants to return a product they bought.

To handle both tickets, you’ll work on several automations that leverage most of Zendesk and Zendesk AI features, plus the [Next Matter integration for Zendesk](https://www.nextmatter.com/integrations/zendesk):



1. **Zendesk Guide**, a help center that the customer can use to self-serve their tickets.
2. **Zendesk automations**, which are time-based and can help you track tickets until they’re resolved.
3. **Zendesk triggers**, which are condition-based and can notify the customer whenever a ticket is updated.
4. **Zendesk Bot Builder**, the tool to customize a chatbot that offers quick replies and articles to deflect tickets.
5. **Zendesk email auto-replies**, which are an email-based approach to quick replies and self-serve articles.
6. **Suggested macros**, which can help your CS agents learn from previous actions that have led to solving tickets.
7. For the more complex ticket, we’ll use the **Next Matter sidebar app**, which helps you quickly fire a workflow from any ticket to route it to any needed team member, then track and resolve it.


## **Here’s what you will need**

The goal of this guide is to automate most of your simple Zendesk tickets for free, so to start off you’ll only need a **Zendesk account with any plan**. 

As extra steps, you’ll learn how to automate complex tickets with [Next Matter ](https://app.nextmatter.com/signup)(which you can [try with a demo](https://www.nextmatter.com/try-next-matter)).

You’ll also find extra automation suggestions that require the **Advanced AI add-on for Zendesk** (which is $50 per month/agent) or [early access to the Zendesk GenAI features](https://support.zendesk.com/hc/en-us/articles/*************-Zendesk-Generative-AI-EAP-capabilities-overview) (which is free but only lets you use the features while they’re part of the program). 

*Installing the Advanced AI add-on. Source: [Zendesk](https://support.zendesk.com/hc/en-us/articles/*************-About-Zendesk-Advanced-AI)*


## **How to automate Zendesk and deflect simple tickets (for free)**

Here's how to automate and self-serve several types of simple tickets:


### **1. Set up your Zendesk help center to self-serve tickets**

***Doesn’t require the Advanced AI add-on***

Although completing this task mainly involves manual work, setting up your help center is a crucial step in providing self-service support for your users.

*The settings page of the Guide help center. Source: [Zendesk](https://support.zendesk.com/hc/en-us/articles/4408823954970-Welcome-to-Guide-for-help-center)*

To enable Help Center, navigate to Zendesk Products > Guide > Get Started. Your Help Center is structured into articles, sections, and categories, and you can customize it to apply your branding.

Then, you can begin creating content and publishing it.


### **2. Adjust the standard Zendesk automations**

***Doesn’t require the Advanced AI add-on***

Although we will go beyond them, Zendesk offers some native automations that help your CS team maintain and track SLAs for transactional support requests (like a change of address or account lockouts).

You can enable these by navigating to Admin Center, clicking Objects and Rules, then Business Rules > Automations.

*Source: [Zendesk Automations](https://support.zendesk.com/hc/en-us/articles/*************-About-automations-and-how-they-work)*

Make sure you enable these automations and adjust them if needed:



*  “Close ticket 4 days after it’s solved”: this automaton closes a ticket four days after its status is set to solved. You can change the amount of time to more or less than 4 days.
* “Pending notification”: this automation is disabled by default, and it sends a notification to the user if its ticket is pending for over 24 hours or 5 days. You can adjust the email notification to let them know that the ticket is pending or requires an extra step, like the user providing feedback.
* “Request customer satisfaction rating”: this automation sends the customer satisfaction survey after a ticket is closed. It’s only available for Suite Growth plans and above.


### **3. Set up the built-in Zendesk triggers**

***Doesn’t require the Advanced AI add-on***

Triggers are automations that execute actions when a ticket meets specific conditions. You can use them to notify customers, route tickets, manage SLAs, and more.

You can create a ticket by navigating to Admin Center, then clicking Objects and Rules > Business rules > Triggers > Add triggers.

When adding a trigger, you will have to define the conditions under which it will fire, and the actions that will be triggered. For example, you can use triggers to notify customers when their ticket is updated or to direct a ticket to the appropriate team member based on conditions like a tag or a subject.


### **4. Build a conversation bot to deflect simple tickets**

***Doesn’t require the Advanced AI add-on***

You can use the bot builder to customize a chatbot that guides customers to a resolution. Chatbots can be used to welcome customers, gather data, route conversations, notify of updates, and more.

*The bot builder allows you to design conversation flows for your bot. Source: [Zendesk](https://support.zendesk.com/hc/en-us/articles/4408838909210-About-the-bot-builder)*

You can create answers and decision trees for your bot using the bot builder. I suggest you check the Zendesk help page for [Creating your own answers](https://support.zendesk.com/hc/en-us/articles/4422584657434-Designing-a-conversation-bot-using-answers#topic_msr_n4p_qtb) and using answer templates.

You can add multiple step types to your flow, and each step represents a message that the bot sends to the customer.

Here’s a list of the steps you can use in the builder, with a link to the Zendesk help page on configuring each step:

1.[ Send a message with a piece of text](https://support.zendesk.com/hc/en-us/articles/4408836323738-Understanding-answer-step-types#topic_iqz_fwc_k4b)

2.[ Present up to 10 quick replies as options for the user to interact‍](https://support.zendesk.com/hc/en-us/articles/4408836323738-Understanding-answer-step-types#topic_mnf_gwc_k4b)

3.[ Show up to 6 help center articles to the customer‍](https://support.zendesk.com/hc/en-us/articles/4408836323738-Understanding-answer-step-types#topic_grj_gwc_k4b)

4.[ Add a carousel with up to 10 informational panels](https://support.zendesk.com/hc/en-us/articles/4408836323738-Understanding-answer-step-types#topic_il3_pmj_tvb)

5.[ Ask for details and add the information to the generated ticket](https://support.zendesk.com/hc/en-us/articles/4408836323738-Understanding-answer-step-types#topic_i5r_grz_n5b)

6.[ Ask if a question has been resolved‍](https://support.zendesk.com/hc/en-us/articles/4408836323738-Understanding-answer-step-types#topic_n2v_p23_q5b)

7.[ Make an API call](https://support.zendesk.com/hc/en-us/articles/4408836323738-Understanding-answer-step-types#topic_d4z_fwm_2tb)

8.[ Escalate the conversation to an agent](https://support.zendesk.com/hc/en-us/articles/4408836323738-Understanding-answer-step-types#topic_zqr_gwc_k4b) (or use Next Matter to trigger a workflow)[‍](https://support.zendesk.com/hc/en-us/articles/4408836323738-Understanding-answer-step-types#topic_hpg_wqm_gwb)

9.[ Add conditional logic to the answer flow](https://support.zendesk.com/hc/en-us/articles/4408836323738-Understanding-answer-step-types#topic_hpg_wqm_gwb)

10.[ Use your business hours as a condition for the answer flow‍](https://support.zendesk.com/hc/en-us/articles/4408836323738-Understanding-answer-step-types#topic_sxz_322_sqb)

11.[ Link to another answer in the same bot](https://support.zendesk.com/hc/en-us/articles/4408836323738-Understanding-answer-step-types#topic_bq2_232_d1c)


### **5. Enable email auto-replies to suggest articles and self-serve tickets**

***Doesn’t require the Advanced AI add-on***

All Zendesk plans also give you the option to send email auto-replies that suggest articles for solving a ticket.

*Source: [Zendesk](https://support.zendesk.com/hc/en-us/articles/4408825385242-Configuring-email-autoreplies-to-deflect-requests)*

You can build an autoreply by [creating a trigger for automatic ticket updates](https://support.zendesk.com/hc/en-us/articles/4408886797466), then configuring:



* A trigger condition: for example, launch the trigger as soon as a user submits a ticket
* A trigger action: you can choose Autoreply with articles, which will allow you to create an email (with subject and body) where you can use placeholders that include suggested articles.

Some of the placeholders you can use are {{autoreply.article_list}} (which uses AI to show three articles that match the ticket request) and {{autoreply.first_article_body}} (which displays the entire body of the first matching article within the email). You can see more placeholders on this [Zendesk help page](https://support.zendesk.com/hc/en-us/articles/4408825385242-Configuring-email-autoreplies-to-deflect-requests).


### **6. Use suggested macros for CS agents dealing with simple tickets**

***Only available for plans Professional and above***

Macros are standard responses and actions that your agents can use repetitively to solve support requests. You can create macros for tasks like changing the assignee, adding followers, updating ticket fields, or adding attachments to tickets. See the [Zendesk help page on creating macros](https://support.zendesk.com/hc/en-us/articles/4408844187034-Creating-macros-for-repetitive-ticket-responses-and-actions).

*Source: [Zendesk](https://support.zendesk.com/hc/en-us/articles/4685355428250-Viewing-intelligent-triage-predictions-and-suggested-macros)*

As part of Zendesk AI, you can enable suggested macros and see actions that match the ticket you’re managing. As you select the suggestions that Zendesk offers, machine learning will improve the quality of those suggestions over time.


## **Automating complex tickets & escalations (with Next Matter)**

***Doesn’t require the Advanced AI add-on***

If we move from a simple ticket, like checking the status of an order, to a complex one, like returning a product, automation becomes increasingly complex too.

To define what a *complex *ticket is, you can look at the criteria we use for [complex processes](https://www.nextmatter.com/blog/complexity-poor-customer-service): applied to this example, a product return can require input from multiple team members, an integration with a CRM or ecommerce system, several communication stages, and 3rd-party suppliers and couriers.

*Source: our guide to [complexity as the main factor behind poor customer experience](https://www.nextmatter.com/blog/complexity-poor-customer-service)*

To automate these complex tickets that involve multiple stakeholders, you will need to use Next Matter. You can [request a demo here](https://www.nextmatter.com/try-next-matter).

‍

Once your account is set up, visit the Zendesk Marketplace to get the Next Matter sidebar app. Then, in your Next Matter portal, navigate to Company > Integrations > Connect to Zendesk. See this guide to [connect to Zendesk using OAuth](https://help.nextmatter.com/docs/integrate-with-zendesk), and after that, you will see the Next Matter sidebar when you open Zendesk.

Next, you have to build the actual Next Matter workflow that you’ll trigger from Zendesk. If you want to automate product returns, then you need to create a workflow with all steps up to the ticket resolution.

You can use a guide like [Building your first Next Matter workflow](https://help.nextmatter.com/docs/build-your-first-process), but we’ll go with a much easier approach: some workflows, like a [product return](https://www.nextmatter.com/use-cases/product-return-replacement-and-or-refund?_gl=1*8zr3mb*_ga******************************_ga_PD5B2YL4M7********************************************.), are already built in the [automations library](https://app.nextmatter.com/app/automations-library/use-cases/all), so you simply click on Use template.

Now, it’s time to make a ticket trigger that automated workflow. You can use the Next Matter sidebar widget when viewing a ticket: search for the workflow you’d like to start (product return), press Link to run the workflow for this specific ticket, and then press Open to see the actual workflow in Next Matter.

Now, it’s time to start a workflow instance from a Zendesk ticket. To do this, make sure that the first step in the workflow (on Next Matter) contains a form field where you can write the Zendesk ticket ID.

After this, you can use Next Matter to fully track and resolve the ticket:


## **Extra steps for Advanced AI add-on users**

There are a few interesting automation tools within the [Advanced AI add-on](https://support.zendesk.com/hc/en-us/articles/*************-About-Zendesk-Advanced-AI), which expands the features already built within Zendesk AI.

The add-on is priced at $50 per agent/month. You can only buy it if your plan is Suite, Support Professional, or above. Let’s look at the features it includes:


### **7. Automatically triage tickets**

Intelligent Triage is perhaps the most exciting feature within the Advanced AI add-on. It uses AI to predict customer intent, language, and sentiment from your tickets and comments.

To start using Intelligent Triage, navigate to Objects and rules within the Admin center, then Business rules > Intelligent Triage.

*Source: [Zendesk](https://support.zendesk.com/hc/en-us/articles/*************-About-intelligent-triage-and-Intelligence-in-the-context-panel)*

After enabling this feature, you will see new fields within all your tickets. Here’s what AI predicts:



1. The language in which the ticket is written
2. The sentiment (how the customer feels) for the ticket, ranging from Very Positive to Very Negative
3. The intent (what the ticket is about), which can belong to taxonomies like Account, Billing, Finance, Misc, Order, Sell, Service, Software, and more.

*Source: [Zendesk](https://support.zendesk.com/hc/en-us/articles/*************-Viewing-and-managing-intelligent-triage-predictions)*

On top of this, you also see the level of confidence for the values in the three fields above.

Intelligent triage opens up lots of possibilities. For example, you could combine this with a trigger that starts a Next Matter workflow if a Very Negative ticket comes up for a customer that you identify as a key account, routing it directly to a senior CS agent.


### **8. Enable Advanced autoreplies**

If you have enabled intelligent triage, you can also set up advanced autoreplies. These autoreplies differ from the standard ones (which only provide predefined replies) in that they can deliver fully customized responses based on the predicted language, intent, and sentiment.

You can use advanced autoreplies in combination with triggers, which will now display the Autoreply action. You will have to select some conditions (e.g., deliver a specific autoreply for Very Negative sentiment and Account intent) and write the reply message yourself.


## **Extra steps for Early Access Program (EAP) users**

If you join the [Zendesk Generative AI EAP](https://support.zendesk.com/hc/en-us/articles/*************-Zendesk-Generative-AI-EAP-capabilities-overview), you get access to message and call summaries. This interesting feature will become available only within the Advanced AI add-on as soon as it leaves the EAP program, so I suggest you try it.

With the GenAI features, you can summarize, expand, or enhance the comments that have been added to a ticket, which helps you in several ways:



* Summarizing a ticket helps your agents handle it with speed. After joining the EAP, you simply have to click Intelligence within the sidebar for a ticket, then Summarize conversation.

*Source: [Zendesk](https://support.zendesk.com/hc/en-us/articles/*************-About-intelligent-triage-and-Intelligence-in-the-context-panel)*



* Expanding or enhancing are available within the composer when you’re replying to a comment. You can either develop your own prompt (and turn it into a full message) or make it friendlier or more formal.

*Source: [Zendesk](https://support.zendesk.com/hc/en-us/articles/5608712782362-Using-generative-AI-to-summarize-and-enhance-ticket-comments)*

Another interesting feature within the GenAI EAP is call summaries. You can enable them by going to Settings within your Talk channel, then enabling Transcribe and summarize calls using Generative AI.

After this, all recorded calls will display summaries and transcripts as internal notes for your agents to see.

*Source: [Zendesk](https://support.zendesk.com/hc/en-us/articles/6170157307162-Using-generative-AI-to-create-call-summaries-EAP?utm_campaign=NM%20Social%20Media&utm_medium=email&_hsmi=2&utm_content=2&utm_source=hs_email)*


## **Final Thoughts**

Zendesk is a giant help desk. As a customer support suite, it provides a versatility that would require 10+ individual tools to achieve just the same.

The downside of having such a large offering is that the learning curve to master the toolkit (and optimize it with automations) can be daunting. This article covers the basic opportunities for automating that make the most impact on your CX operations.

Of course, some complex tickets need third-party apps to be fully automated. If you need [only one app for Zendesk](https://www.zendesk.com/marketplace/apps/support/992085/next-matter/), then Next Matter can do the heavy lifting for turning your tickets into automated workflows that engage different teams and integrations.

</context_blog>
------------------------------------------------------------------------------------
<context_blog>
Blog title: How To Set Up Zendesk Automatic Ticket Creation
URL: https://saufter.io/how-to-set-up-zendesk-automatic-ticket-creation/
Content:
# **How To Set Up Zendesk Automatic Ticket Creation**


#### **Table of Contents**



- [ ] [Benefits of Zendesk Automatic Ticket Creation](https://saufter.io/how-to-set-up-zendesk-automatic-ticket-creation/#benefits-zendesk)
- [ ] [Easy Steps to set up Zendesk automatic ticket creation](https://saufter.io/how-to-set-up-zendesk-automatic-ticket-creation/#easy-set-up)
    - [ ] [Automatic Ticket creation through email](https://saufter.io/how-to-set-up-zendesk-automatic-ticket-creation/#elementor-toc__heading-anchor-2)
    - [ ] [Enabling automatic ticket creation for your Gmail inbox](https://saufter.io/how-to-set-up-zendesk-automatic-ticket-creation/#elementor-toc__heading-anchor-3)
    - [ ] [Automatic Ticket creation through Zendesk Chat](https://saufter.io/how-to-set-up-zendesk-automatic-ticket-creation/#elementor-toc__heading-anchor-4)
    - [ ] [Automatic ticketing through social media channels](https://saufter.io/how-to-set-up-zendesk-automatic-ticket-creation/#elementor-toc__heading-anchor-5)
- [ ] [Why Zendesk is not best for automatic ticket creation](https://saufter.io/how-to-set-up-zendesk-automatic-ticket-creation/#why-zendesk-not-best)
    - [ ] [Some of the unhappy users’ reviews:](https://saufter.io/how-to-set-up-zendesk-automatic-ticket-creation/#elementor-toc__heading-anchor-7)
- [ ] [Meet Saufter, the best Zendesk automatic ticket creation alternative!](https://saufter.io/how-to-set-up-zendesk-automatic-ticket-creation/#helplama-helpdesk)
- [ ] [Conclusion](https://saufter.io/how-to-set-up-zendesk-automatic-ticket-creation/#elementor-toc__heading-anchor-9)

    Audio Player


    **00:00**


    **00:00**


    Use Up/Down Arrow keys to increase or decrease volume.


Last Updated: March 2025

Are you looking for how to enable automatic ticket creation using Zendesk? Hop on, we are discussing exactly the same here today.

Firstly, why do you need an automated ticket creation setup? An automated ticket creation system can help speed up your customer support process through its AI-enabled tools and features. You can enable automated ticket creation on Zendesk Chat, Zendesk Talk, Zendesk support email, etc with Zendesk.

For this, you need to configure the respective Zendesk tool accordingly. Once done, it starts to create tickets automatically whenever customers reach out over email, chat, phone, or any web, mobile, or social channel. Also, sends a notification automatically to your customers to confirm that your support team had received the request.

Let us now discuss this in more detail.

Table of contents:



* [Benefits of Zendesk Automatic Ticket Creation](https://saufter.io/how-to-set-up-zendesk-automatic-ticket-creation/#benefits-zendesk)
* [Easy steps to set up Zendesk automatic ticket creation](https://saufter.io/how-to-set-up-zendesk-automatic-ticket-creation/#easy-set-up)
* [Why Zendesk is not the best for automatic ticket creation](https://saufter.io/how-to-set-up-zendesk-automatic-ticket-creation/#why-zendesk-not-best)
* [Meet Saufter, the best Zendesk automatic ticket creation alternative!](https://saufter.io/how-to-set-up-zendesk-automatic-ticket-creation/#helplama-helpdesk)


## **Benefits of Zendesk Automatic Ticket Creation**

*Source: zendesk.com*

When a customer has a question, he sends an email to the company. The in-built Answer Bot steps in to help, by scanning the text of the email to understand what the request is about.

It uses its powerful AI to find the most relevant articles and suggests them in a reply to the customer.

Your customer reviews the articles and if an answer is found, they can mark their question as answered. Thus the ticket is now solved.

If suppose the customer still needs help, then the system automatically creates a ticket and routes it to an agent.

It also collects Feedback automatically to improve future suggestions. Thus you can save a lot of manual time by answering repetitive tasks and creating tickets manually.


## **Easy Steps to set up Zendesk automatic ticket creation**

Setting up automatic ticket creation on Zendesk is slightly a lengthy process that requires your time to sit and set the parameters for every channel and every integration. Let us now see the major setup process for Zendesk email, Zendesk Chat, and social media.


### Automatic Ticket creation through email

When you install Zendesk and create an account, a support address is created for you by <NAME_EMAIL>. This serves as your system support address. You can also customize this support email address.

*Source: Zendesk support video tutorial*

When a customer sends you a request to this support address, Zendesk ticketing software automatically creates a ticket and automatically sets the ticket life cycle to “New”. You can view this ticket by using the following steps.



* Log in to your Zendesk account.
* Click on the Views icon.
* Click on Unassigned Tickets to view the new tickets.

You can use different email addresses for different verticals to manage and track your tickets based on the category of the support, for example – sales, marketing, etc.

To view all your support email addresses created, go to Admin and click on Email, the main window will display all the created support addresses.

You can also create triggers to route tickets to the respective teams.

You can receive support requests at an external email address (instead of a Zendesk Support email address). To do so, you need to forward the email that’s coming into your external email account (Gmail, for example) to Zendesk Support.


### Enabling automatic ticket creation for your Gmail inbox

*Important note*: Enabling this functionality leverages Google API services and hence adheres to Google’s limited use requirements described in the [Google API Services: User Data Policy](https://developers.google.com/terms/api-services-user-data-policy).

You can import email from one or more Gmail inboxes and automatically convert email messages to tickets. Zendesk Support will check for new emails in your Gmail inbox every minute and convert only new, unread email messages into tickets.

You can connect to multiple Gmail accounts and they get automatically added as a support address.

Before you connect to your Gmail account, make sure you sign in to the Gmail account you want to connect to.

*Source: Zendesk support video tutorial*

To connect to your Gmail account



* In the Admin Center, click the Channels icon in the sidebar, then select Email.
* In the Support addresses section, click Add address, then select Connect external address.
* If requested, sign in to your Gmail account.
* Follow the steps to complete the process.


### Automatic Ticket creation through Zendesk Chat

You can enable automatic ticket creation in Zendesk through Zendesk Chat. By enabling this, chats will turn into a ticket automatically after they end.

The chatbot collects all the necessary customer data like name, email, etc, and automatically maps it all into the ticket fields for agents’ use. You can also use this to meet your escalation needs.

You can configure different settings for how tickets are created for both chats and offline messages.

*Source: Zendesk support video tutorial*

To do so:



* Go to Zendesk Admin Panel – select Chat from the Channels menu on the left sidebar.
* Sign in and link your Zendesk Chat account.
* Then follow the instructions to complete the Zendesk Chat sign-in process.
* Go to Settings, select Account, and click on the Zendesk Support tab. 
* Set Automatic ticket creation to Automatic.
* Transcript visibility: set to Private.
* Ticket assignment: set to the Last agent.
* Click Save.
* Under Ticket Creation for Offline Messages, also configure the Automatic Ticket Creation and Transcript Visibility settings as described above.
* Click Save Changes.


### Automatic ticketing through social media channels

To enable automatic ticketing through social media conversations on Twitter, Facebook, etc, you need to first enable login permission for your customers through a link.

*Source: Zendesk support video tutorial*

To do this,



* Go to your Admin Center.
* Select the social media platform (eg: Twitter) from the Channels menu on the left slide bar.
* Click on Enable in Admin Center and select Twitter login on the End Users window
* Then click on Save.
* Now click on the Twitter login again and add your Twitter account and Save.
* Select General Settings on the Twitter login window and select Yes on the ticket append link and select the check box for Always include shortened link.
* Click on Save.

Similarly, you can enable and customize auto-ticketing settings and triggers for Facebook pages, other available social platforms, Zendesk Talk, and other third-party apps or integrations from the Zendesk Admin Center.


## **Why Zendesk is not best for automatic ticket creation**

Though the Zendesk ticketing features sound exciting, the software interface is complicated to use and requires multiple settings and authorizations. We too second this opinion as we found it very complicated to research and source the right inputs for this article.

We also found plenty of unhappy customers criticizing its usability and customer support on various platforms. One of the customers highlighted their poor customer service as an irony as the software is designed to help elevate the customer support experience. Most of the users feel the software is highly-priced.


### Some of the unhappy users’ reviews:


    *The features are limited and the training options are insane. They charge $350 for a 10-minute video that didn’t even answer one of my questions over the course of 9 months while trying to get a simple answer about how to use and report on Tags* – says a customer on [getapp.com](https://www.getapp.com/customer-service-support-software/a/zendesk/reviews/).

 


    *The number one thing is the irony that they are a CS suite, yet they have poor customer service. It is also very pricey. It takes a lot of time to load and do to just get a simple report such as agent ticket counts and tag reports* – says a customer on [g2.com](https://www.g2.com/products/zendesk-support-suite/reviews).

 


    *Would not recommend this ticketing platform to any company. Their platform is constantly lagging, and would not work properly with any language tool extension. It freezes if you write a long answer* – says a customer on [Trustpilot](https://www.trustpilot.com/review/www.zendesk.com).


## **Meet Saufter, the best Zendesk automatic ticket creation alternative!**

Saufter is the only AI-enabled helpdesk that understands your business requirements on the whole and offers unified customer support solutions at an affordable price.

The Helpdesk integrates seamlessly with Shopify, email, Live chat, and native social platforms just with a click. The setup is easy and the user interface is simple to use enabling a smoother ticketing system across channels.

It hosts various interesting automation features that help streamline several tasks like ticketing and assigning, launching notifications, chatbot for self-help, and more. All these help you save up to 50% of your agents’ time to concentrate on more complex tasks.

This Helpdesk offers unique automated order tracking updates to you and your customers. Also enables your customers to initiate automated returns/exchange tickets. As a result, you can see an increase in customer satisfaction and increased ROI.

**[Visit the Website](https://saufter.io/home/<USER>

**[Check Pricing](https://saufter.io/custom-pricing/)**


## **Conclusion**

In this article, we have discussed how to set up a Zendesk automatic ticket in detail along with its benefits and some inevitable setbacks using the software. 

We also gave you an alternative study of [Saufter](http://saufter.io/home/<USER>

</context_blog>
------------------------------------------------------------------------------------