Context A: Zendesk article: Intelligent triage use cases and workflows
Intelligent triage use cases and workflows \
URL: https://sample.url.com/context-a
\
Intelligent triage is an AI-powered feature that automatically detects what a ticket is about (its intent), what language it's written in, and whether the customer's message is positive or negative (its sentiment). You can use this information to route tickets to the right groups automatically, create views to group similar types of requests, and report on trends in the types of tickets your customers are submitting.

This article describes example use cases to help you better understand how intelligent triage can streamline your workflows and support your business. Specifically, you'll learn how to use intelligent triage to automatically deflect tickets, route tickets to the right groups, reduce resolution time and touches, and forward information to external parties.

As you read through the use cases provided here, remember that you can always modify or expand on them to better support your specific workflows.

For more information about intelligent triage, see [Intelligent triage resources](https://support.zendesk.com/hc/en-us/articles/4471123173402).

This article contains the following topics:

- [Deflect: Redirect customers to self-serve or correct destination](https://support.zendesk.com/hc/en-us/articles/5222280338202-Intelligent-triage-use-cases-and-workflows#topic_ojz_rdx_tvb)
- [Route: Send tickets to a specific language queue from a general queue](https://support.zendesk.com/hc/en-us/articles/5222280338202-Intelligent-triage-use-cases-and-workflows#topic_r4m_tdx_tvb)
- [Reduce: Proactively request more information](https://support.zendesk.com/hc/en-us/articles/5222280338202-Intelligent-triage-use-cases-and-workflows#topic_fjq_5dx_tvb)
- [Forward: Use webhooks to pass information to external resources](https://support.zendesk.com/hc/en-us/articles/5222280338202-Intelligent-triage-use-cases-and-workflows#topic_rcb_wdx_tvb)

Related articles:

- [Automatically detecting customer intent, language, and sentiment](https://support.zendesk.com/hc/en-us/articles/*************)
- [Analyzing intelligent triage results and taking action](https://support.zendesk.com/hc/en-us/articles/5201262314266)
- [Using intelligent triage to identify and act on ticket escalations](https://support.zendesk.com/hc/en-us/articles/6353620565530)

**Note:** When creating triggers, views, or reports in Admin Center or Explore, intelligent triage prediction values are available only in English. However, intelligent triage is capable of evaluating content in the languages listed [here](https://support.zendesk.com/hc/en-us/articles/*************#h_01GYJ1PBVKD26QN3E8JNS3X3TX).

## Deflect: Redirect customers to self-serve or correct destination

**Scenario**: An end user is contacting you about a process where submitting a ticket or contacting support is not the best course of action. Instead, the required information is provided on your website or some other source, meaning the user can self-serve by following a process or other documentation that’s readily available.

**Examples**:

- Subscription cancellations
- User profile update requests
- Refund, return policy, and warranty questions
- Job applications

**How to use intelligent triage to address the scenario**:

1. Identify any intents that are frequently applied to the tickets submitted in the scenario described above (for example, "Cancel subscription"). If helpful, you can group multiple related intents together (like "Refund request" and "Refund via specific channel").
2. Determine the necessary confidence level that the intent must have for you to be comfortable taking automatic action on it. In other words, are false positives acceptable, where the end user can reply if they still need help?
3. [Create a trigger](https://support.zendesk.com/hc/en-us/articles/*************#topic_k43_4hs_25b) that sends an automated reply to the end user. Include instructions on how they can complete the task, a link to where they need to go in their account or app to complete the task, or a link to an existing document that answers their question.
4. Leave the conversation open-ended in case the intent was accidentally mismatched, but set the ticket to a Solved status.

## Route: Send tickets to a specific language queue from a general queue

**Scenario**: All or most end users submit tickets to a common queue, with tickets from different languages ending up in the same queue.

**Examples**:

- All end users use the same contact form or email address, regardless of language
- End users use the source platform in one language, but their preferred language is different from the current language of the platform or browser

**How to use intelligent triage to address the scenario**:

- **Option 1**: [Create a trigger](https://support.zendesk.com/hc/en-us/articles/*************#topic_pxc_rhs_25b) (leaving out any intent conditions) that routes tickets to the appropriate agents or group based on language (and potentially language confidence, if necessary).
- **Option 2**: Use other integrations to reference the ticket’s language value and take an action based on that value. For example, if you currently [translate macros using dynamic content](https://support.zendesk.com/hc/en-us/articles/4776777747866), you could instead [use Liquid markup](https://support.zendesk.com/hc/en-us/articles/4408883291290) to determine which language the macro should use based on the [intelligent triage Language field](https://support.zendesk.com/hc/en-us/articles/*************#topic_ebn_l4g_htb). This approach is useful if the requester's language isn't already set in their profile (for example, if they're contacting support from an unregistered email, or in a different language than the one they have set in their profile).

## Reduce: Proactively request more information

**Scenario**: Customers contact support, but don’t include the details required to resolve the request. Agents have to reply asking for the necessary information rather than being able to solve the request during the first touch.

**Examples**:

- Return/replacement requests where the customer needs to provide an address
- Processes where the customer must include a purchase order or invoice number

**How to use intelligent triage to address the scenario**:

1. Identify any intents that are frequently applied to the tickets submitted in the scenario described above (for example, "Return order"). If helpful, you can group multiple related intents together.
2. Determine the necessary confidence level that the intent must have for you to be comfortable taking automatic action on it.
3. [Create a trigger](https://support.zendesk.com/hc/en-us/articles/*************#topic_k43_4hs_25b) that sends an automated reply to the end user, prompting them for the required details if they haven't already included them. This gives the customer the opportunity to reply before the agent sees the ticket, and makes it more likely that the agent can solve the ticket in a single touch.

## Forward: Use webhooks to pass information to external resources

**Scenario**: Customers contact support for a request that requires involvement from an external team or system. Agents must manually forward these requests to the appropriate destinations.

**Examples**:

- End users reach out to customer support to change their email address or other contact details, but that’s owned by a team outside of Zendesk
- Certain requests require compliance or other processes to be followed outside of Zendesk

**How to use intelligent triage to address the scenario**:

1. Identify any intents that are frequently applied to the tickets submitted in the scenario described above (for example, "Change email address"). If helpful, you can group multiple related intents together.
2. Determine the necessary confidence level that the intent must have for you to be comfortable taking automatic action on it.
3. [Create a trigger](https://support.zendesk.com/hc/en-us/articles/*************#topic_k43_4hs_25b) that sends an automated reply that does both of the following:
   1. Informs the requester that their ticket has been received.
   2. [Uses a webhook](https://support.zendesk.com/hc/en-us/articles/4408839108378), [email target](https://support.zendesk.com/hc/en-us/articles/4408883282458), or other means from within the product to forward the relevant details from the customer’s request to the appropriate external team. For example, you might send an email to an external team that includes the requester’s name, email address, subject, and original message. The external team can then process the customer’s request in their system.

---

Context B: Zendesk article: viewing and managing intelligent triage predictions
Zendesk article: viewing and managing intelligent triage predictions \
URL: https://sample.url.com/context-b
\
You must have the [Advanced AI add-on](https://support.zendesk.com/hc/en-us/articles/5524125586330) to use the features described in this article.

If you [use intelligent triage](https://support.zendesk.com/hc/en-us/articles/*************), you can view and manage the values for the intent, language, and sentiment prediction types.

This article contains the following topics:

- [Viewing all intent, language, and sentiment values](https://support.zendesk.com/hc/en-us/articles/*************-Viewing-and-managing-intelligent-triage-predictions#topic_l3b_hn3_fzb)
- [Editing intent names](https://support.zendesk.com/hc/en-us/articles/*************-Viewing-and-managing-intelligent-triage-predictions#topic_qbn_ln3_fzb)
- [Requesting a new intent](https://support.zendesk.com/hc/en-us/articles/*************-Viewing-and-managing-intelligent-triage-predictions#topic_pv1_m5b_1bc)
- [Allowing agents to see and update intelligent triage fields](https://support.zendesk.com/hc/en-us/articles/*************-Viewing-and-managing-intelligent-triage-predictions#topic_vx1_4n3_fzb)

Related articles:

- [Automatically detecting customer intent, language, and sentiment](https://support.zendesk.com/hc/en-us/articles/*************)
- [Making sense of unexpected intelligent triage predictions](https://support.zendesk.com/hc/en-us/articles/5608698604698)

## Viewing all intent, language, and sentiment values

In Admin Center, you can see all possible values for the intent, language, and sentiment prediction types. You can also see these values in any of the supported languages.

**To see all possible intent, language, and sentiment values**

1. In [Admin Center](https://support.zendesk.com/hc/en-us/articles/*************#topic_hfg_dyz_1hb), click

<p id="gdcalert1" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image1.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert2">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image1.png "image_tooltip")
**Objects and rules** in the sidebar, then select **Business rules > Intelligent triage**. 2. Click **Settings** for the prediction type you want to view (**Intent**, **Language**, or **Sentiment**). 3. Click the **Intent list**, **Language list**, or **Sentiment list** tab. \

<p id="gdcalert2" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image2.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert3">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image2.png "image_tooltip")

4. (Intent only) Click any category in the list to expand it to view the subcategories. Click any subcategory to expand it to view individual intents. \
   Alternatively, click **Actions** > **Expand all** to expand all categories and subcategories. Click **Actions** > **Collapse all** to collapse them again.
5. To search for a specific value, use the search bar.
6. To see the tags associated with each value, click **Actions** > **Show tags**. This can be helpful when building triggers or views with intelligent triage conditions.
7. To see the values in a different language, click **Actions** > **View language** and select a language. The available languages are listed [here](https://support.zendesk.com/hc/en-us/articles/*************-Zendesk-language-support-by-product#h_01GYJ1PBVKD26QN3E8JNS3X3TX:~:text=AI%20add%2Don-,Intelligent%20triage,-ar).

### Viewing updates to the list of intents

Zendesk occasionally adds new intents and deactivates unneeded intents. When this happens, the affected intents are flagged with either a New or Deactivated icon for 30 days, and a banner appears in the Intents list tab.

**To view updates to the list of intents**

1. In the **Your intents list has been updated** banner, click **View changes**. \

<p id="gdcalert3" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image3.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert4">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image3.png "image_tooltip")
\
A side panel opens and shows which intents have been updated in the last 30 days. \

<p id="gdcalert4" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image4.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert5">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image4.png "image_tooltip")

2. Click **Close** to return to the **Intents list** tab.

### Viewing deactivated intents

When you view the AI Intents list, deactivated intents are not shown by default. However, you can choose to include them in the list.

Zendesk may deactivate intent values if the intent is no longer available or relevant to your account. Intent values may also be reactivated if they become relevant again. When this happens, they’re added back to your account but are not flagged as new.

Deactivated intents still appear in Explore reports.

**To view deactivated intents**

1. [Open the AI Intents list.](https://support.zendesk.com/hc/en-us/articles/*************#topic_l3b_hn3_fzb)
2. On the **Intents list** tab, click **Actions** > **Show deactivated intents**. \

<p id="gdcalert5" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image5.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert6">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image5.png "image_tooltip")
\
Deactivated intents now appear in the AI Intents list. \

<p id="gdcalert6" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image6.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert7">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image6.png "image_tooltip")

### Filtering by new intents

When you view the AI Intents list, you can filter it to see only [edited intents](https://support.zendesk.com/hc/en-us/articles/*************#topic_qbn_ln3_fzb), newly added intents, or deactivated intents.

**To filter by edited, new, or deactivated intents**

1. [Open the AI Intents list.](https://support.zendesk.com/hc/en-us/articles/*************-Viewing-and-managing-intelligent-triage-predictions#topic_l3b_hn3_fzb)
2. On the **Intent list** tab, click **Filter**.
3. Select **Edited**, **New**, or **Deactivated**, depending on which type of intent you want to see. \

<p id="gdcalert7" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image7.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert8">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image7.png "image_tooltip")
\
**Note:** To see the **Deactivated** option, you must have already followed the steps to [view deactivated intents](https://support.zendesk.com/hc/en-us/articles/*************-Viewing-and-managing-intelligent-triage-predictions#topic_vl1_23b_jdc).

## Editing intent names

You can edit an intent’s name in any supported language to better reflect the terminology preferred by your organization.

Editing an intent name doesn’t change the intent itself. Intelligent triage continues to detect tickets based on the original intent, even after its name has been edited. Because of this, you should only edit intent names to make small adjustments to an intent’s terminology, not updates that completely change the meaning of an intent.

**To edit an intent name**

1. In [Admin Center](https://support.zendesk.com/hc/en-us/articles/*************#topic_hfg_dyz_1hb), click

<p id="gdcalert8" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image8.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert9">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image8.png "image_tooltip")
**Objects and rules** in the sidebar, then select **Business rules > Intelligent triage**. 2. Click **Settings** for the **Intent** prediction type. 3. (Optional) Change the language by clicking **Actions** > **View language** and select the language you want to edit intents for. 4. Browse or search to find the intent you want to edit. 5. Hover your mouse over the intent, click the options (

<p id="gdcalert9" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image9.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert10">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image9.png "image_tooltip")
) icon, and select **Edit name**. \

<p id="gdcalert10" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image10.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert11">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image10.png "image_tooltip")

6. Update the name of the intent and click the checkmark. \

<p id="gdcalert11" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image11.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert12">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image11.png "image_tooltip")

The intent is automatically marked with an Edited icon to let you know that it has been changed from its original name.

<p id="gdcalert12" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image12.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert13">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image12.png "image_tooltip")

**To restore an intent name to its default value**

1. In [Admin Center](https://support.zendesk.com/hc/en-us/articles/*************#topic_hfg_dyz_1hb), click

<p id="gdcalert13" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image13.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert14">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image13.png "image_tooltip")
**Objects and rules** in the sidebar, then select **Business rules > Intelligent triage**. 2. Click **Settings** for the **Intent** prediction type. 3. Browse or search to find the intent you want to restore. 4. Hover your mouse over the intent, click the options (

<p id="gdcalert14" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image14.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert15">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image14.png "image_tooltip")
) icon, and select **Restore default name**. \

<p id="gdcalert15" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image15.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert16">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image15.png "image_tooltip")

## Requesting a new intent

If the [existing values for the intent prediction type](https://support.zendesk.com/hc/en-us/articles/*************#topic_l3b_hn3_fzb) don’t meet your organization’s needs, you can request that Zendesk create a new intent for you. You must have intents turned on to request a new intent. You may request up to 50 intents, but each intent request must be submitted separately.

When you request a new intent, Zendesk performs an evaluation to determine whether the new intent can be created. The evaluation process can take up to two weeks. To track your request, an initial email is sent to you, and you’re subsequently notified of an approval or rejection and further details.

If your intent is approved, it isn’t immediately added to your account. An approval means that Zendesk will add the intent to our standard intent model and redeploy the model to make the intent available. We anticipate the model will be redeployed one to two times each quarter.

You can request a new intent in Admin Center. Intents cannot be requested in bulk.

**Note:** If you’ve opted out of data collection, your request cannot be approved.

**To request a new intent**

1. In [Admin Center](https://support.zendesk.com/hc/en-us/articles/*************#topic_hfg_dyz_1hb), click

<p id="gdcalert16" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image16.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert17">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image16.png "image_tooltip")
**Objects and rules** in the sidebar, then select **Business rules > Intelligent triage**. 2. Click **Intent**. 3. Select the **Intent list** tab. 4. Click the **Actions** drop-down and select **Request new intent**. \

<p id="gdcalert17" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image17.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert18">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image17.png "image_tooltip")
\
The **Request new intent** page opens. \

<p id="gdcalert18" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image18.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert19">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image18.png "image_tooltip")

5. Fill in the following required fields. Forms submitted without all the fields will be declined.
   - **Name.** A short, descriptive name for the intent. To see the intents currently available to you for comparison, see [Viewing all intent, language, and sentiment values](https://support.zendesk.com/hc/en-us/articles/*************#topic_l3b_hn3_fzb).
   - **Description.** An explanation of the meaning behind the intent. Remember that an intent is a prediction of what the ticket is about.
   - **Placement.** The category and subcategory that the new intent applies to. If you're not sure which category and subcategory to pick, select **I don't know**.
   - **Tickets with this intent.** Example tickets that the new intent applies to. You must list up to ten examples, and each ticket must:
     - Have the full URL (for example, https://&lt;yoursubdomain>.zendesk.com/agent/tickets/1)
     - Come from the Email channel
     - Be in English
     - Have a subject and description
     - Be unique (in other words, tickets can't be repeated) but must represent the same intent
   - **E-mail address.** The email address that should be used to communicate the status of the request.
6. Click **Request intent**.

## Allowing agents to see and update intelligent triage fields

When you enable intelligent triage, the system automatically fills out the Intent, Language, and Sentiment fields, but these fields are [visible in tickets](https://support.zendesk.com/hc/en-us/articles/4685355428250) by default only if you have a single ticket form.

- If you have multiple ticket forms and you want agents to be able to see and change these fields in tickets, [edit your ticket forms](https://support.zendesk.com/hc/en-us/articles/4408846520858#topic_c5x_l3b_lk) and add the new fields by dragging them into the form.
- If you have a single ticket form but you don’t want agents to be able to see and change these fields in tickets, [edit your default ticket form](https://support.zendesk.com/hc/en-us/articles/4408846520858#topic_c5x_l3b_lk) and remove the new fields by dragging them off the form.
- If you want to use the intelligent triage field values only for Explore reporting or API use, you don’t need to add them to any ticket forms.

If you opt to let agents see the Intent, Language, and Sentiment fields in tickets, they can update the values in these fields if they feel they’re not correct.

Intelligent triage predictions are based on a ticket’s first message only. Agents should understand that any updates they make to these fields should still be based on the first message. Changing the intent, language, or sentiment doesn't influence the machine learning model responsible for intelligent triage. In other words, agents can't train the model.

**Tip:** You can [report on agents' manual updates](https://support.zendesk.com/hc/en-us/articles/4550629802650) to the intelligent triage prediction fields, which helps you discover trends in the types of tickets where intelligent triage isn't correctly predicting the intent, language, or sentiment.

---

Context C: Zendesk Article - Automatically detecting customer intent, language, and sentiment
URL: https://sample.url.com/context-c
Intelligent triage uses artificial intelligence (AI) to enrich your Support and messaging tickets with actionable information without the help of agents or admins. Specifically, intelligent triage predicts the intent, language, and customer sentiment for new tickets. You can then use this information to automate workflows with triggers and automations, set up views for teams, create Explore reports, and more.

This article contains the following topics:

- [Requirements for using intelligent triage](https://support.zendesk.com/hc/en-us/articles/*************-Automatically-detecting-customer-intent-language-and-sentiment#topic_bz4_24x_bvb)
- [Understanding intelligent triage](https://support.zendesk.com/hc/en-us/articles/*************-Automatically-detecting-customer-intent-language-and-sentiment#topic_ebn_l4g_htb)
- [Turning on and configuring intelligent triage](https://support.zendesk.com/hc/en-us/articles/*************-Automatically-detecting-customer-intent-language-and-sentiment#topic_gpp_p4g_htb)

Related articles:

- [About intelligent triage](https://support.zendesk.com/hc/en-us/articles/4964463770650)
- [Viewing and managing intelligent triage predictions](https://support.zendesk.com/hc/en-us/articles/*************)

## Requirements for using intelligent triage

Intelligent triage is available for organizations who meet the following requirements:

<table>
  <tr>
   <td><strong>Requirement type</strong>
   </td>
   <td><strong>Requirement criteria</strong>
   </td>
  </tr>
  <tr>
   <td>Plan
   </td>
   <td>
<ul>

<li>Suite Professional and above (Legacy plans not eligible)</li>

<li>Support Professional and above (Legacy plans not eligible)</li>
</ul>
   </td>
  </tr>
  <tr>
   <td>Industry
<p>
This requirement applies only to <a href="https://support.zendesk.com/hc/en-us/articles/*************#topic_ebn_l4g_htb">intent predictions</a>. You may still use intelligent triage for language and sentiment predictions without being in one of the listed industries.
   </td>
   <td>
<ul>

<li>Retail and e-commerce business-to-consumer (B2C). Does not include manufacturers, wholesale, business-to-business (B2B), or customer-to-customer (C2C).</li>

<li>Software platforms or applications. B2C only. Does not include companies that commercialize gadgets or hardware components.</li>

<li>Financial services. Traditional and online banking only. B2C only.</li>

<li>Insurance: Includes auto, health, home, and pet insurance.</li>

<li>Employee experience: Includes IT and HR departments only.</li>

<li>Travel, hospitality, and tourism: Includes airlines, bus lines, travel agencies, car rentals, and booking management.</li>

<li>Entertainment and gaming. Includes online casinos, betting platforms, and ticket selling for events.</li>

<li>Education. Includes information about education courses, examinations, admissions, scholarships, and student life.</li>
</ul>
   </td>
  </tr>
  <tr>
   <td>Ticket volume
<p>
This requirement applies only to intent predictions.
   </td>
   <td>More than 1,000 tickets created by end users in the supported languages in the last 6 months
   </td>
  </tr>
  <tr>
   <td>Ticket language distribution
   </td>
   <td>At least 90% of tickets in a <a href="https://support.zendesk.com/hc/en-us/articles/*************-Automatically-detecting-customer-intent-language-and-sentiment#topic_nl1_s4g_htb">supported language</a>
   </td>
  </tr>
  <tr>
   <td>Right model fit
<p>
This requirement applies only to intent predictions.
   </td>
   <td>To determine how well the current machine learning model applies to an account, we perform a quality check in a sample of tickets from the past 6 months. During the quality check, intelligent triage must label at least 30% of tickets with high confidence and at least 60% with medium confidence.
   </td>
  </tr>
</table>

### Limited availability participants

Zendesk previously offered intelligent triage as part of a limited availability offering. To have been part of the limited availability offering, you had to have:

- Met the requirements listed above
- Activated intelligent triage before Dec 12, 2022

Customers who took advantage of the limited availability offering retain free access to the intelligent triage functionality released before December 12, 2022 without needing to purchase a paid plan add-on. Additional functionality won't be included in the free limited availability offering.

## Understanding intelligent triage

When you enable intelligent triage, the system adds new fields to your tickets:

- **Intent**: A prediction of what the ticket is about. To see the possible values, [open the Intent list tab of the Intent settings page](https://support.zendesk.com/hc/en-us/articles/*************#topic_l3b_hn3_fzb) to see the AI Intents list under the **Taxonomy values** heading.
- **Intent confidence**: How likely it is that the intent prediction is correct. Possible values are **High**, **Medium**, and **Low**.
- **Language**: A prediction of what language the ticket is written in. To see the possible values, [open the Language list tab of the Language settings page](https://support.zendesk.com/hc/en-us/articles/*************#topic_l3b_hn3_fzb).
- **Language confidence**: How likely it is that the language prediction is correct. Possible values are **High**, **Medium**, and **Low**.
- **Sentiment**: A prediction of how the customer feels about their request. Possible values are **Very Positive**, **Positive**, **Neutral**, **Negative**, and **Very Negative**.
- **Sentiment confidence**: How likely it is that the sentiment prediction is correct. Possible values are **High**, **Medium**, and **Low**.

These fields are populated via ticket updates by Zendesk’s machine learning model based on the content of the first message of a ticket when it’s submitted. Agents can [update the field values](https://support.zendesk.com/hc/en-us/articles/*************#topic_vx1_4n3_fzb) if necessary.

<p id="gdcalert1" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image1.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert2">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image1.png "image_tooltip")

When the fields are populated, tags are also automatically added to the ticket to help you build triggers, automations, and reporting. These tags reflect the values in the Intent, Language, and Sentiment fields, and are structured as follows:

- intent\_\__value of Intent field_
- language\_\__value of Language field_
- sentiment\_\__value of Sentiment field_

**Tip:** When creating triggers, automations, or reports, you can use the field value or the tag, depending on which one makes your build easier. For example, when creating a trigger with multiple intents, it’s easier to use tags because you can add them faster than adding separate conditions for multiple field values.

To learn how to create a report showing the results of intelligent triage, see [Explore recipe: Intelligent triage predictions and confidence](https://support.zendesk.com/hc/en-us/articles/4550620559258).

**Tip:** You can also use the [Tickets API](https://developer.zendesk.com/api-reference/ticketing/tickets/tickets/) to return the intent, language, and sentiment as custom field values.

### Understanding intent, language, and sentiment values

#### Intent

For the Intent field, intelligent triage is trained to identify intents specifically for [certain industries](https://support.zendesk.com/hc/en-us/articles/*************#topic_bz4_24x_bvb:~:text=plans%20not%20eligible)-,Industry,-This%20requirement%20applies). Additionally, intent and sentiment detection works only for the following languages:

<table>
  <tr>
   <td>
<ul>

<li>Arabic</li>

<li>Bulgarian</li>

<li>Chinese (Simplified)</li>

<li>Czech</li>

<li>Danish</li>

<li>Dutch</li>

<li>English</li>

<li>Finnish</li>

<li>French</li>

<li>German</li>
</ul>
   </td>
   <td>
<ul>

<li>Greek</li>

<li>Hebrew</li>

<li>Hindi</li>

<li>Hungarian</li>

<li>Indonesian</li>

<li>Italian</li>

<li>Japanese</li>

<li>Korean</li>

<li>Norwegian</li>

<li>Polish</li>
</ul>
   </td>
   <td>
<ul>

<li>Portuguese</li>

<li>Romanian</li>

<li>Russian</li>

<li>Spanish</li>

<li>Swedish</li>

<li>Thai</li>

<li>Turkish</li>

<li>Ukrainian</li>

<li>Vietnamese</li>
</ul>
   </td>
  </tr>
</table>

The Zendesk Intent Model includes intents for all of the supported industries. An account with this model has access to relevant intents and use cases based on their ticket conversation data. This model allows us to support accounts with multiple brands, mixed use cases, and industries that cross over between our currently supported industries.

#### Language

For the Language field, intelligent triage can detect approximately 150 different languages. See [Zendesk language support by product](https://support.zendesk.com/hc/en-us/articles/*************#h_01GYJ1PBVKD26QN3E8JNS3X3TX).

#### Sentiment

The Sentiment field is a prediction of how positive or negative a customer feels about the request they’re submitting. The prediction is made based on the text of the first message in the ticket and is grouped into one of the following categories:

- **Very positive**: The message likely contains strong positive words (like “brilliant” or “perfect”), positive words modified by intensity adverbs, or multiple positive sentences.
- **Positive**: The message likely contains phrases expressing gratitude, or one to two positive sentences.
- **Neutral**: The message likely contains factual statements without additional negative quantifiers (like “any” or “always”), or a mix of positive and negative statements.
- **Negative**: The message likely contains phrases expressing frustration, complaints containing negative words, or repetition of the same dissatisfaction.
- **Very negative**: The message likely contains strong negative words, capitalized text, multiple exclamation marks, or multiple negative phrases.

Intelligent triage is specifically calibrated for customer service. This means that a ticket isn’t assigned a negative sentiment just because a customer has an issue with their order, can’t find the information they need, or some other similar “negative” situation. Instead, the model is tuned to analyze sentiment with the assumption that the customer is contacting customer service because they have an issue that needs to be addressed.

## Turning on and configuring intelligent triage

Administrators can turn on intelligent triage in Admin Center.

Intelligent triage adds predictions to tickets that were created only after it was turned on. For other reasons you might not see predictions on tickets, see [Why didn't intelligent triage add predictions to a ticket?](https://support.zendesk.com/hc/en-us/articles/5798063767066)

**To turn on and configure intelligent triage**

1. In [Admin Center](https://support.zendesk.com/hc/en-us/articles/*************#topic_hfg_dyz_1hb), click

<p id="gdcalert2" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image2.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert3">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image2.png "image_tooltip")
**Objects and rules** in the sidebar, then select **Business rules > Intelligent triage**. \
You're taken to the following screen, where the status of the Intent, Language, and Sentiment prediction types is initially listed as Pending. After a moment, the status changes to Active, letting you know that intelligent triage is ready. If the Intent status shows Unavailable, see [Why is the intent unavailable? \
](https://support.zendesk.com/hc/en-us/articles/5758644394522)

<p id="gdcalert3" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image3.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert4">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image3.png "image_tooltip")

2. To configure or turn off the individual prediction types, hover your mouse over the prediction type (intent, language, or sentiment) you want to modify and select **Settings** from the options menu.
3. On the prediction type's settings page, choose from the following options:
   _ **Detect &lt;prediction type>**: Deselect the checkbox if you don't want the system to automatically enrich tickets with that prediction type.
   _ **Email channels**: The **Web form**, **Email**, and **Web service (API)** channels are turned on by default. You can also turn on the **Text**, **Web Widget**, **Mobile SDK**, **Mobile**, **Facebook Post**, **X Corp**, and **Social Messaging** channels. (If you create tickets via [channel integrations](https://support.zendesk.com/hc/en-us/articles/4408824097050#topic_amm_zbh_bhb), select **Social Messaging**.) \
   **Note:** While we generally don’t recommend turning on additional channels, you might consider enabling them if you have high ticket volume on those channels. Channels with more conversational messages, system notifications, or spam will have lower-confidence predictions.
   _ **Messaging channels**: The **Web Widget**, **WhatsApp**, and **Facebook Messenger** channels are turned on by default. You can also turn on the **Android SDK**, **Google RCS**, **Instagram DM**, **iOS SDK**, **LINE**, **Slack**, **Telegram**, **Twilio SMS**, **X Corp DM**, **Viber**, **WeChat**, **Apple Business Chat**, **Google Business Messages**, **KakaoTalk**, **MessageBird SMS**, **Native Messaging**, and **Sunshine Conversations API** channels. \
   **Note:** Turning on any one of the **Web Widget**, **iOS SDK**, **Android SDK**, or **Native Messaging** channels automatically turns on all four channels together.
   _ **Exclusion conditions**: To exclude tickets created by agents from being enriched with the prediction type, select **Do not triage agent-initiated tickets**. \
   **Note:** When this checkbox is selected, suspended tickets that are recovered are not enriched with intelligent triage predictions. \

<p id="gdcalert4" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image4.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert5">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image4.png "image_tooltip")

4. Click **Save**. \
   After a moment, the **Off **indicator in the page header changes to **On** to indicate that the prediction type is now active.
