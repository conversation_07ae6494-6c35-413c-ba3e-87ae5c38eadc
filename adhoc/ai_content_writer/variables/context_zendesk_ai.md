Context A: Getting started with Zendesk AI and Advanced AI
""""# **Getting started with Zendesk AI and Advanced AI**

Zendesk AI is the intelligence layer of the Zendesk platform. Built on billions of points of customer-service data, Zendesk AI enhances every part of your service experience, from smarter conversations and AI agents, to productivity tools for agents, to new insights and instant actions for admins.

This article introduces you to all the AI-powered features Zendesk has to offer. It also shows you how to leverage these features to achieve your business goals. It walks through the key items you need to set up and gives you guidelines on where to find more information.

Some AI features are included in the suite, and some require the [Advanced AI add-on](https://support.zendesk.com/hc/en-us/articles/*************). To show or hide information about the Advanced AI features throughout this article, use the buttons below.

Hide Advanced AI features Show Advanced AI features

**Tip:** For information on other AI features that are still in early access programs (EAPs), see [Current and upcoming Zendesk betas and early access programs (EAPs)](https://support.zendesk.com/hc/en-us/articles/4408829663642#h_01J0PM180PA62RG0GK0S9RF902).

This article contains the following topics:

- [Zendesk AI and Advanced AI at a glance](https://support.zendesk.com/hc/en-us/articles/5608652527386-Getting-started-with-Zendesk-AI-and-Advanced-AI#h_01HH0BTGDG5KZPGRD3V3R3BZ6P)
- [Using AI to empower agents and improve efficiency](https://support.zendesk.com/hc/en-us/articles/5608652527386-Getting-started-with-Zendesk-AI-and-Advanced-AI#h_01HH0CM5D1AC3S1X1CJHMJKMDP)
- [Using AI to route tickets and provide faster service](https://support.zendesk.com/hc/en-us/articles/5608652527386-Getting-started-with-Zendesk-AI-and-Advanced-AI#h_01HH0BTYYBV6ETWHG8779M9Y8C)
- [Using AI to strengthen your knowledge base and deliver answers](https://support.zendesk.com/hc/en-us/articles/5608652527386-Getting-started-with-Zendesk-AI-and-Advanced-AI#h_01HH0BXJYWD1TJ7K2GGKDPZRJG)
- [Using AI to provide 24/7 service and build smarter AI agents](https://support.zendesk.com/hc/en-us/articles/5608652527386-Getting-started-with-Zendesk-AI-and-Advanced-AI#h_01HH0BXYS1DMWKDBR4YNK8G5ZV)

Related articles:

- [About Zendesk Advanced AI](https://support.zendesk.com/hc/en-us/articles/*************)
- [Resources for Zendesk Advanced AI](https://support.zendesk.com/hc/en-us/articles/5608656346650)

## Zendesk AI and Advanced AI at a glance

Zendesk offers two levels of AI functionality to meet your business needs:

- Zendesk AI, included with standalone product and Suite plans, with specific features varying by plan level
- Zendesk Advanced AI, available as an add-on for Suite and Support Professional and above plans

The table below summarizes the features included in each level.

<table>
  <tr>
   <td> 
   </td>
   <td>Automate customer interactions with AI agents
   </td>
   <td>Assist agents to solve issues faster
   </td>
   <td>Optimize your service workflows
   </td>
  </tr>
  <tr>
   <td>Zendesk AI
<p>
Included with Suite plans, with specific features varying by plan level
   </td>
   <td><a href="https://support.zendesk.com/hc/en-us/articles/4408824263578">Conversation bots</a>
<p>
<a href="https://support.zendesk.com/hc/en-us/articles/4408825385242">Autoreplies with articles</a>
<p>
<a href="https://support.zendesk.com/hc/en-us/articles/6138268212634">Generative replies</a>
<p>
<a href="https://support.zendesk.com/hc/en-us/articles/6067442759066">Bot personas</a>
<p>
<a href="https://support.zendesk.com/hc/en-us/articles/4422584657434-Creating-conversation-bot-answers-for-common-customer-questions#:~:text=three%20AI%2Dpowered-,suggested%20intents,-to%20use%20instead">Suggested intents</a> when creating answers
<p>
<a href="https://support.zendesk.com/hc/en-us/articles/5537827011994">Intent suggestions</a> for unanswered questions
   </td>
   <td><a href="https://support.zendesk.com/hc/en-us/articles/*************#topic_g5d_krv_hrb">Suggested macros</a> for agents
<p>
<a href="https://support.zendesk.com/hc/en-us/articles/5581313653530">Knowledge in the context panel</a>
   </td>
   <td><a href="https://support.zendesk.com/hc/en-us/articles/*************">Content Cues</a> for content managers
<p>
<a href="https://support.zendesk.com/hc/en-us/articles/5633225532826">Semantic search</a> in the help center
   </td>
  </tr>
  <tr>
   <td>Zendesk Advanced AI
<p>
Available as an add-on for Suite and Support Professional and above
   </td>
   <td><a href="https://support.zendesk.com/hc/en-us/articles/5501378527898#topic_mvf_wwg_kxb">Autoreplies with intelligent triage</a>
<p>
 
   </td>
   <td><a href="https://support.zendesk.com/hc/en-us/articles/7051314237466">Auto assist</a>
<p>
<a href="https://support.zendesk.com/hc/en-us/articles/7041677653914">Suggested first replies</a>
<p>
<a href="https://support.zendesk.com/hc/en-us/articles/6942763726106">Quick answers for Agent Workspace</a>
<p>
<a href="https://support.zendesk.com/hc/en-us/articles/8037649972634">Ticket summaries</a>
<p>
<a href="https://support.zendesk.com/hc/en-us/articles/4685355428250">Intent, language, and sentiment predictions</a>
<p>
<a href="https://support.zendesk.com/hc/en-us/articles/5608712782362">Enhance writing</a>
<p>
<a href="https://support.zendesk.com/hc/en-us/articles/6885971957914">Merging suggestions</a>
   </td>
   <td><a href="https://support.zendesk.com/hc/en-us/articles/4964463770650">Intelligent triage</a>
<p>
<a href="https://support.zendesk.com/hc/en-us/articles/*************">Macro suggestions</a> for admins
<p>
<a href="https://support.zendesk.com/hc/en-us/articles/*************#topic_ky5_qwg_xyb">Autoreplies</a> and <a href="https://support.zendesk.com/hc/en-us/articles/*************#topic_kvx_rwg_xyb">Internal note</a> trigger actions
<p>
<a href="https://support.zendesk.com/hc/en-us/articles/6267360002714">Generative AI for help center</a>
<p>
<a href="https://support.zendesk.com/hc/en-us/articles/6170157307162">Generative AI for voice</a>
   </td>
  </tr>
</table>

## Using AI to empower agents and improve efficiency

Your agents have a lot on their plates these days. Give them the tools they need to succeed right out of the gate with a roundup of AI-derived intelligence, such as a ticket summary and user sentiment, in an easily accessible side panel.

Connect them with your help center to give them the answers they need to answer customer questions without having to leave their workspace. And give them the right words to use when answering customer questions, helping your customers enjoy a consistent experience no matter which agent they’re connected with.

You can save them time in other ways, too. Proactively suggest tickets they can merge together to solve multiple customer requests at once. Or for agents who take calls, give them tools that automatically transcribe and summarize the content of the call they just finished, saving them precious wrap-up time.

## Give agents their own AI-powered assistant (Advanced AI)

Auto assist is an AI-powered assistant that helps agents solve tickets faster. Auto assist uses a large language model (LLM) to understand the contents of submitted tickets and make suggestions to your agents on how to solve them.

<p id="gdcalert1" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image1.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert2">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image1.png "image_tooltip")

Benefits

- Spend less time on repetitive tickets by following automatically generated guidance from auto assist.
- Solve tickets in a more consistent way based on admin-configured procedures.
- Take actions on behalf of the agent after the agent has reviewed and approved them.

Prerequisites

- You must have the [Advanced AI add-on](https://support.zendesk.com/hc/en-us/articles/*************) and have [configured procedures](https://support.zendesk.com/hc/en-us/articles/7924047699738) and (optionally) [actions](https://support.zendesk.com/hc/en-us/articles/8013439366810).

Show me how

- [Using auto assist to help agents solve tickets](https://support.zendesk.com/hc/en-us/articles/7051314237466)
- [Turning on and configuring auto assist](https://support.zendesk.com/hc/en-us/articles/8013454025114)
- [Creating and managing procedures for auto assist](https://support.zendesk.com/hc/en-us/articles/7924047699738)
- [Creating and managing actions for auto assist](https://support.zendesk.com/hc/en-us/articles/8013439366810)
- [Workflow recipe: Canceling and refunding a Shopify order with auto assist](https://support.zendesk.com/hc/en-us/articles/7719560079642)
- [Explore recipe: Reporting on the auto assist feature of agent copilot](https://support.zendesk.com/hc/en-us/articles/7739110419610)

## Get the conversation started (Advanced AI)

Suggested first replies use generative AI to suggest a first response for agents in tickets based on existing macros and help center articles. Using AI-generated suggestions to respond to tickets helps agents save time and increase productivity.

<p id="gdcalert2" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image2.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert3">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image2.png "image_tooltip")

Benefits

- Increase first response times by giving agents the right words to get the conversation started, ultimately helping them resolve customer requests more quickly.

Prerequisites

- You must have the [Advanced AI add-on](https://support.zendesk.com/hc/en-us/articles/*************) and be using [Agent Workspace](https://support.zendesk.com/hc/en-us/articles/4408821259930).

Show me how

- [Using AI to generate a first reply in a ticket](https://support.zendesk.com/hc/en-us/articles/7041677653914)
- [Turning on suggested first replies](https://support.zendesk.com/hc/en-us/articles/8037936748570)
- [Explore recipe: Reporting on suggested first replies](https://support.zendesk.com/hc/en-us/articles/8037982400794)

## Get agents up to speed with a ticket quickly (Advanced AI)

Ticket summaries, intelligent triage predictions, and enhanced suggested macros work together to give agents AI-powered insights and suggestions to help solve the customer’s issue faster.

<p id="gdcalert3" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image3.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert4">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image3.png "image_tooltip")

Benefits

- Get agents up to speed quickly with a ticket by using the summarize feature to recap all the public comments that have been added to the ticket so far.
- Give agents access to insights on customer intent, sentiment, and language to find the best resolution quickly.
- Suggest the most relevant macros to apply so agents can respond and solve issues faster.

Prerequisites

- You must have the [Advanced AI add-on](https://support.zendesk.com/hc/en-us/articles/*************) and be using [Agent Workspace](https://support.zendesk.com/hc/en-us/articles/4408821259930), [intelligent triage](https://support.zendesk.com/hc/en-us/articles/4964463770650), and (optionally) [suggested macros](https://support.zendesk.com/hc/en-us/articles/*************).

Show me how

- [Viewing intelligent triage predictions](https://support.zendesk.com/hc/en-us/articles/4685355428250)
- [Summarizing ticket comments using generative AI](https://support.zendesk.com/hc/en-us/articles/8037649972634)
- [Applying suggested macros to a ticket](https://support.zendesk.com/hc/en-us/articles/*************)

## Boost agent productivity when responding to tickets (Advanced AI)

Agents can use the expand feature in the composer to save time while responding to a customer. Additionally, agents can change the tone of their comment to make it either more conversational or more professional, depending on the tone dictated by your company’s brand.

<p id="gdcalert4" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image4.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert5">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image4.png "image_tooltip")

Benefits

- Decrease the time agents need to craft a response to a customer request.
- Turn brief bullet points into a comprehensive response that addresses a customer need.
- Enhance the tone of agent responses to maintain brand consistency across your support team.

Prerequisites

- You must have the [Advanced AI add-on](https://support.zendesk.com/hc/en-us/articles/*************) and be using [Agent Workspace](https://support.zendesk.com/hc/en-us/articles/4408821259930).

Show me how

- [Expanding a comment you're composing](https://support.zendesk.com/hc/en-us/articles/5608712782362#topic_r5d_xvc_3xb)
- [Changing the tone of a comment you're composing](https://support.zendesk.com/hc/en-us/articles/5608712782362#topic_w25_xvc_3xb)

## Connect agents with help center content to find answers

Knowledge in the context panel lets agents access suggested content from your knowledge base and community forums, search for external content, and take actions related to content while working on tickets without leaving the ticket interface.

<p id="gdcalert5" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image5.jpg). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert6">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image5.jpg "image_tooltip")

Benefits

- Guide agents to solve more complex customer issues faster based on content from your help center.

Prerequisites

- You must be using [Agent Workspace](https://support.zendesk.com/hc/en-us/articles/4408821259930).

Show me how

- [Searching for content in the knowledge section](https://support.zendesk.com/hc/en-us/articles/4408826700570)
- [Linking, quoting, and pinning content to tickets](https://support.zendesk.com/hc/en-us/articles/5780128753946)
- [Flagging articles in Knowledge](https://support.zendesk.com/hc/en-us/articles/4408826160922)
- [Creating and requesting articles in Knowledge](https://support.zendesk.com/hc/en-us/articles/4408835161114)

## Help agents address common issues consistently

Suggested macros help agents find existing macros to apply to a ticket based on the content of that specific ticket.

<p id="gdcalert6" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image6.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert7">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image6.png "image_tooltip")

Benefits

- Give agents the right macros to quickly find resolutions for customers, all from their centralized workspace.

Prerequisites

- Your account must have [enough macro usage data](https://support.zendesk.com/hc/en-us/articles/*************#topic_ktj_hlv_hrb).

Show me how

- [Enabling and disabling suggested macros](https://support.zendesk.com/hc/en-us/articles/*************)
- [Using suggested macros](https://support.zendesk.com/hc/en-us/articles/*************)

More information

- [Macros resources](https://support.zendesk.com/hc/en-us/articles/*************)

## Generate answers with the click of a button (Advanced AI)

Quick answers for Agent Workspace provide AI-generated answers to searches within the Knowledge section of the context panel. Agents can then copy the answer directly into the ticket or click the link to the source article that contains the generated answer.

<p id="gdcalert7" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image7.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert8">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image7.png "image_tooltip")

Benefits

- Automatically generate answers to questions that agents search for in the Knowledge section of the context panel, saving them valuable time.
- Quickly share generated answers with customers with just a few clicks, increasing resolution times and satisfaction ratings.

Prerequisites

- You must have the [Advanced AI add-on](https://support.zendesk.com/hc/en-us/articles/*************) and an active help center.

Show me how

- [Using quick answers for generative search in tickets](https://support.zendesk.com/hc/en-us/articles/6942763726106)
- [Turning on quick answers for Agent Workspace](https://support.zendesk.com/hc/en-us/articles/8079579364250)

## Suggest tickets that can be merged together (Advanced AI)

Merging suggestions identify tickets that can be merged with the ticket the agent is currently working on. These suggestions appear in the context panel within a ticket.

<p id="gdcalert8" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image8.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert9">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image8.png "image_tooltip")

Benefits

- Proactively identify tickets to be merged into the ticket an agent is currently working on, helping them solve multiple customer requests at once.

Prerequisites

- You must have the [Advanced AI add-on](https://support.zendesk.com/hc/en-us/articles/*************).

Show me how

- [Merging related tickets based on suggestions](https://support.zendesk.com/hc/en-us/articles/6885971957914)

## Help agents wrap up calls faster (Advanced AI)

Generative AI for voice allows you to transcribe and summarize voice call recordings in Zendesk Talk using generative AI. This frees up agents from having to manually write call notes during and after a call. Instead, they can focus directly on solving customer problems and move efficiently from call to call.

<p id="gdcalert9" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image9.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert10">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image9.png "image_tooltip")

Benefits

- Automatically convert call recordings to text and save the transcript to the ticket conversation log for added context after a call ends.
- Create a concise, AI-generated summary of the call transcript and save the summary to the ticket conversation log.

Prerequisites

- You must have the [Advanced AI add-on](https://support.zendesk.com/hc/en-us/articles/*************).

Show me how

- [Using generative AI to create call summaries on tickets](https://support.zendesk.com/hc/en-us/articles/6170157307162)

## Using AI to route tickets and provide faster service

Generally speaking, the faster service you provide, the happier your customers will be. Use Zendesk’s AI features to quickly route tickets to the right agents the first time, saving an average of 45 seconds per ticket compared to manual triage. Even better, deflect easily answered tickets altogether, giving your agents time to focus on more complex customer interactions.

Plus, supply agents with the right words to address commonly asked customer questions quickly. You can even give them a helping hand with just-in-time internal guidance about how to handle tricky tickets.

## Route tickets based on customer needs (Advanced AI)

Using intelligent triage, you can automatically route Support and messaging tickets to the right teams based on what the ticket is about (its intent), the language it's written in, whether the customer's message is positive or negative (its sentiment), or a combination of all three.

<p id="gdcalert10" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image10.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert11">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image10.png "image_tooltip")

Benefits

- Eliminate manual triage by automatically categorizing incoming requests with the customer’s intent, language, and sentiment, saving 30-60 seconds on each request.
- Power automated routing workflows that send incoming requests to the appropriate agent the first time.
- Automate responses to customers, allowing them to self-serve and solve their own requests.
- Proactively request missing information so agents can save time and manual effort.
- Get deeper reporting insights to better understand your customers and optimize your operations.

Prerequisites

- You must have the [Advanced AI add-on](https://support.zendesk.com/hc/en-us/articles/*************).

Show me how

- [Automatically detecting customer intent, language, and sentiment](https://support.zendesk.com/hc/en-us/articles/*************)
- [Choosing a routing method for automatically triaged tickets](https://support.zendesk.com/hc/en-us/articles/*************)

Examples

- [Intelligent triage use cases and workflows](https://support.zendesk.com/hc/en-us/articles/*************)

Best practices

- [Analyzing intelligent triage results and taking action](https://support.zendesk.com/hc/en-us/articles/*************)

More information

- [Intelligent triage resources](https://support.zendesk.com/hc/en-us/articles/*************)

## Deflect easily answered customer requests (Advanced AI)

Autoreplies with intelligent triage let you create custom responses to customer email requests based on AI predictions about intent, language, and sentiment. You build autoreplies with intelligent triage using the Autoreply trigger action.

<p id="gdcalert11" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image11.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert12">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image11.png "image_tooltip")

Benefits

- Automatically deflect a greater share of customer queries, freeing agents up to work on more complex customer requests.

Prerequisites

- You must have the [Advanced AI add-on](https://support.zendesk.com/hc/en-us/articles/*************) and be using [intelligent triage](https://support.zendesk.com/hc/en-us/articles/4964463770650).

Show me how

- [Adding a public comment to a ticket using a trigger](https://support.zendesk.com/hc/en-us/articles/*************#topic_ky5_qwg_xyb)

More information

- [Resources for autoreplies](https://support.zendesk.com/hc/en-us/articles/*************)

## Identify needed macros and create them for agents (Advanced AI)

Macro suggestions for admins makes it easier to determine which macros will be most useful for your agents and end users. This feature suggests new macros that admins might want to create based on repeated content from all agent replies in your account.

<p id="gdcalert12" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image12.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert13">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image12.png "image_tooltip")

Benefits

- Receive suggestions to create shared macros to help agents respond faster and more consistently.
- Identify knowledge gaps in macros and optimize them to provide the most relevant response.
- Reduce the time spent on analysis and promote seamless collaboration between teams.

Prerequisites

- You must have the [Advanced AI add-on](https://support.zendesk.com/hc/en-us/articles/*************).

Show me how

- [Creating macros from macro suggestions for admins](https://support.zendesk.com/hc/en-us/articles/*************)

More information

- [Macros resources](https://support.zendesk.com/hc/en-us/articles/*************)

## Provide internal guidance to agents (Advanced AI)

You can create a trigger that automatically adds an internal note to a submitted ticket that meets certain conditions. For example, you could configure a trigger to look for tickets with a negative sentiment and add an internal note that gives the agent information about handling or escalating delicate customer service situations.

<p id="gdcalert13" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image13.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert14">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image13.png "image_tooltip")

Benefits

- Provide agents with helpful process reminders or other information, speeding up agent onboarding and supporting ongoing training.

Prerequisites

- You must have the [Advanced AI add-on](https://support.zendesk.com/hc/en-us/articles/*************) and (optionally) be using [intelligent triage](https://support.zendesk.com/hc/en-us/articles/4964463770650).

Show me how

- [Adding an internal note to a ticket using a trigger](https://support.zendesk.com/hc/en-us/articles/*************#topic_kvx_rwg_xyb)

## Using AI to strengthen your knowledge base and deliver answers

Up-to-date knowledge is key to any successful customer service experience. Help your agents deliver the right answers based on content you’ve already created in your help center.

Create new content quickly and easily with AI tools that help you expand on points, choose the right tone, and simplify word choice. Then build on that content by identifying gaps and other opportunities for improvement that help you provide a better experience for your self-serve customers.

Finally, help customers and agents find the content they need with semantic search that intuitively understands what they’re looking for.

## Reply to customer requests with article suggestions

Standard autoreplies, also called autoreplies with articles, are automated responses to customer requests sent through an email or web form. The responses include suggested help center articles to help customers resolve their issues.

<p id="gdcalert14" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image14.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert15">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image14.png "image_tooltip")

Benefits

- Automatically deflect customer queries that can be answered by your help center content, freeing agents up to work on more complex customer requests.

Prerequisites

- You must have [activated your help center](https://support.zendesk.com/hc/en-us/articles/4408846795674).

Show me how

- [Configuring email autoreplies to deflect requests](https://support.zendesk.com/hc/en-us/articles/4408825385242)
- [Using autoreplies with articles for web forms](https://support.zendesk.com/hc/en-us/articles/*************)

Best practices

- [Optimizing your articles for autoreplies](https://support.zendesk.com/hc/en-us/articles/4408845739162)

More information

- [Resources for autoreplies](https://support.zendesk.com/hc/en-us/articles/*************)

## Create help center content quickly and effectively (Advanced AI)

In the help center, the expand feature helps you quickly and efficiently create expanded content for articles and content blocks. You can change the tone of the content you’re writing to make it either more conversational or more professional, depending on your company’s brand. You can also simplify your content to make it easier to read.

<p id="gdcalert15" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image15.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert16">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image15.png "image_tooltip")

Benefits

- Create articles from short notes or bullet points to speed up content creation.
- Apply a consistent brand voice across your help center content.
- Simplify your articles by removing unnecessary words that slow down reader comprehension.

Prerequisites

- You must have the [Advanced AI](https://support.zendesk.com/hc/en-us/articles/*************) add-on.

Show me how

- [Using generative AI to expand and enhance the tone of help center content](https://support.zendesk.com/hc/en-us/articles/6267360002714)

## Find gaps in your help center based on common tickets

Content cues use machine learning technology and Guide article usage data to help you discover opportunities and tasks that will improve your knowledge base health.

<p id="gdcalert16" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image16.jpg). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert17">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image16.jpg "image_tooltip")

Benefits

- Improve your knowledge base by reviewing suggestions for articles that might need to be created or updated based on common customer requests.

Prerequisites

- See the [Content Cues account requirements](https://support.zendesk.com/hc/en-us/articles/*************#topic_gjw_r15_rhb).

Show me how

- [Understanding content cues](https://support.zendesk.com/hc/en-us/articles/*************)

## Improve help center search results

Semantic search generates the most accurate search results possible by capturing the meaning of search queries, helping users locate content without prior knowledge of the exact keywords to use.

<p id="gdcalert17" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image17.jpg). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert18">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image17.jpg "image_tooltip")

Benefits

- End users can find the information they’re looking for more easily, increasing the deflection power of your help center.
- Agents can be more efficient as they can find answers more quickly.

Prerequisites

- You must have [activated your help center](https://support.zendesk.com/hc/en-us/articles/4408846795674).
- Zendesk must have rolled out semantic search to your help center. See [How to check if your help center is enabled for semantic search](https://support.zendesk.com/hc/en-us/articles/5633225532826#topic_h42_c1y_3xb).

Show me how

- [About semantic search and how it works](https://support.zendesk.com/hc/en-us/articles/5633225532826)

## Using AI to provide 24/7 service and build smarter AI agents

When your customers span the globe, every hour of the day is an opportunity to provide great customer service. Zendesk AI agents let you do just that. Provide 24/7 service with conversation bots that guide your customers to the right solutions without agent intervention.

Go further with AI agents that help you personalize the service experience and understand your customers’ needs at a deeper level, helping you provide service without needing to increase your agent headcount. Plus, build these bots quickly and fill knowledge gaps with AI-powered intents that help take the guesswork out of bot building.

## Provide 24/7 customer service using conversation bots

Conversation bots work with Zendesk’s messaging channels to deliver automated conversational support to your customers. They are highly customizable and easy to build using the drag-and-drop bot builder to guide customers to a resolution.

<p id="gdcalert18" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image18.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert19">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image18.png "image_tooltip")

Benefits

- Manage higher volumes without adding staff by letting the bot solve common issues without an agent getting involved, or by facilitating handoffs to agents by gathering key information from customers first.
- Provide an always-available resource to give customers instant answers and resolve issues faster.

Show me how

- [Creating a conversation bot for your web and mobile channels](https://support.zendesk.com/hc/en-us/articles/4408824263578)
- [Building a conversation bot using answers](https://support.zendesk.com/hc/en-us/articles/4422584657434)

More information

- [Zendesk bot resources](https://support.zendesk.com/hc/en-us/articles/4408834322842)

## Generate conversational answers using help center content

Generative replies can immediately begin to respond to questions from your end users. These responses use generative AI to evaluate articles in your help center, then use that knowledge to provide concise answers within the ongoing conversation. End users get the information they need without leaving the conversation channel to read an article.

<p id="gdcalert19" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image19.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert20">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image19.png "image_tooltip")

Benefits

- Automatically resolve customer inquiries using your existing help center content, without the need for manually configuring answers upfront.

Prerequisites

- You must be using [Agent Workspace](https://support.zendesk.com/hc/en-us/articles/4408821259930) and [messaging](https://support.zendesk.com/hc/en-us/articles/*************).
- An active Zendesk knowledge base must be connected to the bot's [assigned brand](https://support.zendesk.com/hc/en-us/articles/6447052708762).

Show me how

- [Using AI to generate replies in a conversation bot](https://support.zendesk.com/hc/en-us/articles/6138268212634)

## Make your bots sound more human

Bot personas let you select and apply a personality to a conversation bot’s AI-generated responses. You can choose from a number of personas, including:

- Professional, a polite, direct voice.
- Friendly, a casual, approachable voice.
- Playful, a lighthearted, charming voice.

<p id="gdcalert20" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image20.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert21">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image20.png "image_tooltip")

## Build more effective conversation bots

Suggested intents help your bot accurately understand your customers’ needs by suggesting intents when you’re creating an answer for a conversation bot. These suggested, or pre-trained, intents can be used in place of training phrases to help your conversation bot match and deliver the most relevant answer for a customer's question.

<p id="gdcalert21" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image21.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert22">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image21.png "image_tooltip")

Benefits

- Save admins manual setup time because the bots come pre-trained with intents for smarter service conversations in your industry. That means they’re available out of the box without months of manual setup to help you scale exceptional service with ease.
- Automatically detect and classify requests based on the customer’s intent to deliver the most accurate answer, resulting in a better customer experience and higher deflection rate for your business.

Prerequisites

- You must have the [Advanced AI add-on](https://support.zendesk.com/hc/en-us/articles/*************) and be using [intelligent triage](https://support.zendesk.com/hc/en-us/articles/4964463770650) to use intents in more than three answers per bot.

Show me how

- [Adding suggested intents to answers](https://support.zendesk.com/hc/en-us/articles/5537827011994#topic_bsl_wsp_jxb)

<p id="gdcalert22" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image22.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert23">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image22.png "image_tooltip")

Benefits

- Provide a more natural and engaging customer experience on bot-deployed messaging channels.

Prerequisites

- You must be using [Agent Workspace](https://support.zendesk.com/hc/en-us/articles/4408821259930) and [messaging](https://support.zendesk.com/hc/en-us/articles/*************).

Show me how

- [Using bot personas to add personality to AI-generated responses](https://support.zendesk.com/hc/en-us/articles/6067442759066)

## Fill gaps in your bot’s knowledge

Intent suggestions identify the questions your customers ask most often during bot conversations that don’t match an existing bot answer. This helps you uncover gaps in the information provided by your bot so you can create new answers (or improve existing ones).

<p id="gdcalert23" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image23.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert24">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image23.png "image_tooltip")

Benefits

- Identify knowledge gaps in conversation bots and suggest unused intents that can improve ticket deflection.
- Give your customers a better support experience by avoiding the “Sorry, I didn’t get that” fallback message.

Prerequisites

- You must have the [Advanced AI add-on](https://support.zendesk.com/hc/en-us/articles/*************), be using [intelligent triage](https://support.zendesk.com/hc/en-us/articles/4964463770650), and be using a bot in a [supported language](https://support.zendesk.com/hc/en-us/articles/5537827011994#topic_bz4_24x_bvb).

Show me how

- [Creating answers based on intent suggestions](https://support.zendesk.com/hc/en-us/articles/5537827011994#topic_utd_wsp_jxb)""""

Context B: Overview of AI agents
"""<!-----

Conversion time: 0.826 seconds.

Using this Markdown file:

1. Paste this output into your source file.
2. See the notes and action items below regarding this conversion run.
3. Check the rendered output (headings, lists, code blocks, tables) for proper
   formatting and use a linkchecker before you publish this page.

Conversion notes:

- Docs to Markdown version 1.0β40
- Wed Dec 18 2024 21:32:01 GMT-0800 (PST)
- Source doc: Overview of AI agents
- Tables are currently converted to HTML tables.
  ----->

The automated resolutions pricing model for [AI agents](https://support.zendesk.com/hc/en-us/articles/6970583409690) is now live. See [About the latest pricing plan](https://support.zendesk.com/hc/en-us/articles/*************#topic_fy3_fzc_1cc) for more information.

AI agents are the next generation of AI-powered bots that automate and resolve your customers’ issues across service channels. AI agents functionality is included in Zendesk Suite or Support plans, along with a number of [automated resolutions](https://support.zendesk.com/hc/en-us/articles/*************) used to measure usage of AI agents.

The Advanced AI add-on also offers AI features to assist agents and optimize operations for admins. To compare AI agents and Advanced AI features, see [Zendesk AI and Advanced AI at a glance](https://support.zendesk.com/hc/en-us/articles/5608652527386#h_01HH0BTGDG5KZPGRD3V3R3BZ6P).

This article includes the following sections:

- [AI agents capabilities and requirements](https://support.zendesk.com/hc/en-us/articles/6970583409690-Overview-of-AI-agents#topic_lrn_rwr_y1c)
- [Finding more information](https://support.zendesk.com/hc/en-us/articles/6970583409690-Overview-of-AI-agents#topic_kqn_rp1_ccc)

Related articles:

- [About automated resolutions for AI agents](https://support.zendesk.com/hc/en-us/articles/*************)
- [Moving to automated resolutions from existing bot pricing plans](https://support.zendesk.com/hc/en-us/articles/*************)
- [Monitoring your automated resolution usage](https://support.zendesk.com/hc/en-us/articles/*************)
- [Turning off automated resolution features](https://support.zendesk.com/hc/en-us/articles/*************)
- [On-demand: Learn how to use AI agents](https://training.zendesk.com/on-demand-learn-how-to-use-ai-agents) (Free training course on AI-powered intents and autoreplies)

## AI agents capabilities and requirements

AI agents can handle queries across multiple Zendesk support channels using AI and automation tools.

**Note:** AI agents are available on Suite plans, and accounts with Zendesk Support + Chat with [messaging activated](https://support.zendesk.com/hc/en-us/articles/*************).

The following table describes the features included with AI agents and any additional requirements.

<table>
  <tr>
   <td><strong>Feature</strong>
   </td>
   <td><strong>Description</strong>
   </td>
   <td><strong>Channel availability</strong>
   </td>
   <td><strong>Additional requirements</strong>
   </td>
  </tr>
  <tr>
   <td><strong>Autoreplies with articles</strong>
   </td>
   <td>Suggest relevant help center articles based on customer queries sent through email, web form, API, and Web Widget (Classic).
<p>
See:
<ul>

<li><a href="https://support.zendesk.com/hc/en-us/articles/*************">Using autoreplies to in emails</a></li>

<li><a href="https://support.zendesk.com/hc/en-us/articles/*************">Using autoreplies in web forms</a></li>
</ul>
   </td>
   <td>
<ul>

<li>Email</li>

<li>API</li>

<li>Web form</li>

<li>Web Widget (Classic)</li>

<li>Messaging</li>
</ul>
   </td>
   <td>
<ul>

<li>Help center</li>
</ul>
   </td>
  </tr>
  <tr>
   <td><strong>AI-powered intents</strong>
   </td>
   <td>Automate requests with out-of-the-box intents. You can assign intents to answers to save time and increase bot accuracy. See <a href="https://support.zendesk.com/hc/en-us/articles/5537827011994#topic_v12_blx_xbc">Reviewing and assigning intents to answers</a>.
   </td>
   <td>
<ul>

<li>Messaging</li>
</ul>
   </td>
   <td>
<ul>

<li><a href="https://support.zendesk.com/hc/en-us/articles/5537827011994#topic_bz4_24x_bvb">Matching intent model</a></li>
</ul>
   </td>
  </tr>
  <tr>
   <td><strong>Intent suggestions</strong>
   </td>
   <td>Identify commonly-asked but unanswered questions from customers to help you fill any gaps in your bot’s knowledge. See <a href="https://support.zendesk.com/hc/en-us/articles/5537827011994#topic_eny_wnm_bbc">Reviewing top customer intents without answers</a>.
   </td>
   <td>
<ul>

<li>Messaging</li>
</ul>
   </td>
   <td>
<ul>

<li>Matching intent model</li>
</ul>
   </td>
  </tr>
  <tr>
   <td><strong>Answers</strong>
   </td>
   <td>Create custom, predefined responses to customer questions, from suggesting help center articles to connecting to your business systems and automating tasks for customers.
<p>
See:
<ul>

<li><a href="https://support.zendesk.com/hc/en-us/articles/4408824263578">Creating a conversation bot</a></li>

<li><a href="https://support.zendesk.com/hc/en-us/articles/4422584657434">Creating answers</a></li>
</ul>
   </td>
   <td>
<ul>

<li>Messaging</li>
</ul>
   </td>
   <td> 
   </td>
  </tr>
  <tr>
   <td><strong>Generative replies</strong>
   </td>
   <td>Reply to customer queries with answers generated from your knowledge base. See <a href="https://support.zendesk.com/hc/en-us/articles/6138268212634">Using AI-generated replies in a conversation bot</a>.
   </td>
   <td>
<ul>

<li>Messaging</li>
</ul>
   </td>
   <td> 
   </td>
  </tr>
  <tr>
   <td><strong>Bot personas</strong>
   </td>
   <td>Set the bot’s tone of voice to reflect your brand. See <a href="https://support.zendesk.com/hc/en-us/articles/6067442759066">Using bot personas to add personality to AI-generated responses</a>.
   </td>
   <td>
<ul>

<li>Messaging</li>
</ul>
   </td>
   <td> 
   </td>
  </tr>
  <tr>
   <td><strong>AI agents reporting in the Insights dashboard</strong>
   </td>
   <td>Evaluate the performance of your AI agents to understand the impact on your business and gain insights on where to further optimize. See <a href="https://support.zendesk.com/hc/en-us/articles/6847708774554">Using the Insights dashboard</a>.
   </td>
   <td>
<ul>

<li>All channels</li>
</ul>
   </td>
   <td> 
   </td>
  </tr>
  <tr>
   <td><strong>Autoreplies based on intelligent triage*</strong>
<p>
<em>*This feature requires the Advanced AI add-on</em>
   </td>
   <td>Create custom responses to customer email requests based on AI predictions including intent, language, and sentiment. See <a href="https://support.zendesk.com/hc/en-us/articles/5501378527898">Using autoreplies based on intelligent triage</a>.
   </td>
   <td>
<ul>

<li>Email</li>

<li>API</li>

<li>Web form</li>
</ul>
   </td>
   <td>
<ul>

<li>Matching intent model (intent-based triggers only)</li>
</ul>
   </td>
  </tr>
</table>

**Note:** Some features are not available in all Zendesk-supported languages. See [Languages supported in AI agents](https://support.zendesk.com/hc/en-us/articles/4408846760346) for more information.

## Finding more information

If you have feedback or questions related to AI agents, visit our [community forum](https://support.zendesk.com/hc/en-us/community/posts/*************) where we collect and manage customer product feedback. For general assistance with your Zendesk products, contact [Zendesk Customer Support](https://support.zendesk.com/hc/en-us/articles/*************).
"""

Context C: About automated resolutions for AI agents
"""

#

**About automated resolutions for AI agents**

---

Follow

Follow

---

<p id="gdcalert1" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image1.jpg). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert2">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image1.jpg "image_tooltip")

**[Aimee Spanier](https://support.zendesk.com/hc/en-us/profiles/1263082050629)**

Zendesk Documentation Team

Edited Dec 11, 2024

<p id="gdcalert2" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image2.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert3">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image2.png "image_tooltip")

The automated resolutions pricing model for [AI agents](https://support.zendesk.com/hc/en-us/articles/6970583409690) is now live. See [About the latest pricing plan](https://support.zendesk.com/hc/en-us/articles/*************-About-automated-resolutions-for-AI-agents#topic_fy3_fzc_1cc) for more information.

[AI agents](https://support.zendesk.com/hc/en-us/articles/6970583409690) are AI-powered bots designed to resolve customer support requests without input from live agents. AI agent usage is measured by _automated resolutions_. This article provides an overview of the automated resolutions pricing model for AI agents.

This article contains the following sections:

- [About the latest pricing plan](https://support.zendesk.com/hc/en-us/articles/*************-About-automated-resolutions-for-AI-agents#topic_fy3_fzc_1cc)
- [Understanding how automated resolutions are measured](https://support.zendesk.com/hc/en-us/articles/*************-About-automated-resolutions-for-AI-agents#topic_m1n_sq4_jwb)
- [Understanding the default allocation of automated resolutions per plan and adding more](https://support.zendesk.com/hc/en-us/articles/*************-About-automated-resolutions-for-AI-agents#topic_fcr_sq4_jwb)
- [Finding more information](https://support.zendesk.com/hc/en-us/articles/*************-About-automated-resolutions-for-AI-agents#topic_kqn_rp1_ccc)

Related articles:

- [Overview of AI agents](https://support.zendesk.com/hc/en-us/articles/6970583409690)
- [Moving to automated resolutions from existing bot pricing plans](https://support.zendesk.com/hc/en-us/articles/*************)
- [Managing your automated resolutions](https://support.zendesk.com/hc/en-us/articles/*************)
- [Turning off automated resolution features](https://support.zendesk.com/hc/en-us/articles/*************)
- [On-demand: Learn how to use AI agents](https://training.zendesk.com/on-demand-learn-how-to-use-ai-agents) (Free training course on AI-powered intents and autoreplies)

##

About the latest pricing plan

The latest pricing for AI agents measured by automated resolutions was introduced on August 14, 2024. For details, see AI agents in the comparison table on the [Zendesk Suite plans page](https://www.zendesk.com/pricing/#compare-plans).

The automated resolution pricing and overage calculation takes effect on accounts using AI agent features based on several factors, including:

- When the account implemented Zendesk bots or AI agents
- Account renewal date
- Previous usage commitments, such as Answer Bot resolutions or Zendesk bot monthly active users (MAUs)

In addition to the new pricing, accounts created before the introduction of AI agents and automated resolutions will see changes to their account management pages in Admin Center. For information on these UI changes, see [Moving to automated resolutions from existing bot pricing plans](https://support.zendesk.com/hc/en-us/articles/*************#topic_igx_zhv_lcc).

The following table describes the configuration options and history that can affect when the new pricing is applied to an account that is using AI agent features, including:

- **Account creation date**: When the account was created. Refers to the creation date of a permanent account, or a trial account that became a permanent account.
- **Used Zendesk bots**: Was the account using Zendesk bot features before the introduction of AI agents?
- **Pre-existing bot usage commitment**: Is the account using [Answer Bot resolutions](https://support.zendesk.com/hc/en-us/articles/*************) or Zendesk bot (MAUs) to measure bot usage?
- **Changes to pre-existing bot commitment after April 16, 2024**: Were there changes to a pre-existing bot usage commitment agreement after April 16, 2024?

Admins can determine account creation dates, renewal dates, and bot usage commitments by [viewing their plan subscription information](https://support.zendesk.com/hc/en-us/articles/*************) in Admin Center.

<table>
  <tr>
   <td>
<strong>Account creation date</strong>
   </td>
   <td>
<strong>Used Zendesk bots?</strong>
   </td>
   <td>
<strong>Pre-existing bot usage commitment?</strong>
   </td>
   <td>
<strong>Changes to pre-existing bot usage commitment after April 16, 2024?</strong>
   </td>
   <td>
<strong>Automated resolution and overage pricing application date</strong>
   </td>
  </tr>
  <tr>
   <td>
On or after April 16, 2024
   </td>
   <td>
No
   </td>
   <td>
N/A
   </td>
   <td>
N/A
   </td>
   <td>
August 14, 2024
   </td>
  </tr>
  <tr>
   <td rowspan="4" >
Before April 16, 2024
   </td>
   <td>
No
   </td>
   <td>
No
   </td>
   <td>
N/A
   </td>
   <td>
August 14, 2024
   </td>
  </tr>
  <tr>
   <td>
Yes
   </td>
   <td>
No
   </td>
   <td>
N/A
   </td>
   <td>
First account renewal date after August 14, 2024
   </td>
  </tr>
  <tr>
   <td>
Yes
   </td>
   <td>
Yes
   </td>
   <td>
No
   </td>
   <td>
First account renewal date after October 16, 2024
   </td>
  </tr>
  <tr>
   <td>
Yes
   </td>
   <td>
Yes
   </td>
   <td>
Yes
   </td>
   <td>
October 16, 2024
   </td>
  </tr>
</table>

Additionally, on November 6, 2024, customers currently using Answer Bot resolutions or Zendesk bot MAUs will see the following changes in their accounts:

- Any unused Answer Bot resolutions or Zendesk bot monthly active users will be converted to automated resolutions.
- A new [automated resolutions dashboard](https://support.zendesk.com/hc/en-us/articles/*************) will be available to track your usage, replacing the existing Answer Bot resolution and Zendesk bot MAU dashboards.

##

Understanding how automated resolutions are measured

An automated resolution is counted when a customer’s issue is successfully resolved _without_ live-agent intervention, by a conversation bot or by an autoreply via email or web form. Automated resolutions are counted per conversation rather than per user.

There are differences in how and when an issue is deemed resolved, depending on the channel used. This section explains these differences so you can better understand your usage.

**Note:** Automated resolution usage is calculated per conversation, not per end user. A single user’s visits over multiple channels, browsers, or devices will be considered separate interactions.

This section includes the following topics:

- [Automated resolutions in conversation bots](https://support.zendesk.com/hc/en-us/articles/*************-About-automated-resolutions-for-AI-agents#topic_dcn_13x_4wb)
- [Automated resolutions in autoreplies with articles](https://support.zendesk.com/hc/en-us/articles/*************-About-automated-resolutions-for-AI-agents#topic_o2r_13x_4wb)
- [Automated resolutions in autoreplies based on intelligent triage](https://support.zendesk.com/hc/en-us/articles/*************-About-automated-resolutions-for-AI-agents#topic_gww_13x_4wb)
- [Automated resolutions in Web Widget (Classic)](https://support.zendesk.com/hc/en-us/articles/*************-About-automated-resolutions-for-AI-agents#topic_p4p_zqr_y1c)
- [Actions that don’t contribute to automated resolution calculations](https://support.zendesk.com/hc/en-us/articles/*************-About-automated-resolutions-for-AI-agents#topic_v2v_yqr_y1c)

###

Automated resolutions in conversation bots

Automated resolutions are counted in a conversation bot deployed on a messaging Web Widget, as well as on a third-party messaging channel such as Facebook Messenger, X Direct Message, Instagram Direct, LINE, or WhatsApp.

An automated resolution is counted after 72 hours of inactivity if AI evaluation has confirmed that the bot's response was relevant and the last interaction was one of the following:

- The end user provided positive feedback (“Yes, problem solved”)
  - If no feedback is provided, the conversation will be evaluated using AI
- The bot shared help center articles in response to the end user’s request:
  - If articles were shared using Article Recommendation or an article step in an answer flow, the end user must have clicked on at least one article link.
  - If articles were shared in a generative reply, the end user doesn’t have to click on an article for an automated resolution to be counted.
- The end user reached the final step in an answer flow.

The _end user_ is anyone who makes a request and interacts with the bot.

<p id="gdcalert3" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image3.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert4">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image3.png "image_tooltip")

**Note:** No automated resolutions are counted when you test your bot using the [bot testing functionality](https://support.zendesk.com/hc/en-us/articles/4408835784602#topic_ob1_lxx_r5b).

###

Automated resolutions in autoreplies with articles

If you’re using autoreplies with articles in [email notifications](https://support.zendesk.com/hc/en-us/articles/*************) or [web forms](https://support.zendesk.com/hc/en-us/articles/*************-Using-autoreplies-with-articles-for-web-forms), an automated resolution is counted in _either_ of the following situations:

- An article is suggested to the user, and the user clicks “Yes, close my request” in:
  - A feedback prompt on the article page opened via the email notification or web form.
  - A feedback prompt embedded in the email notification or web form.
- An article is suggested to the user, and within 72 hours, the user clicks on the suggested article link _and_
  - The ticket status is marked solved before any user or public agent reply _or_
  - No user or public agent reply has been added to the ticket.

###

Automated resolutions in autoreplies based on intelligent triage

If you’re using [autoreplies based on intelligent triage](https://support.zendesk.com/hc/en-us/articles/5501378527898#topic_mvf_wwg_kxb) in email notifications, an automated resolution is counted when _both_ of the following criteria are met:

- An autoreply is added to a ticket using a trigger based on an intelligent triage condition.
- No user or public agent reply has been added to the ticket in 72 hours.

**Note:** You must have the [Advanced AI add-on](https://support.zendesk.com/hc/en-us/articles/*************) to use autoreplies based on intelligent triage.

###

Automated resolutions in Web Widget (Classic)

If you’re using [Web Widget (Classic) to deliver article recommendations](https://support.zendesk.com/hc/en-us/articles/4408843471642) to your end users, an automated resolution is counted when the bot has suggested at least one article via Article Recommendations and the end user has clicked on at least one article preview link or provided positive feedback “Yes, problem solved”.

The conversation is considered unresolved if:

- The user initiated a live chat
- The user submitted a contact form
- The user requested a callback
- The user provided negative feedback (“No, I still need help”)
- The bot didn’t understand the request

<p id="gdcalert4" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image4.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert5">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image4.png "image_tooltip")

In the Web Widget (Classic), a session ends after 72 hours of inactivity or when the end user closes the browser or tab.

###

Actions that don’t contribute to automated resolution calculations

In most cases, the bot response used in calculating automated resolution is an article recommendation. However, the following actions are not considered when calculating automated resolutions, even though an article may be suggested:

- [Answer Bot for Slack](https://support.zendesk.com/hc/en-us/articles/4408827411098) (Article recommendation)
- [Article Recommendation API](https://support.zendesk.com/hc/en-us/articles/4408831077018)
- [Article Recommendation for Mobile SDK Classic](https://support.zendesk.com/hc/en-us/articles/4408843471642)
- [Article Recommendation for Agents](https://support.zendesk.com/hc/en-us/articles/5581313653530)
- [Zendesk bots for Microsoft Teams](https://support.zendesk.com/hc/en-us/articles/*************#topic_e55_sxr_pzb)

##

Understanding the default allocation of automated resolutions per plan

All Zendesk Suite and Support plans include a baseline number of automated resolutions based on your plan type. You can increase the number of automated resolutions to avoid exceeding your allotted amount, or[pause the AI agent functionality](https://support.zendesk.com/hc/en-us/articles/*************) when you reach your limit in order to prevent overage.

If you want to ensure your account isn't consuming any automated resolutions, you can remove all capabilities related to AI agents. For more information, see [Turning off automated resolution features](https://support.zendesk.com/hc/en-us/articles/*************).

This section includes the following topics:

- [Default automated resolution allocation per plan](https://support.zendesk.com/hc/en-us/articles/*************-About-automated-resolutions-for-AI-agents#topic_hn3_q3x_4wb)
- [Adding automated resolutions to your account](https://support.zendesk.com/hc/en-us/articles/*************-About-automated-resolutions-for-AI-agents#topic_g2n_q3x_4wb)
- [Avoiding automated resolution overage](https://support.zendesk.com/hc/en-us/articles/*************-About-automated-resolutions-for-AI-agents#topic_awx_qp1_ccc)
- [Monitoring automated resolution usage](https://support.zendesk.com/hc/en-us/articles/*************-About-automated-resolutions-for-AI-agents#topic_flr_yp1_ccc)

###

Default automated resolution allocation per plan

Most Zendesk usage plans include a number of automated resolutions. If you’re not part of a Suite or Support plan or find that your plan doesn’t provide enough automated resolutions, you will be able to purchase more as needed.

The following table shows how many automated resolutions are included in each plan. Light agents are not included in the default allocation calculation. Accounts on all plans have a maximum of 10,000 allocated automated resolutions per year, applied [after the new pricing plan takes effect](https://support.zendesk.com/hc/en-us/articles/*************-About-automated-resolutions-for-AI-agents#topic_fy3_fzc_1cc). If you need more automated resolutions, you can [add them to your account](https://support.zendesk.com/hc/en-us/articles/*************-About-automated-resolutions-for-AI-agents#topic_g2n_q3x_4wb).

<table>
  <tr>
   <td>
<strong>Plan</strong>
   </td>
   <td>
<strong>Zendesk Suite</strong>
   </td>
   <td>
<strong>Support (standalone)</strong>
   </td>
  </tr>
  <tr>
   <td>
Enterprise
   </td>
   <td>
15 automated resolutions/agent/month
   </td>
   <td>
15 automated resolutions/agent/month
   </td>
  </tr>
  <tr>
   <td>
Professional
<p>

Growth

   </td>
   <td>
10 automated resolutions/agent/month
   </td>
   <td>
10 automated resolutions/agent/month
   </td>
  </tr>
  <tr>
   <td>
Team
   </td>
   <td>
5 automated resolutions/agent/month
   </td>
   <td>
5 automated resolutions/agent/month
   </td>
  </tr>
</table>

**Note:** Legacy Support plans include access to AI agents but do not come with included automated resolutions. Those customers can purchase automated resolutions as needed.

Your allocation of automated resolutions expires annually or at the end of your subscription term (if less than a year) unless you are on a non-standard subscription term.

###

Adding automated resolutions to your account

After the trial period ends, you can increase the number of automated resolutions to avoid exceeding your allotted amount. Using more than your allotted resolutions can result in overage charges.

You can add to your automated resolution allotment by purchasing 100 or more automated resolutions ahead of time. Also called committed usage, this approach allows you to raise the maximum number of automated resolutions available to you in advance. Committed usage provides a better per-resolution cost than overage billing (or pay-as-you-go).

If you choose an overage billing (or pay-as-you-go) approach to automated resolutions management, the cost per automated resolution over your allotment will be greater than the cost per automated resolution when you buy a fixed number to increase your allotment. You can prevent overage billing by limiting the number of automated resolutions available in your account each month. Overage is billed monthly, regardless of your subscription terms.

###

Avoiding automated resolution overage

You can choose how your account responds when you reach your automated resolution limit.

<p id="gdcalert5" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image5.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert6">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image5.png "image_tooltip")

For more information, see [Maintaining or pausing functionality when you reach your automated resolution limit](https://support.zendesk.com/hc/en-us/articles/*************#topic_iw3_b2h_bdc).

###

Monitoring automated resolution usage

The Automated resolutions dashboard provides a look into how many automated resolutions you’re using. You can view this dashboard in Admin Center (**Account > Usage > Automated resolutions**).

For more information, see [Viewing automated resolution usage in the dashboard](https://support.zendesk.com/hc/en-us/articles/*************#topic_mrg_wdh_bdc).

<p id="gdcalert6" ><span style="color: red; font-weight: bold">>>>>>  gd2md-html alert: inline image link here (to images/image6.png). Store image on your image server and adjust path/filename/extension if necessary. </span><br>(<a href="#">Back to top</a>)(<a href="#gdcalert7">Next alert</a>)<br><span style="color: red; font-weight: bold">>>>>> </span></p>

![alt_text](images/image6.png "image_tooltip")

##

Finding more information

If you have feedback or questions related to AI agents, visit our [community forum](https://support.zendesk.com/hc/en-us/community/posts/*************) where we collect and manage customer product feedback. For general assistance with your Zendesk products, contact [Zendesk Customer Support](https://support.zendesk.com/hc/en-us/articles/*************)
""""
