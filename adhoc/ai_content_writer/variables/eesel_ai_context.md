# **eesel AI: Contextual Information**

## **Important eesel AI URLs**

- URL for the main site: https://eesel.ai
- Customer story blogs URL: https://www.eesel.ai/blog/category/customer-stories
- Other Zendesk AI blogs URL: https://www.eesel.ai/blog/category/zendesk-ai
- Book a demo URL: https://calendly.com/eesel/30
- Start a trial URL: https://dashboard.eesel.ai/api/auth/signup?returnTo=v2

## **Overview**

eesel AI is a platform designed to integrate with your existing helpdesk system to streamline and automate customer support. It offers two main products: an **AI Agent** and an **AI Assistant** (also called the AI Copilot). These tools help manage ticket volume, automate repetitive tasks, and reduce costs, making customer support more efficient.

---

## **Product Details**

### **AI Agent**

The AI Agent is integrated into your helpdesk and offers several features:

- **Tier 1 Ticket Automation**:  
  Automatically resolves simple, repetitive tickets like FAQs or order updates without needing human involvement. Respond in your brand's tone and escalate when necessary.

- **Intelligent Triage**:  
  Routes tickets to the correct agent or department, applies tags, and prioritizes based on urgency.

- **E-commerce Integration**:  
  Fetches order tracking or customer-specific data from platforms like Shopify or Magento. You can set up verification steps to ensure secure information retrieval.

- **Custom API Actions**:  
  Performs advanced tasks by making API calls to retrieve data from internal systems or handle specific customer requests.

- **Escalation Management**:  
  Hands off tickets to human agents when necessary, with all context retained for a seamless transition.

- **Website Integration**:  
  Adds a chat bubble to your website for real-time customer interactions.

- **Multi-Bot Workflows**:  
  Allows creating multiple bots, each trained for specific tasks. Bots can collaborate, such as one bot triaging tickets and another resolving specific queries. Each bot can have different prompting, customization, and integrations.

### **AI Assistant (AI Copilot)**

The AI Assistant works through a browser extension to assist human agents:

- **Draft Replies**:  
  Detects when an agent is working on a ticket and suggests draft responses based on historical data and the knowledge it is trained on.

- **Information Retrieval**:  
  Helps find specific documentation or company policies to respond accurately.

- **Context-Aware Assistance**:  
  Integrates into workflows to provide suggestions tailored to the ticket’s context, saving time.

---

## **Training Capabilities**

eesel AI is designed to deliver accurate and relevant responses by training on various data sources:

- **Supported Sources**:  
  Includes training on past tickets, internal documents (e.g., Google Docs, Confluence), PDFs, external wikis, and over 100 other integrations.

- **Customizable Training**:  
  Aligns with workflows, brand tone, and specific interaction standards by allowing detailed instructions for unique queries. Not limited to preset customization or tone.

- **Simulation and Fine-Tuning**:  
  Simulates responses on past tickets, allowing businesses to fine-tune how bots respond and ensuring accuracy before going live. Test out new prompting before going live, roll out to agents selectively.

---

## **Insights and Reporting**

- **Knowledge Gap Analysis**:  
  Identifies areas where the AI struggled to provide accurate answers, helping improve training data by pinpointing missing sources. AI insights about what kind of knowledge is missing.

- **ROI Calculator**:  
  Quantifies time and financial savings achieved through eesel AI.

- **Deflection rate**:  
  Shows how many queries are effecitvely deflected by AI.

---

## **Additional Products**

- **Livechat AI**:  
  Adds a chat bubble to your website or integrates inline to provide instant customer responses.

- **Teammate AI**:  
  Integrates with collaboration tools like Slack or Teams, giving employees access to up-to-date answers to internal questions.

---

## **Set up information for AI Agent and Assistant**

1. Add an integration to train your bot with knowledge sources, like Confluence, your help center, or Google Docs.
2. Add an integration for your bot to operate in, like your Zendesk or Slack.
3. Customize your bot with tone, greetings, sign offs, and specific escalation parameters, and add actions for it to take.
4. Chat with your bot inside the dashboard to test out how well it synthesizes information and your prompt.
5. Roll out selectively to your agents and watch your support get automated.
6. (optional) Download the extension to draft replies or chat to your bot inside the browser.

---

## **Target Audience**

eesel AI is ideal for companies using platforms like Zendesk, Intercom, or Freshdesk. It is especially useful for:

- Businesses handling a large volume of tickets, including repetitive or seasonal inquiries.
- Teams looking for an affordable alternative to helpdesk-native AI tools.
- Organizations that need customizable workflows and integration options.
- Companies needing scalable solutions for seasonal ticket spikes without hiring additional staff.

---

## **Pain Points Addressed**

### **1. High Costs and Unsustainable Pricing Models**

- **Problem**: Customers face high costs with existing tools like Zendesk AI, particularly due to ambiguous pricing models (e.g., per-resolution charges).
- **How eesel AI Addresses This**:
  - Pay-per-interaction pricing ensures transparency and eliminates surprise costs.
  - No pay-per agent or per-user pricing.
  - Reduces reliance on human agents by automating repetitive tasks, achieving higher ROI.

### **2. Lack of Intelligence and Poor Functionality in Existing Bots**

- **Problem**: Current bots lack intelligence, fail to provide actionable responses, and often escalate all queries to human agents.
- **How eesel AI Addresses This**:
  - Leverages advanced LLMs for context-aware, meaningful responses.
  - Trains on past tickets, FAQs, and SOPs to align with specific workflows.
  - Configurable for empathetic, conversational language to build customer trust.

### **3. High Ticket Volumes and Scaling Challenges**

- **Problem**: Businesses struggle to manage high ticket volumes without increasing staff.
- **How eesel AI Addresses This**:
  - Automates Tier 1 tickets (e.g., FAQs, refunds, password resets).
  - Analyzes past tickets to optimize responses and reduce ticket backlog.
  - Scales effortlessly to handle increasing ticket volumes.
  - Cost effective way of increasing support productivity,
  - Assist existing agents to improve their workflow with the AI assistant.

### **4. Workflow and Integration Limitations**

- **Problem**: Existing tools don’t integrate seamlessly with internal systems or support advanced workflows.
- **How eesel AI Addresses This**:
  - Custom API integrations enable bots to perform tasks like refunds, account updates, or KYC verification.
  - Multi-bot support allows workflows to be separated by brand or team.
  - Flexible tools for automating complex workflows.

### **5. Lack of Actionability or Depth**

- **Problem**: Many bots only repeat help center articles instead of solving problems.
- **How eesel AI Addresses This**:
  - Trained to perform real actions, such as issuing refunds or resetting accounts.
  - Simulates responses to refine accuracy before going live.
  - Escalates complex queries only when necessary, with full context for agents.
  - Train on past tickets and

### **6. Frustration with Vendor Responsiveness**

- **Problem**: Customers experience poor support and unresponsive vendors.
- **How eesel AI Addresses This**:
  - Dedicated support team for onboarding, setup help, and for smooth implementation.
  - Rapid training and integration timelines (1–2 weeks).
  - Regular follow-ups and account reviews ensure continued success.

---

## **Pricing Plans**

### **Team Plan - $299/month**

- For mid-sized teams needing deeper integrations.
- Includes everything in Starter Plan plus:
  - Helpdesk integration (Zendesk, Intercom, etc.).
  - Slack/Teams integration.
  - Detailed performance reports.
  - Support for 3 bots.
  - Up to 1,000 interactions/month.

### **Business Plan - $799/month**

- For larger teams with complex workflows.
- Includes everything in Team Plan plus:
  - Support for 10 bots.
  - Up to 5,000 interactions/month.
  - Priority support.
  - Advanced reporting.

### **Enterprise Plan - Custom Pricing**

- Tailored for large-scale operations.
- Features:
  - Unlimited bots and interactions.
  - Dedicated customer success manager.

---

## **Conclusion**

eesel AI is a comprehensive platform for automating and improving customer support. Its tools address key challenges like ticket volume, training limitations, and high costs. With extensive integrations, customizable workflows, and flexible pricing, eesel AI is a scalable solution for businesses of any size.
