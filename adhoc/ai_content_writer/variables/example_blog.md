# Link Building for SEO: The Beginner’s Guide

You can think of links as votes. When other websites are linking to your page, it tells Google that your page is somehow important. Which is essentially Google’s [PageRank algorithm](https://ahrefs.com/blog/google-pagerank/) in a nutshell.

So the more [high-quality backlinks](https://ahrefs.com/blog/high-quality-backlinks/) a page has, the higher it tends to rank in Google. And if you want to outrank it with your own page, you’ll likely need to get more links than it has.

![Backlinks help you to rank higher in Google](https://ahrefs.com/blog/wp-content/uploads/2023/09/backlinks-help-you-to-rank-higher-in-google.png)

Backlinks help pages rank higher in Google’s search results.

I obviously oversimplified things quite a bit. Ranking #1 in Google is a lot more nuanced than just getting more links, because links aren’t the only ranking signal that Google uses. But it’s [a very strong signal](https://searchengineland.com/now-know-googles-top-three-search-ranking-factors-245882) nevertheless, and it has a very direct influence on your search rankings.

So what is link building, and how do you do it?

Link building is the process of getting other websites to link to pages on your website. Its purpose is to boost the “authority” of your pages in the eyes of Google so that these pages rank higher and bring more search traffic.

Let’s get a bit deeper into that.

Conceptually, most popular link building strategies fall into one of the following four buckets:

1.  **Adding links** – That’s when you go to some website and manually add your link there.
2.  **Asking for links** – That’s when you send emails to owners of relevant websites and ask them to link to you.
3.  **Buying links** – Same as above, but you offer them money (or some other form of compensation).
4.  **Earning links** – This refers to creating and promoting something so noteworthy that people would link to it naturally.

![Four ways to build backlinks](https://ahrefs.com/blog/wp-content/uploads/2023/09/four-ways-to-build-backlinks.png)

How to get backlinks.

You can also hire an experienced link builder (or a link building agency) to do it all for you. And that’s what a lot of digital marketers and business owners eventually end up doing because no matter which tactics you choose, building links is a lot of work.

But even if you decide to outsource link building, it would be immensely useful to have some basic knowledge of how it’s done. This way, you would be able to see if the person you hired is doing a good job or not.

So let’s take a closer look at each of the four buckets.

### 1\. Adding links

This is when you go to a website that doesn’t belong to you and manually place your link there.

The most common tactics that fit into this category are:

- Social profiles creation.
- Business directory submissions.
- Review site listings.
- Posting to forums, communities, and Q&A sites.

Building links via those tactics is very easy to do. And for that exact reason, such links tend to have little to no value in the eyes of Google.

Other than that, these kinds of links barely give you any competitive edge. If you can go to a website and manually place your link there, so can your competitors.

And yet, this group of link building tactics should not be ignored completely. In fact, some professional link builders prefer to start with these kinds of links when they’re working with a brand-new website.

They refer to it as building “foundational links.”

Think about it. Most online businesses have branded accounts at the major social networks, as well as listings at major business directories and review sites (Yelp, Trustpilot, ProductHunt, Glassdoor, etc.). And all of these pages contain a link to their website.

![Link from our Twitter profile](https://ahrefs.com/blog/wp-content/uploads/2023/09/link-from-our-twitter-profile.jpg)

Google clearly pays attention to these profile pages. If you look at the “Knowledge” panel for Ahrefs (see the screenshot below), you’ll notice the links to our social profiles featured there. And we weren’t the ones who added them. Google identified our social profiles on its own and linked them to the Ahrefs brand as part of its [Knowledge Graph](https://support.google.com/knowledgepanel/answer/9787176).

![Links in the "Knowledge" graph](https://ahrefs.com/blog/wp-content/uploads/2023/09/links-in-the-knowledge-graph.png)

Yes, these kinds of links are either [nofollow](https://ahrefs.com/blog/nofollow-links/) or very, very weak. Which means that they hardly move the needle when it comes to ranking in Google.

But given that the “nofollow” attribute is now [treated as a hint](https://developers.google.com/search/blog/2019/09/evolving-nofollow-new-ways-to-identify), there’s a chance that over time your profile pages will accrue some quality links of their own and might start bringing a bit of search engine optimization (SEO) value to your website.

For example, Ahrefs’ profile page on Twitter boasts 11,000 backlinks, coming from over a thousand different websites. So I’m pretty sure it does have some “weight” in the eyes of Google.

![Backlink profile of our Twitter profile, via Ahrefs' Site Explorer](https://ahrefs.com/blog/wp-content/uploads/2023/09/backlink-profile-of-our-twitter-profile-via-ahref.png)

That being said, don’t go crazy listing your website in every imaginable social network and business directory there is. We’re talking about merely a few dozen of them where it is natural for your business to be listed. Anything beyond that would be a royal waste of your time.

And the best way to find some quality websites to add your link to is to study the links of your competitors. Which we’ll discuss in more detail later in this guide.

### 2\. Asking for links

This is when you reach out to other website owners and ask them for a link, which SEOs often refer to as “[link outreach](https://ahrefs.com/blog/outreach/).”

But you can’t possibly reach out to the folks at datasciencecentral.com and ask them to link to your page with cookie recipes, right? You need to pick websites that are somehow related to your page, since they are more likely to actually consider your request.

The process of collating a list of relevant websites to reach out to is called “[link prospecting](https://ahrefs.com/blog/link-prospecting/).” And the more effort you invest into finding suitable outreach targets, the higher your success rate would be.

But why would owners of other websites (even the relevant ones) care to link to your page anyways?

Well, ideally, you want them to be so impressed with your resource that they would naturally want to share it with the visitors of their website (i.e., link to it).

But not every page of your website is a one-of-a-kind masterpiece worthy of a thousand links. So SEO professionals have devised a set of tactics to persuade the owners of other websites to add links to their pages.

Here’s a brief list of these tactics, along with the general reasoning behind them:

- [**Guest posting**](https://ahrefs.com/blog/guest-blogging/) – Write an awesome article for their website, from which you can link to yourself.
- [**Skyscraper technique**](https://ahrefs.com/blog/skyscraper-technique/) – Find an outdated (or somehow inferior) page that lots of websites are linking to. Create a much better one on your own website. Then show it to all the “linkers.”
- [**Resource page link building**](https://ahrefs.com/blog/resource-page-link-building/) – Find pages that list resources similar to yours and request to be added there.
- [**Broken link building**](https://ahrefs.com/blog/broken-link-building/) – Find a dead page that has lots of links. Create an alternative on your own website and ping all the linkers about it. That’s broken link building in a nutshell.
- [**Image link building**](https://ahrefs.com/blog/build-links-with-images/) – Find sites that have used your images without proper attribution and ask them for a link.
- [**HARO**](https://ahrefs.com/blog/haro-link-building/) **and journalist requests** – Contribute an “expert quote” for their article.
- [**Unlinked mentions**](https://ahrefs.com/blog/unlinked-mentions/) – Ask to turn the mention of your brand into a link.
- [**PR**](https://moz.com/blog/easiest-pr-focused-link-building-tip) – Give them a killer story to cover.

Here’s a caveat, though.

The reasoning behind each one of these tactics might seem quite fair and logical, but you’ll be surprised how low the success rate is. I mean, if you manage to get five links out of a hundred outreach emails, you can be proud of yourself.

But there’s one simple thing you can do to skew the odds in your favor. That is to build relationships with people in your industry way before you need something from them.

Think about it. If today you got a cold email from a random person asking for a link, would you even bother replying? I doubt so. But what if that email comes from someone whom you’ve previously talked to on Twitter or maybe even met at some in-person event? You’ll be a lot more likely to pay attention, right?

Thus, if you start connecting with folks from your industry in advance (and maybe even do small favors for them), you’ll have no issues reaching out with a link request at some point in future.

Here’s a good example from my own experience. Gael Breton from Authority Hacker first reached out to me back in 2014. That was before I even joined Ahrefs:

![2014 email from Gael Breton ](https://ahrefs.com/blog/wp-content/uploads/2023/09/2014-email-from-gael-breton.png)

I enjoyed Gael’s work, and we’ve kept in touch ever since. Which is one of the reasons why his website has 122 incoming links from ahrefs.com as of today:

![How many times we're linking to Gael's website from our site, via Ahrefs' Site Explorer](https://ahrefs.com/blog/wp-content/uploads/2023/09/how-many-times-were-linking-to-gaels-website-fro.png)

But don’t get me wrong. Gael didn’t ask me for links every once in a while to get these. We simply follow each other’s work. And when they publish something noteworthy on Authority Hacker, I would know about it and share it with our team. And then we might link to it at some point from our blog.

That’s how relationships help you to acquire links naturally.

### 3\. Buying links

This is the easiest way to build links. A lot of website owners would be happy to link to you if you pay them for it.

But exchanging money (or anything else, really) for links is quite risky. [Google considers it a manipulation of its algorithm](https://developers.google.com/search/docs/essentials/spam-policies#link-spam). And it might punish you for it by kicking your website out of the search results.

Another risk of buying links comes from simply wasting your money on [bad links](https://ahrefs.com/blog/bad-links/) that won’t even work in the first place.

That being said, we don’t want to teach you any tactics that might put your business (or your wallet) at risk. So there would be no tips on “how to buy links the right way” in this guide.

And yet, you should be well aware that many people in the SEO industry do buy links to achieve their ranking goals. Once you start researching your competitors’ backlinks and reaching out to the same websites, you’ll soon find out if they paid for any of their links.

### 4\. Earning links

You “earn” links when other people link to the pages on your website without you having to ask them to do so. This won’t happen unless you have something truly noteworthy that other website owners will genuinely want to mention on their websites.

So here are a few things that can make the pages of your website worthy of a link:

- Your company’s proprietary data
- Results of experiments (which require significant efforts)
- Unique ideas and strong opinions (i.e., thought leadership)
- Industry surveys
- Breaking news

For example, back in 2017, we used our proprietary data to carry out a unique research study, which answered one of SEO’s most frequently asked questions: “[How long does it take to rank in Google?](https://ahrefs.com/blog/how-long-does-it-take-to-rank/)”

As of today, this blog post has almost 3,000 backlinks from about 1,700 different websites.

![Backlink profile for my blog post on how long it takes to rank in Google, via Ahrefs' Site Explorer](https://ahrefs.com/blog/wp-content/uploads/2023/09/backlink-profile-for-my-blog-post-on-how-long-it-r.png)

And even six years later, this research is still picking up new links. Here are a few linked mentions from earlier this year:

![Examples of recent links to my post](https://ahrefs.com/blog/wp-content/uploads/2023/09/examples-of-recent-links-to-my-post.png)

But you don’t necessarily have to create any content at all. Your business in itself can be link-worthy. Or the products and services that you offer.

A fair share of links to the ahrefs.com website come from people mentioning our products and company rather than the content we publish. Here are a few linked mentions we got just yesterday:

![Examples of recent links to our homepage](https://ahrefs.com/blog/wp-content/uploads/2023/09/examples-of-recent-links-to-our-homepage.png)

But people can’t link to things that they don’t know exist. So no matter how awesome your page (or your product) is, you’ll need to promote it. And the more people see your resource, the higher the chance that some of them will end up linking to it.

We’ll talk more about this later in this guide.

Different kinds of links have different impacts on your page’s ranking in Google. And no one knows for sure how exactly Google measures the value of each individual link.

But there are five general concepts of evaluating links that the SEO community believes to be true.

![Five attributes of a good backlink](https://ahrefs.com/blog/wp-content/uploads/2023/09/five-attributes-of-a-good-backlink.png)

What makes a good link.

### 1\. Authority

It seems intuitive that a link from The New York Times and a link from your friend’s small travel blog couldn’t possibly be treated by Google as equals. NYT is a world-famous authority, and your friend’s blog is hardly known even among their friends.

Over many years of building links, SEOs have gathered a lot of empirical evidence that links from more well-known and authoritative websites have a bigger influence on your page’s rankings in Google.

But how do you measure the “authority” of the website?

Well, according to [an industry survey done by Aira](https://aira.net/state-of-link-building/link-building-measurement-and-reporting/), the most popular website authority metrics are Ahrefs’ [Domain Rating (DR)](https://ahrefs.com/blog/domain-rating/) and Moz’s Domain Authority (DA). With internally developed metrics (which often have DR or DA blended in them) holding the third spot.

![Our Domain Rating (DR) metric is the most popular among SEOs, according to Aira's State of Link Building Report 2022](https://ahrefs.com/blog/wp-content/uploads/2023/09/our-domain-rating-dr-metric-is-the-most-popular.png)

The State of Link Building Report 2022.

We actually have a free [Website Authority Checker](https://ahrefs.com/website-authority-checker) tool, which you can use to check the Domain Rating metric of any website:

![Checking DR in our free Website Authority Checker](https://ahrefs.com/blog/wp-content/uploads/2023/09/checking-dr-in-our-free-website-authority-checker.png)

But other than the authority of an entire website, there’s also the authority of the actual page that is linking to you. Which is known to be calculated by Google with the help of the famous [PageRank algorithm](https://ahrefs.com/blog/google-pagerank/).

In simple terms, the PageRank algorithm is based on the premise that pages with more backlinks (and better ones) of their own cast a stronger “vote.”

![How PageRank works](https://ahrefs.com/blog/wp-content/uploads/2023/09/how-pagerank-works.png)

Pages that have backlinks cast a stronger “vote” than those that don’t.

Here at Ahrefs, we have our own metric to measure the authority of the page. It’s called [URL Rating (UR)](https://ahrefs.com/blog/ahrefs-seo-metrics/#section9), and it is calculated in a fairly similar fashion to the original PageRank.

The UR of this very page that you’re reading is 30, and it has backlinks from over a thousand different websites (ref. domains):

![The URL Rating (UR) of this very page](https://ahrefs.com/blog/wp-content/uploads/2023/09/the-url-rating-ur-of-this-very-page.png)

And there’s one more thing you need to know about authority. If a backlink has [a rel=”nofollow” attribute](https://developers.google.com/search/docs/crawling-indexing/qualify-outbound-links) attached to it, it most likely doesn’t cast a “vote” toward a website that it links to.

### 2\. Relevance

If you run a blog about health and fitness, links from other websites (and pages) on the same topic will have more weight in the eyes of Google compared to links from websites about cars or finances.

Here’s an excerpt from Google’s “[How Search Works](https://www.google.com/intl/en_uk/search/howsearchworks/how-search-works/ranking-results/)” guide that corroborates this theory (bolding is mine):

> If **other prominent websites on the subject** link to the page, that’s a good sign that the information is of high quality.

But this doesn’t mean that you should avoid getting links from websites that aren’t on the same topic as yours. I can’t imagine any seasoned SEO saying: “No, please don’t link to my recipe website from dreamhost.com, a hosting website with DR 93.”

The thing is, whatever topic your website is about, there would be dozens of topics that are perfectly relevant while not the same.

For example, nutrition is very important for health and fitness. So it would be perfectly natural for fitness websites to link to articles about food. And if you want to work out regularly, you need to find time in your schedule for it, so linking to articles about time management wouldn’t be unnatural too.

In other words, relevance is a fairly malleable concept. Unless, of course, you try shoehorning links into places where they clearly don’t belong.

### 3\. Anchor text

If you’re not already familiar with the term, “[anchor text](https://ahrefs.com/blog/anchor-text/)” is a clickable snippet of text that links to another page. In many cases, it succinctly describes what the linked page is about.

So it’s no surprise that Google uses the words in the anchor text to better understand what the referenced page is about and what keywords it deserves to rank for. In fact, [Google’s original PageRank patent](http://infolab.stanford.edu/~backrub/google.html) talks about this quite explicitly (bolding is mine):
....

# And the blog continues on. This is only a SNIPPET!
