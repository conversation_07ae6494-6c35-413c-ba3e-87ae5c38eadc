# AI for customer service - Zendesk
**URL**: https://www.zendesk.com/service/ai/

---
title: AI for customer service — Zendesk | Zendesk Australia
url: https://www.zendesk.com/au/service/ai/
hostname: zendesk.com
description: Revolutionise your support operations with Zendesk AI for customer service featuring intelligent bots, enhanced self-service options and agent productivity tools. Begin your free trial today.
sitename: Zendesk
date: 2023-05-06
---
## ARTIFICIAL INTELLIGENCE

# Zendesk AI makes service what it should be

Only Zendesk AI is built on billions of real customer service interactions. It understands customer experience, which means you unlock the power of personalised support from day one — without any extra work.

## HOW ZENDESK AI WORKS

## Intelligence infused into every interaction

## AI-POWERED BOTS New

## Let AI agents pick up the slack

Zendesk AI agents are more intelligent, reliable and independent than the average bot. They provide real relief to human agents and know how to handle more complex interactions because customer service is what they do best.

## AGENT EFFICIENCY

## Turn agents into top performers

Our intelligent AI assistant does more than advise. It takes action, by offering response suggestions and actions to agents based on your business policies.

Get early access## OPTIMISE WORKFLOWS

## Transform your entire customer experience

Automatically know what your customer wants, how they feel and what language they speak. Go further and customise what to detect and capture so the information you care about is always front and centre.

## Testimonials

## AI for employee experience

## Set your internal teams free

Assign repetitive requests to AI agents that come pre-trained on millions of HR and IT interactions. Route sensitive topics to the right teams, allowing everyone to focus on the right tasks.

Get the details## Advanced data privacy and protection

## Enterprise-grade security and privacy

Zendesk AI follows the same standards applied to all Zendesk products because keeping customer data safe is essential. For businesses that need more protection, our Advanced Data Privacy and Protection add-on provides the highest level of security.

Get the details## Frequently asked questions

Customers on Support and Suite Professional plans or above can use Advanced AI.

---

# Getting started with Zendesk AI and Advanced AI – Zendesk help
**URL**: https://support.zendesk.com/hc/en-us/articles/5608652527386-Getting-started-with-Zendesk-AI-and-Advanced-AI

---
title: Getting started with Zendesk AI and Advanced AI
url: https://support.zendesk.com/hc/en-us/articles/5608652527386-Getting-started-with-Zendesk-AI-and-Advanced-AI
hostname: zendesk.com
description: You must have the Advanced AI add-on to use some of the features described in this article. Zendesk AI is the intelligence layer of the Zendesk platform. Built on billions of points of customer-ser...
sitename: Zendesk help
date: 2023-08-21
---
Zendesk AI is the intelligence layer of the Zendesk platform. Built on billions of points of customer-service data, Zendesk AI enhances every part of your service experience, from smarter conversations and AI agents, to productivity tools for agents, to new insights and instant actions for admins.

This article introduces you to all the AI-powered features Zendesk has to offer. It also shows you how to leverage these features to achieve your business goals. It walks through the key items you need to set up and gives you guidelines on where to find more information.

Some AI features are included in the suite, and some require the Advanced AI add-on. To show or hide information about the Advanced AI features throughout this article, use the buttons below.


This article contains the following topics:

- Zendesk AI and Advanced AI at a glance
- Using AI to empower agents and improve efficiency
- Using AI to route tickets and provide faster service
- Using AI to strengthen your knowledge base and deliver answers
- Using AI to provide 24/7 service and build smarter AI agents

Related articles:

## Zendesk AI and Advanced AI at a glance

Zendesk offers two levels of AI functionality to meet your business needs:

-
**Zendesk AI**, included with standalone product and Suite plans, with specific features varying by plan level -
**Zendesk Advanced AI**, available as an add-on for Suite and Support Professional and above plans

The table below summarizes the features included in each level.

Automate customer interactions with AI agents | Assist agents to solve issues faster | Optimize your service workflows | |

Included with Suite plans, with specific features varying by plan level |
Suggested intents when creating answers Intent suggestions for unanswered questions |
Suggested macros for agents |
Content Cues for content managers Semantic search in the help center |

Available as an add-on for Suite and Support Professional and above |
Autoreplies with intelligent triage |
Macro suggestions for admins Autoreplies and Internal note trigger actions |


## Using AI to empower agents and improve efficiency

Your agents have a lot on their plates these days. Give them the tools they need to succeed right out of the gate with a roundup of AI-derived intelligence, such as a ticket summary and user sentiment, in an easily accessible side panel.

Connect them with your help center to give them the answers they need to answer customer questions without having to leave their workspace. And give them the right words to use when answering customer questions, helping your customers enjoy a consistent experience no matter which agent they’re connected with.

Intelligence in the context panel is an agent-facing panel in the Agent Workspace that shows agents AI-powered insights and suggests the right macros to apply to solve the customer’s issue.

**Benefits**

-
**Get agents up to speed quickly with a ticket**by using the summarize feature to recap all the public comments that have been added to the ticket so far. -
**Give agents access to insights**on customer intent, sentiment, and language to find the best resolution quickly. -
**Suggest the most relevant macros to apply**so agents can respond and solve issues faster. -
**Help agents ramp up quickly and improve**by giving them AI-powered guidance on customer context next steps.

**Prerequisites**

- You must have the Advanced AI add-on and be using Agent Workspace, intelligent triage, and (optionally) suggested macros.

**Show me how**

Agents can use the expand feature in the composer to save time while responding to a customer. Additionally, agents can change the tone of their comment to make it either more conversational or more professional, depending on the tone dictated by your company’s brand.

**Benefits**

-
**Decrease the time agents need to craft a response**to a customer request. -
**Turn brief bullet points into a comprehensive response**that addresses a customer need. -
**Enhance the tone of agent responses**to maintain brand consistency across your support team.

**Prerequisites**

- You must have the Advanced AI add-on and be using Agent Workspace.

**Show me how**

Knowledge in the context panel lets agents access suggested content from your knowledge base and community forums, search for external content, and take actions related to content while working on tickets without leaving the ticket interface.

**Benefits**

-
**Guide agents to solve more complex customer issues faster**based on content from your help center.

**Prerequisites**

- You must be using Agent Workspace.

**Show me how**

Suggested macros help agents find existing macros to apply to a ticket based on the content of that specific ticket.

**Benefits**

-
**Give agents the right macros to quickly find resolutions for customers**, all from their centralized workspace.

**Prerequisites**

- Your account must have enough macro usage data.

**Show me how**

**More information**

Generative AI for voice allows you to transcribe and summarize voice call recordings in Zendesk Talk using generative AI. This frees up agents from having to manually write call notes during and after a call. Instead, they can focus directly on solving customer problems and move efficiently from call to call.

**Benefits**

-
**Automatically convert call recordings to text**and save the transcript to the ticket conversation log for added context after a call ends. -
**Create a concise, AI-generated summary of the call transcript**and save the summary to the ticket conversation log.

**Prerequisites**

- You must have the Advanced AI add-on.

**Show me how**

## Using AI to route tickets and provide faster service

Generally speaking, the faster service you provide, the happier your customers will be. Use Zendesk’s AI features to quickly route tickets to the right agents the first time, saving an average of 45 seconds per ticket compared to manual triage. Even better, deflect easily answered tickets altogether, giving your agents time to focus on more complex customer interactions.

Plus, supply agents with the right words to address commonly asked customer questions quickly. You can even give them a helping hand with just-in-time internal guidance about how to handle tricky tickets.

Using intelligent triage, you can automatically route Support and messaging tickets to the right teams based on what the ticket is about (its intent), the language it's written in, whether the customer's message is positive or negative (its sentiment), or a combination of all three.

**Benefits**

-
**Eliminate manual triage**by automatically categorizing incoming requests with the customer’s intent, language, and sentiment, saving 30-60 seconds on each request. -
**Power automated routing workflows**that send incoming requests to the appropriate agent the first time. -
**Automate responses to customers**, allowing them to self-serve and solve their own requests. -
**Proactively request missing information**so agents can save time and manual effort. -
**Get deeper reporting insights**to better understand your customers and optimize your operations.

**Prerequisites**

- You must have the Advanced AI add-on.

**Show me how**

- Automatically detecting customer intent, language, and sentiment
- Choosing a routing method for automatically triaged tickets

**Examples**

**Best practices**

**More information**

Autoreplies with intelligent triage let you create custom responses to customer email requests based on AI predictions about intent, language, and sentiment. You build autoreplies with intelligent triage using the Autoreply trigger action.

**Benefits**

-
**Automatically deflect a greater share of customer queries**, freeing agents up to work on more complex customer requests.

**Prerequisites**

- You must have the Advanced AI add-on and be using intelligent triage.

**Show me how**

**More information**

Macro suggestions for admins makes it easier to determine which macros will be most useful for your agents and end users. This feature suggests new macros that admins might want to create based on repeated content from all agent replies in your account.

**Benefits**

-
**Receive suggestions to create shared macros**to help agents respond faster and more consistently. -
**Identify knowledge gaps in macros and optimize them**to provide the most relevant response. -
**Reduce the time spent on analysis**and promote seamless collaboration between teams.

**Prerequisites**

- You must have the Advanced AI add-on.

**Show me how**

**More information**

You can create a trigger that automatically adds an internal note to a submitted ticket that meets certain conditions. For example, you could configure a trigger to look for tickets with a negative sentiment and add an internal note that gives the agent information about handling or escalating delicate customer service situations.

**Benefits**

-
**Provide agents with helpful process reminders or other information**, speeding up agent onboarding and supporting ongoing training.

**Prerequisites**

- You must have the Advanced AI add-on and (optionally) be using intelligent triage.

**Show me how**

## Using AI to strengthen your knowledge base and deliver answers

Up-to-date knowledge is key to any successful customer service experience. Help your agents deliver the right answers based on content you’ve already created in your help center.

Create new content quickly and easily with AI tools that help you expand on points, choose the right tone, and simplify word choice. Then build on that content by identifying gaps and other opportunities for improvement that help you provide a better experience for your self-serve customers.

Finally, help customers and agents find the content they need with semantic search that intuitively understands what they’re looking for.

Standard autoreplies, also called autoreplies with articles, are automated responses to customer requests sent through an email or web form. The responses include suggested help center articles to help customers resolve their issues.

**Benefits**

- Automatically deflect customer queries that can be answered by your help center content, freeing agents up to work on more complex customer requests.

**Prerequisites**

- You must have activated your help center.

**Show me how**

**Best practices**

**More information**

In the help center, the expand feature helps you quickly and efficiently create expanded content for articles and content blocks. You can change the tone of the content you’re writing to make it either more conversational or more professional, depending on your company’s brand. You can also simplify your content to make it easier to read.

**Benefits**

-
**Create articles from short notes or bullet points**to speed up content creation. -
**Apply a consistent brand voice**across your help center content. -
**Simplify your articles**by removing unnecessary words that slow down reader comprehension.

**Prerequisites**

- You must have the Advanced AI add-on.

**Show me how**

Content cues use machine learning technology and Guide article usage data to help you discover opportunities and tasks that will improve your knowledge base health.

**Benefits**

-
**Improve your knowledge base**by reviewing suggestions for articles that might need to be created or updated based on common customer requests.

**Prerequisites**

- See the Content Cues account requirements.

**Show me how**

Semantic search generates the most accurate search results possible by capturing the meaning of search queries, helping users locate content without prior knowledge of the exact keywords to use.

**Benefits**

-
**End users can find the information they’re looking for more easily**, increasing the deflection power of your help center. -
**Agents can be more efficient**as they can find answers more quickly.

**Prerequisites**

- You must have activated your help center.
- Zendesk must have rolled out semantic search to your help center. See How to check if your help center is enabled for semantic search.

**Show me how**

## Using AI to provide 24/7 service and build smarter AI agents

When your customers span the globe, every hour of the day is an opportunity to provide great customer service. Zendesk AI agents let you do just that. Provide 24/7 service with conversation bots that guide your customers to the right solutions without agent intervention.

Go further with AI agents that help you personalize the service experience and understand your customers’ needs at a deeper level, helping you provide service without needing to increase your agent headcount. Plus, build these bots quickly and fill knowledge gaps with AI-powered intents that help take the guesswork out of bot building.

Conversation bots work with Zendesk’s messaging channels to deliver automated conversational support to your customers. They are highly customizable and easy to build using the drag-and-drop bot builder to guide customers to a resolution.

**Benefits**

-
**Manage higher volumes without adding staff**by letting the bot solve common issues without an agent getting involved, or by facilitating handoffs to agents by gathering key information from customers first. -
**Provide an always-available resource**to give customers instant answers and resolve issues faster.

**Show me how**

- Creating a conversation bot for your web and mobile channels
- Building a conversation bot using answers

**More information**

Generative replies can immediately begin to respond to questions from your end users. These responses use generative AI to evaluate articles in your help center, then use that knowledge to provide concise answers within the ongoing conversation. End users get the information they need without leaving the conversation channel to read an article.

**Benefits**

-
**Automatically resolve customer inquiries**using your existing help center content, without the need for manually configuring answers upfront.

**Prerequisites**

- You must be using Agent Workspace and messaging.
- An active Zendesk knowledge base must be connected to the bot's assigned brand.

**Show me how**

Bot personas let you select and apply a personality to a conversation bot’s AI-generated responses. You can choose from a number of personas, including:

-
**Professional**, a polite, direct voice. -
**Friendly**, a casual, approachable voice. -
**Playful**, a lighthearted, charming voice.

**Benefits**

-
**Provide a more natural and engaging customer experience**on bot-deployed messaging channels.

**Prerequisites**

- You must be using Agent Workspace and messaging.

**Show me how**

Suggested intents help your bot accurately understand your customers’ needs by suggesting intents when you’re creating an answer for a conversation bot. These suggested, or pre-trained, intents can be used in place of training phrases to help your conversation bot match and deliver the most relevant answer for a customer's question.

**Benefits**

-
**Save admins manual setup time**because the bots come pre-trained with intents for smarter service conversations in your industry. That means they’re available out of the box without months of manual setup to help you scale exceptional service with ease. -
**Automatically detect and classify requests**based on the customer’s intent to deliver the most accurate answer, resulting in a better customer experience and higher deflection rate for your business.

**Prerequisites**

- You must have the Advanced AI add-on and be using intelligent triage to use intents in more than three answers per bot.

**Show me how**

Intent suggestions identify the questions your customers ask most often during bot conversations that don’t match an existing bot answer. This helps you uncover gaps in the information provided by your bot so you can create new answers (or improve existing ones).

**Benefits**

-
**Identify knowledge gaps in conversation bots**and suggest unused intents that can improve ticket deflection. -
**Give your customers a better support experience**by avoiding the “Sorry, I didn’t get that” fallback message.

**Prerequisites**

- You must have the Advanced AI add-on, be using intelligent triage, and be using a bot in a supported language.

**Show me how**

---

# What is your Zendesk AI solution? : r/Zendesk
**URL**: https://www.reddit.com/r/Zendesk/comments/1ahhhcv/what_is_your_zendesk_ai_solution/

---
title: What is your Zendesk AI solution?
author: Rhbaby
date: 2024-02-02
---
#
What is your Zendesk AI solution?

We'd like an AI tool to take a swing at all level-1 support requests -- have you found anything helpful?

Ideally it would be an app in Slack that is able to do simple workflows -- add users to Google Groups, provision them into the Okta apps where SCIM is enabled, and things of the like. If it can't do it automagically, it should ping an agent into the chat or create a Zendesk ticket. I'd like to avoid solutions that that are *just* chatbots.

We've looked at Aisera a bit and are exploring others but I'm hoping someone has something installed and working well that they can recommend.

---

# About Zendesk Advanced AI – Zendesk help
**URL**: https://support.zendesk.com/hc/en-us/articles/5524125586330-About-Zendesk-Advanced-AI

---
title: About Zendesk Advanced AI
url: https://support.zendesk.com/hc/en-us/articles/5524125586330-About-Zendesk-Advanced-AI
hostname: zendesk.com
description: Zendesk Advanced AI is a set of features that expand on the AI offerings already built into the Zendesk Suite. It’s available as an add-on for Suite and Support Professional and above plans, allow...
sitename: Zendesk help
date: 2023-05-10
---

Zendesk Advanced AI is a set of features that expand on the AI offerings already built into the Zendesk Suite. It’s available as an add-on for Suite and Support Professional and above plans, allowing you to achieve even more of your business goals, like increased cost savings, improved customer interactions, reduced agent onboarding time, and more.

This article answers the following questions about Zendesk Advanced AI:

## What is it?

Zendesk Advanced AI is a set of powerful artificial intelligence (AI) tools that are purpose-built for the customer service experience.

The following features are included in Zendesk Advanced AI:

## How does it work?

The Zendesk Advanced AI add-on works across your entire customer service experience. The advanced features included in the add-on allow you to get faster value out of AI.

Watch the demo video below to see some of the Advanced AI features in action, or read on for information about the features included in the add-on.


### Intelligent triage

Intelligent triage uses intent detection, language detection, and sentiment analysis to classify incoming requests and allow teams to power workflows based on these insights.

**What it does:**

-
**Eliminates manual triage**by automatically categorizing incoming requests with the customer’s intent, language, and sentiment, saving 30-60 seconds on each request. -
**Powers automated routing workflows**that send incoming requests to the appropriate agent the first time. -
**Automates responses to customers**that allow them to self-serve and solve their own requests. -
**Proactively requests missing information**so agents can save time and manual effort. -
**Gives you deeper reporting insights**to better understand your customers and optimize your operations.

For more information, see About intelligent triage.

### Intelligence in the context panel

Intelligence in the context panel is an agent-facing panel in the Agent Workspace that shows agents AI-powered insights and suggests the right macros to apply to solve the customer’s issue.

**What it does:**

-
**Gets agents up to speed quickly with a ticket**by using the summarize feature to recap all the public comments that have been added to the ticket so far. -
**Gives agents access to insights**on customer intent, sentiment, and language to find the best resolution quickly. -
**Suggests the most relevant macros**to apply so agents can respond and solve issues faster. -
**Helps agents ramp up quickly and improve**by giving them AI-powered guidance on customer context next steps.

For more information, see Viewing intelligent triage predictions and suggested macros with Intelligence.

### Generative AI for agents

The expand and tone shift features in the Agent Workspace composer help agents be more productive while still providing a high level of customer service. Agents can click a button to expand their reply from a few words to a full response, or change the tone of their reply to make it more friendly or more professional.

**What it does:**

-
**Decreases the time agents need to craft a response**to a customer request. -
**Turns brief bullet points into a comprehensive response**that addresses a customer need. -
**Enhances the tone of agent responses**to maintain brand consistency across your support team.

For more information, see Expanding a comment you're composing and Changing the tone of a comment you're composing.

### Macro suggestions for admins

Macro suggestions for admins are AI-powered suggestions for new shared macros that admins can create to make their agents more effective.

**What it does:**

-
**Offers suggestions to create shared macros**to help agents respond faster and more consistently. -
**Identifies knowledge gaps in macros**and optimizes to provide the most relevant response. -
**Reduces the time spent on analysis**and promotes seamless collaboration between teams.

For more information, see Creating macros from macro suggestions.

### Autoreply and internal note trigger actions

The autoreply and internal note trigger actions work with intelligent triage to automate responses to customer requests or provide internal guidance to agents based on AI predictions about intent, language, and sentiment. You can use the autoreply trigger action to create custom responses to customer email requests. You can use the internal note trigger action to automatically add an internal note to submitted tickets that meet certain conditions.

**What it does:**

-
**Automatically deflects a greater share of customer queries**, freeing agents up to work on more complex customer requests. -
**Provides agents with helpful process reminders or other information**, speeding up agent onboarding and supporting ongoing training.

For more information, see Adding a public comment to a ticket using a trigger and Adding an internal note to a ticket using a trigger.

### Generative AI for help center

Zendesk offers several AI-powered features that help you create help center content quickly and effectively. The expand feature helps you quickly and efficiently create expanded content for articles and content blocks. You can change the tone of the content you’re writing to make it either more conversational or more professional, depending on your company’s brand. You can also simplify your content to make it easier to read.

**What it does:**

-
**Creates articles from short notes or bullet points**to speed up content creation. -
**Applies a consistent brand voice**across your help center content. -
**Simplifies your articles**by removing unnecessary words that slow down reader comprehension.

For more information, see Using generative AI to expand and enhance the tone of help center content.

### Generative AI for voice

Generative AI for voice allows you to transcribe and summarize voice call recordings in Zendesk Talk using generative AI. This frees up agents from having to manually write call notes during and after a call. Instead, they can focus directly on solving customer problems and move efficiently from call to call.

**What it does:**

-
**Automatically converts call recordings to text**and saves the transcript to the ticket conversation log for added context after a call ends. -
**Creates a concise, AI-generated summary of the call transcript**and saves the summary to the ticket conversation log.

For more information, see Using generative AI to create call summaries.

## Why should I use it?

The Zendesk Advanced AI add-on helps you effectively scale your customer service when resources are stretched thin. Our AI models are pre-trained and purpose-built for customer service. Our intent model leverages Zendesk datasets representing years’ worth of data and a wide range of use cases.

Because we've done the heavy lifting, you get robust AI capabilities—including intelligent workflows, agent assistance and productivity tools, and machine learning predictions—all from one platform.

Even better, these tools are ready to go from day one. No need to waste months training a model, no need for developer or data science resources, and no concerns that your AI investment won't produce actionable results.

## Who can use it?

The table below summarizes the requirements to use the Zendesk Advanced AI add-on and its included features.

What's available | Requirement type | Requirement details |
---|---|---|

Advanced AI add-on | Plan type |
- Suite Professional and above (Legacy plans not eligible)
- Support Professional and above (Legacy plans not eligible)
|

Industry
This requirement applies only to intent predictions for intelligent triage. You may still buy the Advanced AI add-on without being in one of the listed industries, but you won’t be able to use intent predictions. |
- Retail and e-commerce: B2C only. Excludes manufacturers, wholesale, and B2B.
- Software: Applications or platforms only. B2C only. Excludes hardware companies.
- Financial services: Traditional and online banking only. B2C only.
- Insurance: Includes auto, health, home, and pet insurance.
- Employee experience: Includes IT and HR departments only.
- Travel, hospitality, and tourism: Includes airlines, bus lines, travel agencies, car rentals, and booking management.
- Entertainment and gaming. Includes online casinos, betting platforms, and ticket selling for events.
|
|

Intelligent triage | Plan type, industry, ticket volume, language distribution, and model fit | |

Intelligence in the context panel | Instance-level |
- Intelligent triage and Agent Workspace turned on
- Macros suggestions for agents: 800+ tickets with macros applied in the past 9 months
|

Generative AI for agents | Instance-level |
- Agent Workspace turned on
|

Macro suggestions for admins | Ticket volume |
- 150+ tickets in English in the past 3 months
|

Autoreplies and internal note trigger actions | Instance-level |
- Intelligent triage turned on
|

Generative AI for help center | None |
- None
|

Generative AI for voice | None |
- None
|


## How do I turn it on?

To get started with the Zendesk Advanced AI add-on, see Buying the Advanced AI add-on or contact your Zendesk account representative.

## Where can I learn more?

For more about Zendesk’s Advanced AI offerings, see Resources for Zendesk Advanced AI.

To hear what we’re thinking around AI, see the Artificial intelligence section of the Zendesk Blog.

---

# Looking for honest opinions: What is Zendesk's AI answer bot ...
**URL**: https://www.reddit.com/r/Zendesk/comments/1bndmyx/looking_for_honest_opinions_what_is_zendesks_ai/

---
title: Looking for honest opinions: What is Zendesk's AI answer bot actually like? Can't help but feel these guys are sooo far behind the pack based on what I've seen.
author: Rainman
date: 2024-03-25
---
Check out the official Zendesk Community for support: zendesk.com/zendesk-community Discover User Groups and upcoming community events at https://usergroups.zendesk.com/zendesk-community-events/

Scan this QR code to download the app now

Or check it out in the app stores

Check out the official Zendesk Community for support: zendesk.com/zendesk-community Discover User Groups and upcoming community events at https://usergroups.zendesk.com/zendesk-community-events/

By continuing, you agree to our User Agreement and acknowledge that you understand the Privacy Policy.

Continue with phone number

OR

New to Reddit?
Sign Up

You’ve set up two-factor authentication for this account.

Lost access to your authenticator?
Use a backup code

You’ve set up two-factor authentication for this account.

Don’t have access to your backup code?
Use a code from an authenticator app

By continuing, you agree to our User Agreement and acknowledge that you understand the Privacy Policy.

Already a redditor?
Log In

Reddit is anonymous, so your username is what you’ll go by here. Choose wisely—because once you get a name, you can’t change it.

By continuing, you agree to our User Agreement and acknowledge that you understand the Privacy Policy.

Continue with phone number

OR

Already a redditor?
Log In

Reddit is anonymous, so your username is what you’ll go by here. Choose wisely—because once you get a name, you can’t change it.

Enter your email address or username and we’ll send you a link to reset your password

An email with a link to reset your password was sent to the email address associated with your account

Didn't get an email?

Resetting your password will log you out on all devices.

---

# Looking for AI chatbot with Zendesk guides/knowledge integration : r ...
**URL**: https://www.reddit.com/r/Zendesk/comments/1clj85c/looking_for_ai_chatbot_with_zendesk/

---
title: Looking for AI chatbot with Zendesk guides/knowledge integration
date: 2024-05-06
---
Scan this QR code to download the app now

Or check it out in the app stores

Sorry, this post was deleted by the person who originally posted it.

By continuing, you agree to our User Agreement and acknowledge that you understand the Privacy Policy.

Continue with phone number

OR

New to Reddit?
Sign Up

You’ve set up two-factor authentication for this account.

Lost access to your authenticator?
Use a backup code

You’ve set up two-factor authentication for this account.

Don’t have access to your backup code?
Use a code from an authenticator app

By continuing, you agree to our User Agreement and acknowledge that you understand the Privacy Policy.

Already a redditor?
Log In

Reddit is anonymous, so your username is what you’ll go by here. Choose wisely—because once you get a name, you can’t change it.

By continuing, you agree to our User Agreement and acknowledge that you understand the Privacy Policy.

Continue with phone number

OR

Already a redditor?
Log In

Reddit is anonymous, so your username is what you’ll go by here. Choose wisely—because once you get a name, you can’t change it.

Enter your email address or username and we’ll send you a link to reset your password

An email with a link to reset your password was sent to the email address associated with your account

Didn't get an email?

Resetting your password will log you out on all devices.

---

# Zendesk's AI in Action for CX
**URL**: https://event.zendesk.com/aiinactionforcx/CXPA

---
title: Zendesk's AI in Action for CX
url: https://event.zendesk.com/aiinactionforcx
hostname: zendesk.com
description: Explore how Zendesk AI can revolutionize your self-service strategy and increase opportunities to automate more of your customer experiences.
sitename: event.zendesk.com
date: 2024-06-11
---
Save your seat for the kick off of our three-part "AI in Action for CX" mini series! These quarterly 30-minute sessions will explore practical AI use cases within customer service & demonstrate how Zendesk's CX solutions, including case management and Workforce Engagement Management (WEM) tools, can transform your service strategy.


If you can’t join us on June 27th, register to get the on-demand recording delivered to your inbox.

In this session, explore how Zendesk AI can revolutionize your self-service strategy and increase opportunities to automate more of your customer experiences by:

- Providing instant, accurate answers to FAQs using existing knowledge base content to wow your customers with 24/7 support

- Personalizing each interaction to foster deeper connections and increase CSAT scores

- Fully automating even the most complex customer requests to free up your agents and optimize operations

---

# Do Zendesk admins even want AI? : r/Zendesk
**URL**: https://www.reddit.com/r/Zendesk/comments/1amo4ot/do_zendesk_admins_even_want_ai/

---
title: Do Zendesk admins even want AI?
author: Separate Working
date: 2024-02-09
---
Check out the official Zendesk Community for support: zendesk.com/zendesk-community Discover User Groups and upcoming community events at https://usergroups.zendesk.com/zendesk-community-events/

#
Do Zendesk admins even want AI?

We've built a Zendesk app that helps draft AI responses from

- Past similar tickets

- Website + help centre content

-- It also helps detect and close 'spam' and 'thank you' tickets.

We've spoken to a sizeable number of Zendesk admins and learnt:

- AI drafted responses are good - but not mind blowing. Macros still rule!

- Auto closing 'Spam' and 'Thank you' tickets got a MUCH better response than expected (we thought this was a pretty meh feature)

- Admins are more interested in "automations" enabled by AI. These automations can be SUPER varied. We don't have a clear view of what automation will make you go "wow"

As an admin, what's your take? What are you really looking for?

Yeah, AI is here and you're being bombarded with AI solutions - but what do you really want?

Even a single line of feedback will help us go in the right direction!

---

# Using AI to summarize conversations in a Support app | Zendesk ...
**URL**: https://developer.zendesk.com/documentation/apps/build-an-app/using-ai-to-summarize-conversations-in-a-support-app/

---
title: Using AI to summarize conversations in a Support app
description: Developer documentation for products at Zendesk
---
# Using AI to summarize conversations in a Support app

GPT-3 is an AI language processing model created by OpenAI. The model can generate human-like responses to text prompts. For example, you can use GPT-3 to answer questions, complete texts, or summarize conversations. GPT-3 powers OpenAI's ChatGPT chatbot.

This tutorial shows how you can use the Zendesk Apps framework (ZAF) to extend Zendesk using external AI systems. In the tutorial, you'll create a client-side Zendesk app that uses ChatGPT to summarize Support ticket conversations. The app uses the OpenAI API to interact with ChatGPT and give it text prompts. Agents can access the app while viewing a ticket in the Agent Workspace.

**Disclaimer:** Zendesk provides this article for instructional purposes only.
Zendesk doesn't provide support for the app or example code in this tutorial.
Zendesk doesn't support third-party technologies, such as GPT-3, ChatGPT, or the
OpenAI API.

## What you'll need

To complete this tutorial, you'll need the following:

-
A Zendesk account with the Zendesk Suite Growth plan or above, or the Support Professional plan or above. To get a free account for testing, see Getting a trial or sponsored account for development.

-
An OpenAI API key. OpenAI offers free API credits you can use for testing.

To sign up for an OpenAI account, see the Create your account page on openai.com. To create an API key, select

**View API keys**in the profile menu, then click**Create new secret key**. -
The Zendesk Command Line Interface (ZCLI). To install or upgrade ZCLI, see Installing and updating ZCLI. You also need to authenticate ZCLI with your Zendesk account. See Authentication in the ZCLI documentation.

ZCLI replaces the Zendesk Apps Tools (ZAT), which are in maintenance mode. To use ZAT instead, see Installing and using ZAT.

-
Familiarity with the ZAF. Before you start, complete the Zendesk app quick start tutorial.


## Creating the app files

First, create basic starter files for an app named **Ticket Summarizer**.

**To create basic app starter files**

-
In your terminal, navigate to the folder where you want to store the app. Example:

`cd projects`

-
In the folder, run:

`zcli apps:new`

-
At the prompts, enter the following values:

- Directory name:
**ticket_summarizer** - Author's name: Your name
- Author's email: Your email address
- Author's website: Leave blank and press Enter.
- App name:
**Ticket Summarizer**

ZCLI creates app starter files in the

**Ticket Summarizer**folder. - Directory name:

## Adding a secure API key setting

Create a secure setting for the OpenAI API key. The OpenAI API requires this key for authentication.

Then add a related label for the setting to the project's **en.json**
translation file. The label's text appears on the app's settings page.

**To add the secure setting and label**

-
In the

**ticket_summarizer**folder, open**manifest.json**in your text editor. -
In

**manifest.json**, add the following`domainWhitelist`

and`parameters`

properties.`{`

`...`

`"version": "1.0.0",`

`"frameworkVersion": "2.0",`

`"domainWhitelist": ["api.openai.com"],`

`"parameters": [`

`{`

`"name": "openAiApiToken",`

`"type": "text",`

`"secure": true`

`}`

`]`

`}`

-
In the

**ticket_summarizer/translations**folder, open**en.json**. -
Replace

**en.json**'s contents with the following JSON:`{`

`"app": {`

`"name": "Ticket Summarizer",`

`"short_description": "Use OpenAI's ChatGPT to summarize Support tickets.",`

`"long_description": "Use OpenAI's ChatGPT to summarize Support tickets.",`

`"installation_instructions": "Add your OpenAI API key and click install.",`

`"parameters": {`

`"openAiApiToken" : {`

`"label": "OpenAI API key"`

`}`

`}`

`}`

`}`

-
Save

**manifest.json**and**en.json**.

## Sending requests to the OpenAI API

Update your app to send a text prompt to the OpenAI API. The prompt should contain the ticket's conversation.

**To update the app**

-
In the app's

**assets**folder, open**iframe.html**. Replace the file's contents with the following HTML:`<!DOCTYPE html>`

`<html>`

`<head>`

`<meta charset="utf-8" />`

`<link`

`rel="stylesheet"`

`href="https://cdn.jsdelivr.net/combine/npm/@zendeskgarden/[email protected],npm/@zendeskgarden/[email protected]"`

`/>`

`</head>`

`<body>`

`<h2 class="u-semibold u-fs-xl">Conversation summary</h2>`

`<div class="u-mt" id="container">Loading the ticket summary...</div>`

`<script src="https://static.zdassets.com/zendesk_app_framework_sdk/2.0/zaf_sdk.min.js"></script>`

`<script type="text/javascript" src="main.js"></script>`

`</body>`

`</html>`

-
In the

**assets**folder, create a**main.js**file. Paste the following code into the file:`const client = ZAFClient.init();`

`async function updateSummary() {`

`const convo = await getTicketConvo();`

`const prompt = await getPrompt(convo);`

`const summary = await getSummary(prompt);`

`const container = document.getElementById("container");`

`container.innerText = summary;`

`}`

`async function getTicketConvo() {`

`const ticketConvo = await client.get("ticket.conversation");`

`return JSON.stringify(ticketConvo["ticket.conversation"]);`

`}`

`async function getPrompt(convo) {`

`return ``

`Summarize the following customer service interaction.`

`Detect the customer's sentiment and extract any key dates,`

`places, or products in the following format.`

`Summary:`

`Customer sentiment:`

`Key Information:`

`${convo}`;`

`}`

`async function getSummary(prompt) {`

`const options = {`

`url: "https://api.openai.com/v1/chat/completions",`

`type: "POST",`

`contentType: "application/json",`

`headers: {`

`Authorization: "Bearer {{setting.openAiApiToken}}",`

`},`

`data: JSON.stringify({`

`model: "gpt-3.5-turbo",`

`messages: [{ role: "user", content: prompt }],`

`}),`

`secure: true,`

`};`

`const response = await client.request(options);`

`return response.choices[0].message.content.trim();`

`}`

`client.on("app.registered", () => {`

`client.invoke("resize", { width: "100%", height: "400px" });`

`updateSummary();`

`});`

`client.on("ticket.conversation.changed", () => {`

`updateSummary();`

`});`

Upon loading, the app uses the ZAF client.get() method to retrieve the open ticket's conversation. It then uses the client.request() method to send a text prompt to OpenAI's Create completion endpoint. The prompt contains the conversation and a requested response format.

The endpoint returns a summary of the conversation using the requested format. The app displays this summary.

The app fetches a new summary whenever the ticket's conversation changes.

-
Save

**iframe.html**and**main.js**.

## Installing the app

ZCLI provides a local web server for app testing. However, the ZCLI server doesn't support secure settings. As a workaround, use ZCLI to package and install the app in a test account instead.

**To install the app**

-
In the

**ticket_summarizer**folder, run:`zcli apps:create`

-
When prompted, enter your OpenAI API key as the

**setting.openAiApiToken**value and press Enter. -
In Admin Center, click the

**Apps and integrations**icon () in the sidebar. Then select**Apps**>**Zendesk Support apps**.The app appears in the list of installed and enabled apps on the

**My Apps**page.

## Testing the app

To test the app, open a Support ticket in the Agent Workspace.

**To test the app**

-
Go to the Agent Workspace in Zendesk Support. From the workspace, open a new or existing ticket. If possible, select a long or complex ticket.

-
Click the

**Apps**icon.After a moment, a summary of the ticket's conversation appears in the

**Ticket Summarizer**app.

Congratulations! You've created a Zendesk app that summarizes Support ticket conversations using ChatGPT. As a next step, you can get your app ready for production. Consider tackling the following tasks:

-
Add error handling for the app's

`client.get()`

and`client.request()`

calls -
Add a cache to reduce calls to the OpenAI API

-
Update the

`getPrompt()`

function to experiment with different text prompts

---

