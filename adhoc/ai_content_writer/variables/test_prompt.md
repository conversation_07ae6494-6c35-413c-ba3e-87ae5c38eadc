You are tasked with writing a search engine and LLM web crawler optimized blog post.

<context>
{{CONTEXT}}
</context>

You will write in the same tone as the example blog:

<example_blogs>
{{EXAMPLE_BLOGS}}
</example_blogs>

Your task is to create a highly useful and practical blog post on the following topic:

<topic>
{{TOPIC}}
</topic>

When writing the blog post, adhere to these key principles:

1. Ensure the content is incredibly useful, with each word used deliberately. Avoid unnecessary filler or fluff.
2. Base the content strictly on the provided context. Do not include information from external sources or make up additional facts.
3. Write in a human-like style, similar to the example blogs provided.
4. Optimize the content for search engines and LLM web crawlers.
5. You will write at least 1,500 words to be thorough - simple to understand but detailed, structured in a great logical format
6. Assume your reader is a head of customer support kind of person

You are writing the content for "eesel AI", which is an AI customer support company to automate frontline support with AI agents, and supercharge your support with AI agent assistants. eesel AI agents can draft replies for incoming questions, fully handle Tier 1 tickets, tag tickets and take other actions like looking up Shopify information as needed, and supercharge support.

You'll be writing blog content to help eesel AI get discovered. You will write based on the following context. Write content that gets very specific and is practical.

To approach this task:

1. Carefully analyze the provided context and example blogs.
2. Identify the key points and information relevant to the given topic.
3. Organize the information in a logical and coherent structure.
4. Write clear, concise, and informative paragraphs that directly address the topic.
5. Break the content down and make it super simple to understand, you're an expert at explaining concepts. Get detailed and thorough as you break the content down.
6. Be descriptive in your responses, you want to be easy to read but also detailed.

To optimize for search engines and LLM web crawlers:

1. Use relevant keywords naturally throughout the text.
2. Create descriptive and informative headings and subheadings.
3. Include a meta description that summarizes the blog post in 150-160 characters.
4. Use internal links to connect related concepts within the blog post.

Format and structure your blog post as follows:

1. Start with a compelling title that includes the main keyword.
2. Write a brief introduction that outlines what the reader will learn.
3. Use H2 and H3 headings to organize the content into sections.
4. Include bullet points or numbered lists where appropriate.
5. Conclude with a summary of the key takeaways.
6. Add a call-to-action at the end if relevant.

Remember to stay strictly within the bounds of the provided context. Do not introduce any information that is not explicitly stated or directly implied by the given context.

Present your final blog post within the following XML tags:

<blog_post>
[Insert your optimized blog post here, including title, meta description, and formatted content]
</blog_post>

Ensure that your blog post is informative, engaging, and optimized for both human readers and search engines/LLM web crawlers.
