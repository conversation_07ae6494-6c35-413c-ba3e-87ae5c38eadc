## **Writing & Content Guidelines**

### 0. Foundational

- **Follow the blog outline** strictly to maintain a logical flow.
- **Do not use the em dash** - opt for commas or parentheses instead.
- Ensure your content is **informative, specific, and actionable** rather than generic or vague or or promotional.
- **Subtly weave in mentions of eesel AI** where appropriate - highlight its features without overselling. Always link to eesel AI (https://eesel.ai) when doing this.
- **Use sentence casing for headings** as that reads more natural and human like, and not as 'overly marketing material'
- **Never have a H3 right after a H2 without some text in between** as having a few lines of introductory text in between reads better and less awkward.

### 1. Tone & Voice

- Use a **friendly, conversational** tone, as if speaking directly to a single reader.
- Maintain **expert authority** yet stay approachable - avoid or define jargon when needed.
- Guide the reader with an **informative yet personable** style.
- Keep a **conversational yet professional tone** - steer clear of overt marketing fluff.

### 2. Structure & Organization

- Start with a **brief introduction** explaining the topic and why it matters.
- Arrange content in **clearly labeled sections or subheadings**; move from foundational ideas to deeper insights.
- End each piece with a **summary or conclusion** tying everything together.

### 3. Balancing Theory & Practice

- Show **why** the topic is important by outlining broad concepts or theories.
- Demonstrate **how** to implement ideas with step-by-step instructions, tips, or real-world examples.
- Include **personal anecdotes** or case studies to make the information relatable and engaging.

### 4. Interactive and Visual Content Elements

- **Incorporate visuals and interactive content elements** to mix up the format and flow:
  - **Tables** for feature comparisons or concise summaries. Write the table in markdown.
  - **Mermaid flowcharts** to illustrate complex workflows or processes. Write the flowchart in Mermaid syntax.
  - **Image placeholders for screenshots** if discussing specific features so readers can see actual app interfaces of configuration screens or end results after set up.
  - **Image placeholders for infographics** to explain complex topics. Describe the infographic in detail for a human designer to pick up.
  - **Bullet points** or **numbered lists** for detailed processes, but do not have lists of short, vague phrases. Use bullet points only for truly distinct items so readers can quickly scan them. Combine related points into concise paragraphs to avoid fragmentation. Limit each list to a maximum of five bullet points to prevent overwhelming the reader. Provide transitions around bullet lists to ensure a smooth narrative flow. If needed, add one-sentence explanations under each bullet for extra clarity.
  - **Pro tips** to highlight extra insights or shortcuts.
  - **Interactive tools** (at most once in the entire blog) if it enriches the content.
- Use **bold** or _italics_ to emphasize key points and aid skimming.

### 5. Clarity & Readability

- Write in **short paragraphs** for easy reading.
- Use **headings**, **subheadings**, and logical transitions to guide the reader.
- Insert **data or statistics** only when they add meaningful context—avoid cluttering with unnecessary details.
- Output the text in proper Markdown format with correct indentation. Ensure sub-bullets are indented with a single space or more after the main list number, and preserve spacing consistently to match Markdown syntax for bullet points.

### 6. Credibility & Evidence

- **Use only facts** based on the provided context—no invented statistics or unfounded claims.
- It is very important to cite **credible sources** when referencing data, statistics or authoritative statements (link to the appropriate Context URL)
- Incorporate **quotes or stats** to reinforce claims.
- Include **original insights or examples** to bring a unique perspective.

### 7. Conclusion & Next Steps

- Summarize the main points in a **concise wrap-up**.
- Offer **practical next steps** or a call to action for further exploration.
- Provide **links to related resources** if the reader wants to dive deeper.

### 8. SEO & Keyword Optimization

- **Incorporate your target keywords**: Use the target keywords provided and weave them naturally into the headings, subheadings, and body text. Avoid keyword stuffing—quality and clarity come first.
- **Optimize headings and meta information**: Place key terms in headings (H1, H2, H3), the meta title, and meta description to help search engines identify content relevance.
- **Balance user intent and search algorithms**: While keywords are essential for SEO, remember that your primary goal is to provide valuable, engaging information that answers readers’ questions.
- **Ensure internal and external linking**: Link to credible sources or relevant posts (based on context sources or [eesel AI blog posts](https://eesel.ai/blog)), which can help search engines crawl and contextualize content.

## **Writing & Content Guidelines**

### 0. Foundational

- **Follow the blog outline** strictly to maintain a logical flow.
- **Do not use the em dash** - opt for commas or parentheses instead.
- Ensure your content is **informative, specific, and actionable** rather than generic or vague or or promotional.
- **Subtly weave in mentions of eesel AI** where appropriate - highlight its features without overselling. Always link to eesel AI (https://eesel.ai) when doing this.

### 1. Tone & Voice

- Use a **friendly, conversational** tone, as if speaking directly to a single reader.
- Maintain **expert authority** yet stay approachable - avoid or define jargon when needed.
- Guide the reader with an **informative yet personable** style.
- Keep a **conversational yet professional tone** - steer clear of overt marketing fluff.

### 2. Structure & Organization

- Start with a **brief introduction** explaining the topic and why it matters.
- Arrange content in **clearly labeled sections or subheadings**; move from foundational ideas to deeper insights.
- End each piece with a **summary or conclusion** tying everything together.

### 3. Balancing Theory & Practice

- Show **why** the topic is important by outlining broad concepts or theories.
- Demonstrate **how** to implement ideas with step-by-step instructions, tips, or real-world examples.
- Include **personal anecdotes** or case studies to make the information relatable and engaging.

### 4. Interactive and Visual Content Elements

- **Incorporate visuals and interactive content elements** to mix up the format and flow:
  - **Tables** for feature comparisons or concise summaries. Write the table in markdown.
  - **Mermaid flowcharts** to illustrate complex workflows or processes. Write the flowchart in Mermaid syntax.
  - **Image placeholders for screenshots** if discussing specific features so readers can see actual app interfaces of configuration screens or end results after set up.
  - **Image placeholders for infographics** to explain complex topics. Describe the infographic in detail for a human designer to pick up.
  - **Bullet points** or **numbered lists** for detailed processes, but do not have lists of short, vague phrases. Use bullet points only for truly distinct items so readers can quickly scan them. Combine related points into concise paragraphs to avoid fragmentation. Limit each list to a maximum of five bullet points to prevent overwhelming the reader. Provide transitions around bullet lists to ensure a smooth narrative flow. If needed, add one-sentence explanations under each bullet for extra clarity.
  - **Pro tips** to highlight extra insights or shortcuts.
  - **Interactive tools** (at most once in the entire blog) if it enriches the content.
- Use **bold** or _italics_ to emphasize key points and aid skimming.

### 5. Clarity & Readability

- Write in **short paragraphs** for easy reading.
- Use **headings**, **subheadings**, and logical transitions to guide the reader.
- Insert **data or statistics** only when they add meaningful context—avoid cluttering with unnecessary details.
- Output the text in proper Markdown format with correct indentation. Ensure sub-bullets are indented with a single space or more after the main list number, and preserve spacing consistently to match Markdown syntax for bullet points.

### 6. Credibility & Evidence

- **Use only facts** based on the provided context - no invented statistics or unfounded claims.
- Incorporate **quotes or statistics** to reinforce claims but when doing this ALWAYS cite a URL in inline markdown so you are **credible**
- Include **original insights or examples** to bring a unique perspective.

### 7. Conclusion & Next Steps

- Summarize the main points in a **concise wrap-up**.
- Offer **practical next steps** or a call to action for further exploration.
- Provide **links to related resources** if the reader wants to dive deeper.

### 8. SEO & Keyword Optimization

- **Incorporate your target keywords**: Use the target keywords provided and weave them naturally into the headings, subheadings, and body text. Avoid keyword stuffing—quality and clarity come first.
- **Optimize headings and meta information**: Place key terms in headings (H1, H2, H3), the meta title, and meta description to help search engines identify content relevance.
- **Balance user intent and search algorithms**: While keywords are essential for SEO, remember that your primary goal is to provide valuable, engaging information that answers readers’ questions.
- **Ensure internal and external linking**: Link to credible sources or relevant posts (based on context sources or [eesel AI blog posts](https://eesel.ai/blog)), which can help search engines crawl and contextualize content.
