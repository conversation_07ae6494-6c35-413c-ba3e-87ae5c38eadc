import os
import getpass
from dotenv import load_dotenv
from langchain_anthropic import <PERSON><PERSON><PERSON><PERSON><PERSON>
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage
from langchain_core.output_parsers import StrOutputParser
from langchain.output_parsers import StructuredOutputParser
from langchain.output_parsers.structured import ResponseSchema
from langchain_core.prompts import Cha<PERSON><PERSON><PERSON><PERSON><PERSON>emplate, PromptTemplate
from prompt import (
    KEY_QUESTIONS_PROMPT,
    OUTLINE_PROMPT,
    SECTION_WRITING_PROMPT,
    REVISION_PROMPT,
    FEEDBACK_PROMPT,
    OUTLINE_OPTIMIZATION_PROMPT,
    MANAGER_FEEDBACK_PROMPT
)
from langchain_core.runnables import RunnablePassthrough
from typing import List, Optional

import logging
import re
import os.path

# Load environment variables
load_dotenv()

model = ChatAnthropic(
    model="claude-3-5-sonnet-20241022",
    max_tokens=8192  # Use large number instead of None
)
parser = StrOutputParser()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

VARIABLES_DIR = "variables"

def create_content_chain():
    # Define response schemas for each chain
    key_questions_schemas = [
        ResponseSchema(name="key_questions", description="List of important questions to address in the content", type="list"),
        ResponseSchema(name="target_keywords", description="List of SEO target keywords to optimize for with 1-2 main keywords and 3-5 long tail", type="list"),
        ResponseSchema(name="key_questions_rationale", description="Explanation of why these questions and keywords are important", type="string")
    ]
    
    outline_schemas = [
        ResponseSchema(name="outline_draft_preparation", description="Outline preparation notes including SEO analysis", type="string"),
        ResponseSchema(name="outline_draft", description="Detailed content outline with sections", type="string"),
        ResponseSchema(name="outline_draft_rationale", description="Explanation of why this is the best outline given the topic, context, writing guidelines and target keywords", type="string")    
    ]
    
    outline_optimization_schemas = [
        ResponseSchema(name="optimized_outline", description="SEO-optimized version of the outline", type="string"),
        ResponseSchema(name="optimization_rationale", description="Explanation of heading optimizations", type="string")
    ]
    
    section_schemas = [
        ResponseSchema(name="sections_planning", description="Planning and analysis for the sections", type="string"),
        ResponseSchema(name="sections_content", description="The actual content for the sections", type="string"),
        ResponseSchema(name="next_sections_to_write", description="Summary of which sections to write next", type="string"),
        ResponseSchema(name="is_blog_complete", description="Flag indicating if this is the final section of the blog", type="boolean")
    ]
    
    revision_schemas = [
        ResponseSchema(name="sections_planning", description="Planning and analysis for the sections", type="string"),        
        ResponseSchema(name="revised_content", description="The revised content section", type="string"),        
        ResponseSchema(name="revision_rationale", description="Explanation of changes made in this revision pass", type="string"),
        #ResponseSchema(name="next_sections_to_write", description="Summary of which sections to write next", type="string"),                
        ResponseSchema(name="is_blog_complete", description="Flag indicating if this is the final section of the blog", type="boolean")
    ]
    
    feedback_schemas = [
        ResponseSchema(
            name="sections_feedback", 
            description=(
                "Detailed analysis of the current section with specific quotes and suggested improvements. "
                "Each suggestion should follow this format:\n"
                "- Original: [exact quote from text]\n"
                "- Modified: [suggested revision]\n"
                "- Rationale: [explanation of why this change improves the text]"
            ),
            type="string"
        )
    ]

    # Add after feedback schemas
    manager_feedback_schemas = [
        ResponseSchema(
            name="manager_feedback",
            description=(
                "Git-style diff feedback on the blog content with specific additions (+) "
                "and deletions (-) suggestions. Each change should include surrounding "
                "context lines for clarity."
            ),
            type="string"
        )
    ]

    # Create parsers
    key_questions_parser = StructuredOutputParser.from_response_schemas(key_questions_schemas)
    outline_parser = StructuredOutputParser.from_response_schemas(outline_schemas)
    outline_optimization_parser = StructuredOutputParser.from_response_schemas(outline_optimization_schemas)
    section_parser = StructuredOutputParser.from_response_schemas(section_schemas)
    revision_parser = StructuredOutputParser.from_response_schemas(revision_schemas)
    feedback_parser = StructuredOutputParser.from_response_schemas(feedback_schemas)
    manager_feedback_parser = StructuredOutputParser.from_response_schemas(manager_feedback_schemas)
    
    # Create the key questions chain
    key_questions = (
        PromptTemplate(
            template=KEY_QUESTIONS_PROMPT + "\n{format_instructions}",
            input_variables=[
                "topic",
                "context",
                "writing_tips",
                "eesel_ai_context"
            ],
            partial_variables={"format_instructions": key_questions_parser.get_format_instructions()}
        ) 
        | model 
        | key_questions_parser
    ).with_config({"run_name": "generate_key_questions"})

    # Create the outline chain
    outline = (
        PromptTemplate(
            template=OUTLINE_PROMPT + "\n{format_instructions}",
            input_variables=[
                "topic",
                "context",
                "eesel_ai_context",
                "target_keywords",
                "key_questions",
                "writing_tips"
            ],
            partial_variables={"format_instructions": outline_parser.get_format_instructions()}
        )
        | model
        | outline_parser
    ).with_config({"run_name": "generate_outline"})

    # Create the outline optimization chain
    outline_optimization = (
        PromptTemplate(
            template=OUTLINE_OPTIMIZATION_PROMPT + "\n{format_instructions}",
            input_variables=[                
                "outline_draft",
                "target_keywords",
                "topic",
                "word_count",
                "writing_tips"
            ],
            partial_variables={"format_instructions": outline_optimization_parser.get_format_instructions()}
        )
        | model
        | outline_optimization_parser
    ).with_config({"run_name": "optimize_outline"})

    # Create the section chain
    section = (
        PromptTemplate(
            template=SECTION_WRITING_PROMPT + "\n{format_instructions}",
            input_variables=[
                "topic",
                "context",
                "eesel_ai_context",
                "word_count",
                "key_questions",
                "target_keywords",
                "optimized_outline",
                "next_sections_to_write",
                "writing_tips"
            ],
            partial_variables={"format_instructions": section_parser.get_format_instructions()}
        )
        | model
        | section_parser
    ).with_config({"run_name": "generate_section"})

    # Create the revision chain
    revision = (
        PromptTemplate(
            template=REVISION_PROMPT + "\n{format_instructions}",
            input_variables=[
                "context",
                "eesel_ai_context",
                "topic",
                "word_count",       
                "key_questions",     
                "target_keywords",
                "optimized_outline",
                "blog_draft",
                "blog_final",
                "sections_feedback",                
            ],
            partial_variables={"format_instructions": revision_parser.get_format_instructions()}
        )
        | model
        | revision_parser
    ).with_config({"run_name": "revise_content"})

    # Create the feedback chain
    feedback = (
        PromptTemplate(
            template=FEEDBACK_PROMPT + "\n{format_instructions}",
            input_variables=[
                "context",
                "eesel_ai_context",
                "topic",
                "word_count",       
                "key_questions",     
                "target_keywords",
                "optimized_outline",
                "blog_draft",
                "writing_tips",
                "next_section_review"                            
            ],
            partial_variables={"format_instructions": feedback_parser.get_format_instructions()}
        )
        | model
        | feedback_parser
    ).with_config({"run_name": "generate_feedback"})

    # Add after other chains
    manager_feedback = (
        PromptTemplate(
            template=MANAGER_FEEDBACK_PROMPT + "\n{format_instructions}",
            input_variables=[
                "context",
                "topic",
                "blog_content"
            ],
            partial_variables={"format_instructions": manager_feedback_parser.get_format_instructions()}
        )
        | model
        | manager_feedback_parser
    ).with_config({"run_name": "generate_manager_feedback"})

    return key_questions, outline, outline_optimization, section, feedback, revision, manager_feedback

def write_output(content: dict) -> None:
    """Write content to a file in the variables directory."""
    os.makedirs(VARIABLES_DIR, exist_ok=True)
    
    if isinstance(content, dict):
        for key, value in content.items():
            if value is None or (not value and not isinstance(value, bool)):
                continue
                
            output_path = os.path.join(VARIABLES_DIR, f'{key}.md')
            
            # Handle sections_content specially
            if key == 'sections_content':
                # Write to individual section file
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(f"{value}\n")
                
                # Append to blog_draft.md
                blog_draft_path = os.path.join(VARIABLES_DIR, 'blog_draft.md')
                with open(blog_draft_path, 'a', encoding='utf-8') as f:
                    f.write(f"\n{value}\n")
                logger.info(f"Appended sections_content to: {blog_draft_path}")
            # Handle revised_content specially
            elif key == 'revised_content':
                # Write to individual revision file
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(f"{value}\n")
                
                # Additionally append to blog_final.md
                blog_final_path = os.path.join(VARIABLES_DIR, 'blog_final.md')
                with open(blog_final_path, 'a', encoding='utf-8') as f:
                    f.write(f"\n{value}\n")
                logger.info(f"Appended revised_content to: {blog_final_path}")
            # Handle sections_feedback specially
            elif key == 'sections_feedback':
                # Write to individual feedback file
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(f"{value}\n")
                
                # Additionally append to feedback.md
                feedback_path = os.path.join(VARIABLES_DIR, 'feedback.md')
                with open(feedback_path, 'a', encoding='utf-8') as f:
                    f.write(f"\n{value}\n")
                logger.info(f"Appended sections_feedback to: {feedback_path}")
            # Add this case
            elif key == 'manager_feedback':
                # Write to manager feedback file
                manager_feedback_path = os.path.join(VARIABLES_DIR, 'blog_final_manager.diff')
                with open(manager_feedback_path, 'w', encoding='utf-8') as f:
                    f.write(f"{value}\n")
                logger.info(f"Wrote manager feedback to: {manager_feedback_path}")
            else:
                # Handle regular key-value pairs
                with open(output_path, 'w', encoding='utf-8') as f:
                    if isinstance(value, list):
                        for item in value:
                            f.write(f"- {item}\n")
                    else:
                        f.write(f"{value}\n")
            
            logger.info(f"Wrote output to: {output_path}")

def reset_output_files(files_to_reset: Optional[List[str]] = None) -> None:
    """Reset specified output files or all files to empty state.
    
    Args:
        files_to_reset: Optional list of filenames to reset. If None, resets all files.
    """
    default_files = [
        'key_questions.md',
        'target_keywords.md',
        'key_questions_rationale.md', 
        'outline_preparation.md',
        'outline.md',
        'outline_rationale.md',
        'optimized_outline.md',
        'sections_content.md',
        'sections_planning.md',
        'next_sections_to_write.md',
        'next_section_review.md',
        'blog_draft.md',
        'blog_final.md',
        'revised_content.md',
        'revision_rationale.md',
        'sections_feedback.md',
        'feedback.md',
        'is_blog_complete.md',        
        'is_feedback_complete.md'
    ]
    
    files = files_to_reset if files_to_reset is not None else default_files
    
    for filename in files:
        filepath = os.path.join(VARIABLES_DIR, filename)
        if os.path.exists(filepath):
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write('')
            logger.info(f"Reset file: {filepath}")

def split_blog_into_chunks(blog_content: str) -> List[str]:
    """Split blog content into chunks based on headings.
    
    Args:
        blog_content: The full blog content as a string
        
    Returns:
        List of blog content chunks split by headings
    """
    # Split on section headings (# or ## or ###)
    sections = re.split(r'(?=^#{1,2}\s)', blog_content, flags=re.MULTILINE)
    
    # Filter out empty sections and strip whitespace
    chunks = [section.strip() for section in sections if section.strip()]
    
    return chunks

def write_next_chunk_for_review(chunks: List[str], current_chunk_index: int) -> None:
    """Write the next chunk to be reviewed to next_section_review.md
    
    Args:
        chunks: List of all content chunks
        current_chunk_index: Index of the current chunk being reviewed
    """
    if current_chunk_index < len(chunks):
        next_chunk = chunks[current_chunk_index]
        with open(os.path.join(VARIABLES_DIR, 'next_section_review.md'), 'w', encoding='utf-8') as f:
            f.write(next_chunk)
        logger.info(f"Wrote chunk {current_chunk_index + 1} of {len(chunks)} for review")
    else:
        # No more chunks to review
        with open(os.path.join(VARIABLES_DIR, 'next_section_review.md'), 'w', encoding='utf-8') as f:
            f.write('')
        logger.info("No more chunks to review")

def generate_content(mode: str = 'full') -> None:
    """
    Generate content based on the specified mode.
    
    Args:
        mode: Operation mode ('full', 'key_questions', 'outline', 'section', 'feedback', 'revision')
    """
    try:
        key_questions_chain, outline_chain, outline_optimization_chain, section_chain, feedback_chain, revision_chain, manager_feedback_chain = create_content_chain()
        logger.info(f"Starting content generation in {mode} mode")
        
        # Reset output files when running in full mode
        if mode == 'full':
            reset_output_files()

        if mode in ['section', 'section-iterative']:
            reset_output_files(['is_blog_complete.md', 'sections_content.md', 'sections_planning.md', 'next_sections_to_write.md', 'blog_draft.md'])

        if mode in ['feedback-start']:
            reset_output_files(['next_section_review.md', 'is_feedback_complete.md', 'sections_feedback.md', 'revision_rationale.md', 'revised_content.md'])

        if mode in ['feedback']:
            reset_output_files(['is_feedback_complete.md', 'sections_feedback.md', 'revision_rationale.md', 'revised_content.md'])

        if mode in ['feedback-iterative']:
            reset_output_files(['blog_final.md', 'next_section_review.md', 'revision_rationale.md', 'revised_content.md', 'feedback.md', 'is_feedback_complete.md', 'sections_feedback.md', 'revision_rationale.md', 'revised_content.md'])

        if mode in ['revision-iterative']:
            reset_output_files(['blog_final.md', 'revision_rationale.md', 'revised_content.md'])

        if mode in ['full', 'key_questions']:
            key_questions_result = invoke_chain_with_file_inputs(
                key_questions_chain,
                KEY_QUESTIONS_PROMPT
            )
            write_output(key_questions_result)
            if mode == 'key_questions':
                return
        
        if mode in ['full', 'outline']:
            outline_draft = invoke_chain_with_file_inputs(
                outline_chain,
                OUTLINE_PROMPT
            )
            write_output(outline_draft)
            
            # Optimize the outline
            optimization_result = invoke_chain_with_file_inputs(
                outline_optimization_chain,
                OUTLINE_OPTIMIZATION_PROMPT
            )
            write_output(optimization_result)
            if mode == 'outline':
                return
        
        if mode in ['full', 'section', 'section-iterative']:
            section_result = invoke_chain_with_file_inputs(
                section_chain,
                SECTION_WRITING_PROMPT
            )
            write_output(section_result)
            if mode == 'section':
                return
            
            # Continue with full content generation if in full mode
            content = section_result["sections_content"]            
            
            # Keep writing sections until blog is complete
            while not section_result.get("is_blog_complete", False):
                section_result = invoke_chain_with_file_inputs(
                    section_chain,
                    SECTION_WRITING_PROMPT
                )
                content += "\n\n" + section_result["sections_content"]                
                write_output(section_result)
                
                if section_result.get("is_blog_complete", False):
                    logger.info("Blog writing completed - all sections finished!")
                    break
            logger.info(f"Content generation complete in {mode} mode - check {VARIABLES_DIR} for outputs")
            
        if mode in ['full', 'feedback', 'feedback-start','feedback-iterative']:
            # Read the blog draft
            blog_draft_path = os.path.join(VARIABLES_DIR, 'blog_draft.md')
            with open(blog_draft_path, 'r', encoding='utf-8') as f:
                blog_content = f.read()
                
            # Split into chunks
            chunks = split_blog_into_chunks(blog_content)
            current_chunk_index = 0
            
            while True:
                logger.debug("Starting new feedback iteration")
                
                #Write next chunk for review
                write_next_chunk_for_review(chunks, current_chunk_index)
                
                # Get feedback for current chunk
                feedback_result = invoke_chain_with_file_inputs(
                    feedback_chain,
                    FEEDBACK_PROMPT
                )
                write_output(feedback_result)
                logger.debug(f"Feedback complete - is_feedback_complete: {feedback_result.get('is_feedback_complete', False)}")
                
                # Apply revisions based on feedback
                logger.debug("Starting revision based on feedback")
                revision_result = invoke_chain_with_file_inputs(
                    revision_chain,
                    REVISION_PROMPT
                )
                write_output(revision_result)
                
                # Break early if in simple feedback mode
                if mode == 'feedback' or mode == 'feedback-start':
                    break
                    
                # Move to next chunk
                current_chunk_index += 1
                
                # Check if feedback is complete (no more chunks)
                if current_chunk_index >= len(chunks):
                    logger.info("Feedback process completed!")
                    break

        # if mode in ['full', 'manager-feedback']:
        #     # Generate manager feedback
        #     manager_feedback_result = invoke_chain_with_file_inputs(
        #         manager_feedback_chain,
        #         MANAGER_FEEDBACK_PROMPT
        #     )
        #     write_output(manager_feedback_result)
        #     if mode == 'manager-feedback':
        #         return

        logger.info(f"Content generation complete in {mode} mode - check {VARIABLES_DIR} for outputs")
            
    except Exception as e:
        logger.error(f"Error in content generation: {str(e)}")
        raise

def invoke_chain_with_file_inputs(chain, prompt_template: str) -> dict:
    """
    Invoke a chain by automatically reading all required inputs from .md files.
    
    Args:
        chain: The LangChain chain to invoke
        prompt_template: The prompt template string to parse for input variables
    
    Returns:
        dict: The chain's output
    """
    # Extract input variables from the prompt template
    # Remove format_instructions as it's handled separately
    input_vars = set(re.findall(r'\{([^}]+)\}', prompt_template)) - {'format_instructions'}
    
    # Read all required input files
    inputs = {}
    for var in input_vars:
        try:
            with open(os.path.join(VARIABLES_DIR, f'{var}.md'), 'r', encoding='utf-8') as f:
                inputs[var] = f.read().strip()
        except FileNotFoundError:
            raise FileNotFoundError(f"Required input file not found: {var}.md")
    
    logger.info(f"Invoking chain with inputs: {list(inputs.keys())}")
    return chain.invoke(inputs)

if __name__ == "__main__":
    # Test individual components
    #generate_content(mode='key_questions')  # Test just key questions
    #generate_content(mode='outline')       # Test just outline
    #generate_content(mode='section')       # Test just one section
    #generate_content(mode='section-iterative')       # Test all sections
    #generate_content(mode='revision-iterative')      # Test just revision
    #generate_content(mode='feedback')      # Test just feedback
    #generate_content(mode='manager-feedback')      # Test manager feedback
    generate_content(mode='full')          # Generate full content with revision

# idea: In the long run we could try feedback like a 'patch diff' that we then apply programmatically
# we'd pass on the total diff worked on at this point in the prompt as well


