{"type": "excalidraw", "version": 2, "source": "https://marketplace.visualstudio.com/items?itemName=pomdtr.excalidraw-editor", "elements": [{"id": "6QI6oa8aPlwfXcuWkg5P7", "type": "text", "x": 204.6229954905781, "y": 13.448141532781278, "width": 1069.6846923828125, "height": 1573.1718750000027, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 922511988, "version": 4204, "versionNonce": 2134856564, "isDeleted": false, "boundElements": null, "updated": 1729199147195, "link": null, "locked": false, "text": "Needs to be able to:\n- certify that an operation is permitted (read/write/admin)\n- identify a set of claims are met for an API operation\n- identify as a user or s2s call\n\n{\n   scope: {\n        permission: read | write | admin\n   }\n   claims: {\n      \n   }\n   subject: eri://${workspace_id}/${namespace_id} // limited to workspace, namespace for now?  \n   version: ${version}\n}\n\nwhen/where to make available?\n- flask middleware add verified claims/subject/etc to request object\n\nvalidate against some example cases\n- auth0 user access\n    - not relevant because session auth is separate, but we would set the same things.\n- chatgpt for confluence admin user\n    - the service would mint a workspace access token\n    - all endpoints would be able to verify that a given \"user\" is permitted to access a specific namespace\n        - how would this work in the services, if they depend on Flask?\n        - i guess we would just externalise that from the permission service.\n        - request_claims \n        - that means permission service can have null user_id passed in, and rely entirely on request claims \n- chatgpt for confluence non-admin user\n    - does it extend appropriately to what is kinda an impersonation?\n        - workspace-level JWT would be assigned to the chat\n        - workspace-level JWT would be used by the users in the \n- chrome extension\n    - uses session auth so this is duped.\n- zendesk\n    - will get a read-only namespace token ... ?\n    - permission service will need to be able to handle both broad workspace tokens + namespace specific tokens.\n    - this seems to be fine since the claims will be \n- public chats etc\n    - should be fine.\n    - just a question of whether namespace config settings should be a dependency of permission service.\n    - !! some circular dep risk with DI !!\n\n\n\ncredential rotation:\n- some customers have old keys hard-coded with full access, this is a problem.\n    - hotdoc\n    - indebted\n    - possibly yellowdig and smava\n- marketplace app\n    - can be fairly easily moved, relatively simple migration task needed.\n    - refresh_token is revocable but expires.\n    - api_token is non revocable but does not expire.\n    - refresh_token used to retrieve\n- customers with direct API access?\n    - maybe just viastore? we would need to rotate these guys.\n- chrome ext\n    - no action required, everyone should be upgraded by now.\n\nopen questions:\n- what is standard jwt design for this use case\n- what is the relationship with oauth/refresh/etc if any, i think none.\n- is it correct that refresh non revoc, etc?\n", "fontSize": 19.068750000000033, "fontFamily": 1, "textAlign": "left", "verticalAlign": "top", "baseline": 1566, "containerId": null, "originalText": "Needs to be able to:\n- certify that an operation is permitted (read/write/admin)\n- identify a set of claims are met for an API operation\n- identify as a user or s2s call\n\n{\n   scope: {\n        permission: read | write | admin\n   }\n   claims: {\n      \n   }\n   subject: eri://${workspace_id}/${namespace_id} // limited to workspace, namespace for now?  \n   version: ${version}\n}\n\nwhen/where to make available?\n- flask middleware add verified claims/subject/etc to request object\n\nvalidate against some example cases\n- auth0 user access\n    - not relevant because session auth is separate, but we would set the same things.\n- chatgpt for confluence admin user\n    - the service would mint a workspace access token\n    - all endpoints would be able to verify that a given \"user\" is permitted to access a specific namespace\n        - how would this work in the services, if they depend on Flask?\n        - i guess we would just externalise that from the permission service.\n        - request_claims \n        - that means permission service can have null user_id passed in, and rely entirely on request claims \n- chatgpt for confluence non-admin user\n    - does it extend appropriately to what is kinda an impersonation?\n        - workspace-level JWT would be assigned to the chat\n        - workspace-level JWT would be used by the users in the \n- chrome extension\n    - uses session auth so this is duped.\n- zendesk\n    - will get a read-only namespace token ... ?\n    - permission service will need to be able to handle both broad workspace tokens + namespace specific tokens.\n    - this seems to be fine since the claims will be \n- public chats etc\n    - should be fine.\n    - just a question of whether namespace config settings should be a dependency of permission service.\n    - !! some circular dep risk with DI !!\n\n\n\ncredential rotation:\n- some customers have old keys hard-coded with full access, this is a problem.\n    - hotdoc\n    - indebted\n    - possibly yellowdig and smava\n- marketplace app\n    - can be fairly easily moved, relatively simple migration task needed.\n    - refresh_token is revocable but expires.\n    - api_token is non revocable but does not expire.\n    - refresh_token used to retrieve\n- customers with direct API access?\n    - maybe just viastore? we would need to rotate these guys.\n- chrome ext\n    - no action required, everyone should be upgraded by now.\n\nopen questions:\n- what is standard jwt design for this use case\n- what is the relationship with oauth/refresh/etc if any, i think none.\n- is it correct that refresh non revoc, etc?\n", "lineHeight": 1.25}], "appState": {"gridSize": null, "viewBackgroundColor": "#ffffff"}, "files": {}}