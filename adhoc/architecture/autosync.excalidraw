{"type": "excalidraw", "version": 2, "source": "https://marketplace.visualstudio.com/items?itemName=pomdtr.excalidraw-editor", "elements": [{"type": "rectangle", "version": 40, "versionNonce": 232889060, "isDeleted": false, "id": "-tGxIOVnhQfVPOZZhmbmJ", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 444.277852326795, "y": -163.86403951342754, "strokeColor": "#e03131", "backgroundColor": "#ffc9c9", "width": 175.37896192096935, "height": 175.37896192096935, "seed": 893409884, "groupIds": ["qLc1r-31jj57esJ95VtHr"], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"id": "9OfT9xbIOGy-oPszbH3KB", "type": "arrow"}], "updated": 1721699373281, "link": null, "locked": false}, {"type": "rectangle", "version": 86, "versionNonce": 291722596, "isDeleted": false, "id": "jR-7NN2TJcRbZ9_qezzC1", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 475.50062206272514, "y": -135.96284102600072, "strokeColor": "#e03131", "backgroundColor": "#ffc9c9", "width": 175.37896192096935, "height": 175.37896192096935, "seed": 1225513692, "groupIds": ["qLc1r-31jj57esJ95VtHr"], "frameId": null, "roundness": {"type": 3}, "boundElements": [], "updated": 1721699373281, "link": null, "locked": false}, {"type": "rectangle", "version": 85, "versionNonce": 157495396, "isDeleted": false, "id": "yZxY1sUNR7xPqdDy7iyww", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 510.0449630471586, "y": -100.08987154216601, "strokeColor": "#e03131", "backgroundColor": "#ffc9c9", "width": 175.37896192096935, "height": 175.37896192096935, "seed": 1417148644, "groupIds": ["qLc1r-31jj57esJ95VtHr"], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "xZwEbP0NV6jIBFo8A_zrl"}, {"id": "wS4QZbX9-lSIiqUlO1_m4", "type": "arrow"}], "updated": 1721699438779, "link": null, "locked": false}, {"type": "text", "version": 17, "versionNonce": 1301302372, "isDeleted": false, "id": "xZwEbP0NV6jIBFo8A_zrl", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 578.5244601819597, "y": -24.900390581681336, "strokeColor": "#1e1e1e", "backgroundColor": "#ffc9c9", "width": 38.41996765136719, "height": 25, "seed": 2072555484, "groupIds": ["qLc1r-31jj57esJ95VtHr"], "frameId": null, "roundness": null, "boundElements": [], "updated": 1721699373281, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "SQS", "textAlign": "center", "verticalAlign": "middle", "containerId": "yZxY1sUNR7xPqdDy7iyww", "originalText": "SQS", "lineHeight": 1.25, "baseline": 21}, {"type": "rectangle", "version": 51, "versionNonce": 836279772, "isDeleted": false, "id": "ICcZw33L7Kob35RcXND_z", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -2.6444101104739843, "y": -166.5360280611734, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 214.39899667264854, "height": 214.39899667264854, "seed": 925879516, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "PstZwbHGMi8AXBzyX2pI9"}, {"id": "9OfT9xbIOGy-oPszbH3KB", "type": "arrow"}], "updated": 1721699280724, "link": null, "locked": false}, {"type": "text", "version": 106, "versionNonce": 692871268, "isDeleted": false, "id": "PstZwbHGMi8AXBzyX2pI9", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 65.04831332350653, "y": -70.24622920623261, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 79.0135498046875, "height": 21.819398962766954, "seed": 784728292, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1721699202594, "link": null, "locked": false, "fontSize": 17.455519170213563, "fontFamily": 1, "text": "Scheduler", "textAlign": "center", "verticalAlign": "middle", "containerId": "ICcZw33L7Kob35RcXND_z", "originalText": "Scheduler", "lineHeight": 1.25, "baseline": 18}, {"type": "text", "version": 389, "versionNonce": 1284765515, "isDeleted": false, "id": "Rz8TX4QV_Kf2G9K6Scjkv", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -95.57512888956, "y": 80.75487986781968, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 417.2796630859375, "height": 75, "seed": 15898468, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1721803457958, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "This system has a daily interval,\nit checks for all customers in schedule\nand schedules a re-sync in the sync queue", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "This system has a daily interval,\nit checks for all customers in schedule\nand schedules a re-sync in the sync queue", "lineHeight": 1.25, "baseline": 68}, {"type": "arrow", "version": 52, "versionNonce": 1348153828, "isDeleted": false, "id": "9OfT9xbIOGy-oPszbH3KB", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 222.98952595272837, "y": -50.96775823245613, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 199.0878314876371, "height": 2.2261842560134895, "seed": 1638721636, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1721699373281, "link": null, "locked": false, "startBinding": {"elementId": "ICcZw33L7Kob35RcXND_z", "focus": 0.07468783777841483, "gap": 11.23493939055379}, "endBinding": {"elementId": "-tGxIOVnhQfVPOZZhmbmJ", "focus": -0.32324077803051127, "gap": 22.20049488642951}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [199.0878314876371, 2.2261842560134895]]}, {"type": "rectangle", "version": 131, "versionNonce": 308545124, "isDeleted": false, "id": "eNXIzx81emTGatSdKNbsH", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 925.2479901545655, "y": -137.78832228091233, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 200.1278206335412, "height": 200.1278206335412, "seed": 1880661212, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "On9k1NkEE4MQ54qKqDB6-"}, {"id": "wS4QZbX9-lSIiqUlO1_m4", "type": "arrow"}], "updated": 1721699442947, "link": null, "locked": false}, {"type": "text", "version": 204, "versionNonce": 2101468900, "isDeleted": false, "id": "On9k1NkEE4MQ54qKqDB6-", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 989.3199113355939, "y": -47.16875293524879, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 71.98397827148438, "height": 18.88868194221412, "seed": 54862820, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1721699481267, "link": null, "locked": false, "fontSize": 15.110945553771296, "fontFamily": 1, "text": "Processor", "textAlign": "center", "verticalAlign": "middle", "containerId": "eNXIzx81emTGatSdKNbsH", "originalText": "Processor", "lineHeight": 1.25, "baseline": 16}, {"type": "arrow", "version": 65, "versionNonce": 421753060, "isDeleted": false, "id": "wS4QZbX9-lSIiqUlO1_m4", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 698.0537019763239, "y": -48.4410919639176, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 210.7903684541808, "height": 1.5426286135607086, "seed": 1207450844, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1721699442948, "link": null, "locked": false, "startBinding": {"elementId": "yZxY1sUNR7xPqdDy7iyww", "focus": -0.39973970952028576, "gap": 12.629777008195845}, "endBinding": {"elementId": "eNXIzx81emTGatSdKNbsH", "focus": 0.13008084437457865, "gap": 16.403919724060984}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [210.7903684541808, -1.5426286135607086]]}, {"type": "text", "version": 172, "versionNonce": 1883387621, "isDeleted": false, "id": "ypV-7Lo-ksbySc0znoYLO", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 895.7235416249621, "y": 81.90713842801688, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 274.8597412109375, "height": 75, "seed": 1447847388, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1721803457962, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Looks up the implementation\n- adhoc, lambda by ARN\n- server, by URL/JWT", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Looks up the implementation\n- adhoc, lambda by ARN\n- server, by URL/JWT", "lineHeight": 1.25, "baseline": 68}, {"type": "text", "version": 1406, "versionNonce": 1431098859, "isDeleted": false, "id": "kc1tBB_apgrstZl40HUN9", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1504.1923341442043, "y": -966.4225549394657, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 1209.20751953125, "height": 1610, "seed": 1169251940, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1721803457973, "link": null, "locked": false, "fontSize": 28, "fontFamily": 1, "text": "SourceRetriever:\n- is_synchronous\n- supports_get_all_modified\n- synchronously_get_all_sources\n- asynchronously_crawl ... not implemented\n-- NOTE: cleanup should just act on things added automatically, e.g. not on extension.\n\nSource:\n- title\n- body\n- last_modified\n\nZendesk:\n- is_synchronous = true\n- synchronously_get_all_sources\n- asynchronously_crawl = false\n\nSitemap:\n- is_synchronous = true\n- synchronously_get_all_sources\n- asynchronously_crawl = false\n\nWeb crawler:\n- is_synchronous = false\n- synchronously_get_all_sources\n- asynchronously_crawl = true\n--- NOTE: does not clean up old pages? Just discovers new pages.\n--- How much to crawl each time? Max pages/depth etc.\n\nIntercom:\n- is_synchronous = true\n- supports_get_all_modified = true\n- synchronously_get_all_sources\n- asynchronously_crawl = false\n\nGoogle Drive:\n- is_synchronous = true\n- supports_get_all_modified = true\n- synchronously_get_all_modified_sources\n- synchronously_get_all_sources --> Not really supported well. \n- asynchronously_crawl = false\n\nNotion:\n- synchronously_get_all_sources\n- asynchronously_crawl = false\n", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "SourceRetriever:\n- is_synchronous\n- supports_get_all_modified\n- synchronously_get_all_sources\n- asynchronously_crawl ... not implemented\n-- NOTE: cleanup should just act on things added automatically, e.g. not on extension.\n\nSource:\n- title\n- body\n- last_modified\n\nZendesk:\n- is_synchronous = true\n- synchronously_get_all_sources\n- asynchronously_crawl = false\n\nSitemap:\n- is_synchronous = true\n- synchronously_get_all_sources\n- asynchronously_crawl = false\n\nWeb crawler:\n- is_synchronous = false\n- synchronously_get_all_sources\n- asynchronously_crawl = true\n--- NOTE: does not clean up old pages? Just discovers new pages.\n--- How much to crawl each time? Max pages/depth etc.\n\nIntercom:\n- is_synchronous = true\n- supports_get_all_modified = true\n- synchronously_get_all_sources\n- asynchronously_crawl = false\n\nGoogle Drive:\n- is_synchronous = true\n- supports_get_all_modified = true\n- synchronously_get_all_modified_sources\n- synchronously_get_all_sources --> Not really supported well. \n- asynchronously_crawl = false\n\nNotion:\n- synchronously_get_all_sources\n- asynchronously_crawl = false\n", "lineHeight": 1.25, "baseline": 1600}, {"type": "text", "version": 861, "versionNonce": 319033925, "isDeleted": false, "id": "N4JiG3hhVY69U5bI4peCR", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 2979.66506950538, "y": -1075.114334025097, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 677.711669921875, "height": 805, "seed": 1000015716, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1721803457975, "link": null, "locked": false, "fontSize": 28, "fontFamily": 1, "text": "Roughly does the following:\n\nwhen_message_received:\n   filter = extract_from_sqs_msg\n\n   if it's crawler type, just skip entirely for now.\n\n   sources = synchroniser.get_all_urls(fitler)\n   if bootstrapping\n      diff existing documents\n      delete all deleted\n      index all new documents\n   else\n      index all documents\n\nchecked for:\n- zendesk -- yes\n- intercom -- yes\n- sitemap -- yes ?\n- crawler -- \n      \n\n", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Roughly does the following:\n\nwhen_message_received:\n   filter = extract_from_sqs_msg\n\n   if it's crawler type, just skip entirely for now.\n\n   sources = synchroniser.get_all_urls(fitler)\n   if bootstrapping\n      diff existing documents\n      delete all deleted\n      index all new documents\n   else\n      index all documents\n\nchecked for:\n- zendesk -- yes\n- intercom -- yes\n- sitemap -- yes ?\n- crawler -- \n      \n\n", "lineHeight": 1.25, "baseline": 795}, {"type": "text", "version": 172, "versionNonce": 481353867, "isDeleted": false, "id": "ucvnIC0fsVgBkU5z_FTGU", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 2980.2186449672836, "y": -358.9276593333863, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 760.2276000976562, "height": 210, "seed": 1106801116, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1721803457976, "link": null, "locked": false, "fontSize": 28, "fontFamily": 1, "text": "zendesk crawling -- public mode\n\ngiven a base URL\nhttps://support.zendesk.com/api/v2/help_center/locales\n\nmake", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "zendesk crawling -- public mode\n\ngiven a base URL\nhttps://support.zendesk.com/api/v2/help_center/locales\n\nmake", "lineHeight": 1.25, "baseline": 200}], "appState": {"gridSize": null, "viewBackgroundColor": "#ffffff"}, "files": {}}