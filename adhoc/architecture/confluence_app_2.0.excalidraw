{"type": "excalidraw", "version": 2, "source": "https://marketplace.visualstudio.com/items?itemName=pomdtr.excalidraw-editor", "elements": [{"id": "ppAl8LzXYOJIQeRGAF1BK", "type": "text", "x": 231.69329180304942, "y": 41.53067504975911, "width": 1143.19921875, "height": 3775, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 1615457878, "version": 10424, "versionNonce": 502278666, "isDeleted": false, "boundElements": null, "updated": 1730156766522, "link": null, "locked": false, "text": "\nOptions\n\n1. Externalise everything, move it all to eesel AI backend.\n    - Possibly more effort than it's worth\n    - Creates a noisy neighbour problem on _our_ servers that is otherwise covered.\n\n2. Keep hacking away with existing solution\n    - Unclear if possible. Possibly fucked. \n\n3. Rebuild solution on Atlassian platform\n\nPossible solution:\n    - Dont store all selected pages in Forge, just \"selected parent page IDs\" and \"de-selected parent page IDs?\"\n    - On check, assume content + children should be indexed. Use CQL. \n    - On uncheck, use CQL to delete all pages from eesel backend.\n            -- is this actually the user behaviour?\n    - Get rid of the save button as operaitons happen immediately.\n    - User timestamp as the sequence ID.\n\non select (page_id):\n    - check seq ID \n    - remove it from deselected_page_ids table.\n    - queue up CQL ingestion for that page + all children\n        - passing parent page ID + seq ID\n    - add page_id to selected_parent_page_ids table\n\non deselect (page_id):\n    - check seq id\n    - add it to deselected_page_ids table. \n\ncheck indexed (page id):\n    - page_id is in the selected_parent_page_ids table\n      OR\n    - exists when searching for page_id where page_id is a descendent of\n    - this could be batched and async loaded when the table expands.  \n\ngarbage collection process:\n    - check page exists\n        - just remove from selected parent page IDs/deselected parent page IDs if no longer exists.\n    - check attachments exists \n\nBE reconcilation process.\n    - user has removed it from eesel but still shown in confluence\n    - content has been deleted, should be removed from eesel\n    - user has added\n   \nchrome extension adding content:\n    - Grr this is problematic, it won't show up in the Confluence app. \n\nProblems:\n    - How to know if a given page should be shown or not?\n        - this is solved by check_indexed. no longer an issue.\n    - New pages would show up as indexed \n    - Migration\n        - we would need to somehow reverse engineer the selected/deselected lists.\n\nOption 4. Manage selected page state through eesel backend.\n\n    - on select parent (page ID)\n        - push N dummy pages into the document list for that site.\n            - must be reflected very quickly.\n            - having some kind of document status would be good.\n            - we do kind of have that. \n        - queue the actual indexing of that document.\n        - use redis on eesel backend to manage seq/op IDs for a given page. \n\n    - on de-select parent (page ID)\n        - just remove that page from the backend, deletion gets queued.\n        - use redis on eesel backend to manage seq/op IDs for a given page. \n\n    - on load of the treeview\n        - we fetch all spaces + async load the status of each node.\n        - cql -> ancestor = {currentPageId} AND id IN ({allDocumentIds})\n\n    - garbage collection\n        - still required, but seems like no change needed\n\n    - BE reconciliation process\n        - not required, since there is no state.\n\n    - Migration\n        - not required\n\n    - chrome extension\n        - works out of the box\n\n    - performance concerns?\n        - no worse than what we have today.\n        - better perceived performance since UX changes reflected much faster. \n\nSequence ID management\n- not trivial so think through it, order must be respected but is not guaranteed by the queues.\n- operations should be per-page\n- if there is a newer operation for a given page, it should skip the current operation.\n- operations must not be dependent on prior events.\n- redis can have maintain kvps for document/page IDs\n    - we should just do this on a URL-hash basis on the indexing/deletion handlers.\n\nCovers most of the issues (obviously except blogs), slight performance improvement.\n- So many additional pros to this, because it also simplifies caching on the frontend, storage\n  on the backend. \n\n?? How does this go with the forge platform limits?\n- 60s max for any async ops. \n    Tough to say how this would go, but our \"get descendents\" covers this in theory.\n    100 per sec = 6,000 in one op \n- 25s max for any sync ops\n    100 per sec = 2,500 in one op. This is measurable\n- frontend => \n    get me the documents (25s, but this will be plenty)\n- frontend => \n    save my new selection, we need to enumerate all pages in that particular selection.\n    could be _many_, but we would want to limit it to some sensible number K.\n\n?? What do we do about error handling? \n- Could it be easier if it were all on the frontend...? \n    - Doesn't feel like it, plus user can't navigate away then.\n    - Potentially far worse performance from user -> confluence vs. forge -> confluence \n- Can easily store an error table and give the user option to dismiss them\n    or simply don't show > 1 time.  \n\n$$$$$ Decision: Store a table of errors and let the user dismiss them. \n\n?? how can we essentially remove any limitation on the queues ??\ne.g. support up to a theoretical max of 100K pages in a single update.\n- Concerns\n--- noisy neighbour on the whole app, this will most likely nuke the whole indexing pipeline\n--- pinecone fills up, easily solvable, prefer a move to serverless but distraction for now.\n- Solution\n--- need to be able to fetch the content of X pages simulataneously in Y seconds.\n--- if Y is 5 seconds, we can do a max of 12 in a given queue message.\n--- async indexing should be essentially instantaneous.\n--- that gives us 1,200 per message.\n--- we can definitely process more than 100 messages/minute.\n--- the attachments might introduce additional complexity, but it;s unlikely that there will be \n    an attachment per page or a vast number of documents attached.\n\n$$$$$ Decision: \n- Measure the time to retrieve 100 pages + content on production\n- Verify this + platform limits meet the criteria.\n\n?? How do we indicate to the user that it's indexing\n-- Add some new cols to the document table\n-- origin (for later), status, created_at, added_by (nullable). \n-- the table can then render something based on whether it's finished or not.\n\nNotes to self:\n- when generating/checking the document ID for redis seq ID, we must remove any chunk IDs \n\n", "fontSize": 20, "fontFamily": 1, "textAlign": "left", "verticalAlign": "top", "baseline": 3768, "containerId": null, "originalText": "\nOptions\n\n1. Externalise everything, move it all to eesel AI backend.\n    - Possibly more effort than it's worth\n    - Creates a noisy neighbour problem on _our_ servers that is otherwise covered.\n\n2. Keep hacking away with existing solution\n    - Unclear if possible. Possibly fucked. \n\n3. Rebuild solution on Atlassian platform\n\nPossible solution:\n    - Dont store all selected pages in Forge, just \"selected parent page IDs\" and \"de-selected parent page IDs?\"\n    - On check, assume content + children should be indexed. Use CQL. \n    - On uncheck, use CQL to delete all pages from eesel backend.\n            -- is this actually the user behaviour?\n    - Get rid of the save button as operaitons happen immediately.\n    - User timestamp as the sequence ID.\n\non select (page_id):\n    - check seq ID \n    - remove it from deselected_page_ids table.\n    - queue up CQL ingestion for that page + all children\n        - passing parent page ID + seq ID\n    - add page_id to selected_parent_page_ids table\n\non deselect (page_id):\n    - check seq id\n    - add it to deselected_page_ids table. \n\ncheck indexed (page id):\n    - page_id is in the selected_parent_page_ids table\n      OR\n    - exists when searching for page_id where page_id is a descendent of\n    - this could be batched and async loaded when the table expands.  \n\ngarbage collection process:\n    - check page exists\n        - just remove from selected parent page IDs/deselected parent page IDs if no longer exists.\n    - check attachments exists \n\nBE reconcilation process.\n    - user has removed it from eesel but still shown in confluence\n    - content has been deleted, should be removed from eesel\n    - user has added\n   \nchrome extension adding content:\n    - Grr this is problematic, it won't show up in the Confluence app. \n\nProblems:\n    - How to know if a given page should be shown or not?\n        - this is solved by check_indexed. no longer an issue.\n    - New pages would show up as indexed \n    - Migration\n        - we would need to somehow reverse engineer the selected/deselected lists.\n\nOption 4. Manage selected page state through eesel backend.\n\n    - on select parent (page ID)\n        - push N dummy pages into the document list for that site.\n            - must be reflected very quickly.\n            - having some kind of document status would be good.\n            - we do kind of have that. \n        - queue the actual indexing of that document.\n        - use redis on eesel backend to manage seq/op IDs for a given page. \n\n    - on de-select parent (page ID)\n        - just remove that page from the backend, deletion gets queued.\n        - use redis on eesel backend to manage seq/op IDs for a given page. \n\n    - on load of the treeview\n        - we fetch all spaces + async load the status of each node.\n        - cql -> ancestor = {currentPageId} AND id IN ({allDocumentIds})\n\n    - garbage collection\n        - still required, but seems like no change needed\n\n    - BE reconciliation process\n        - not required, since there is no state.\n\n    - Migration\n        - not required\n\n    - chrome extension\n        - works out of the box\n\n    - performance concerns?\n        - no worse than what we have today.\n        - better perceived performance since UX changes reflected much faster. \n\nSequence ID management\n- not trivial so think through it, order must be respected but is not guaranteed by the queues.\n- operations should be per-page\n- if there is a newer operation for a given page, it should skip the current operation.\n- operations must not be dependent on prior events.\n- redis can have maintain kvps for document/page IDs\n    - we should just do this on a URL-hash basis on the indexing/deletion handlers.\n\nCovers most of the issues (obviously except blogs), slight performance improvement.\n- So many additional pros to this, because it also simplifies caching on the frontend, storage\n  on the backend. \n\n?? How does this go with the forge platform limits?\n- 60s max for any async ops. \n    Tough to say how this would go, but our \"get descendents\" covers this in theory.\n    100 per sec = 6,000 in one op \n- 25s max for any sync ops\n    100 per sec = 2,500 in one op. This is measurable\n- frontend => \n    get me the documents (25s, but this will be plenty)\n- frontend => \n    save my new selection, we need to enumerate all pages in that particular selection.\n    could be _many_, but we would want to limit it to some sensible number K.\n\n?? What do we do about error handling? \n- Could it be easier if it were all on the frontend...? \n    - Doesn't feel like it, plus user can't navigate away then.\n    - Potentially far worse performance from user -> confluence vs. forge -> confluence \n- Can easily store an error table and give the user option to dismiss them\n    or simply don't show > 1 time.  \n\n$$$$$ Decision: Store a table of errors and let the user dismiss them. \n\n?? how can we essentially remove any limitation on the queues ??\ne.g. support up to a theoretical max of 100K pages in a single update.\n- Concerns\n--- noisy neighbour on the whole app, this will most likely nuke the whole indexing pipeline\n--- pinecone fills up, easily solvable, prefer a move to serverless but distraction for now.\n- Solution\n--- need to be able to fetch the content of X pages simulataneously in Y seconds.\n--- if Y is 5 seconds, we can do a max of 12 in a given queue message.\n--- async indexing should be essentially instantaneous.\n--- that gives us 1,200 per message.\n--- we can definitely process more than 100 messages/minute.\n--- the attachments might introduce additional complexity, but it;s unlikely that there will be \n    an attachment per page or a vast number of documents attached.\n\n$$$$$ Decision: \n- Measure the time to retrieve 100 pages + content on production\n- Verify this + platform limits meet the criteria.\n\n?? How do we indicate to the user that it's indexing\n-- Add some new cols to the document table\n-- origin (for later), status, created_at, added_by (nullable). \n-- the table can then render something based on whether it's finished or not.\n\nNotes to self:\n- when generating/checking the document ID for redis seq ID, we must remove any chunk IDs \n\n", "lineHeight": 1.25}, {"id": "PnSMmKP2D14McSb2R1b46", "type": "freedraw", "x": 189.47355501136167, "y": 301.32525922548007, "width": 178.30286803064314, "height": 1135.2264904648755, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "#99e9f2", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 1142118922, "version": 518, "versionNonce": 1346501194, "isDeleted": false, "boundElements": null, "updated": 1730156716944, "link": null, "locked": false, "points": [[17.794608618822735, 0], [16.360569601815044, 0], [6.442803963181598, 0], [-6.608156166392995, 2.1009105356353697], [-19.659116295967713, 7.203121836463891], [-33.72233926107721, 13.640394512154167], [-50.605437268033924, 21.36098199778936], [-61.63187172653872, 28.56410383425314], [-73.89953323337807, 36.9677459767943], [-83.03400024927612, 44.7918265922636], [-87.60123375722522, 48.703866899998246], [-94.4460586451277, 57.5007829359096], [-97.8564203410368, 62.3753093511028], [-101.27883278498804, 67.2601850792794], [-103.5684749129836, 72.14506080745598], [-109.79871165073992, 84.90576371601902], [-110.84712673040107, 88.47627669530064], [-112.07630303069345, 94.77900830220653], [-113.55854503986956, 106.19430052292435], [-114.7877213401618, 112.49703212983013], [-114.7877213401618, 116.10894236104551], [-114.7877213401618, 123.98476954143197], [-116.28201409737994, 135.44145901408345], [-116.28201409737994, 141.74419062098934], [-116.28201409737994, 146.62906634916592], [-116.28201409737994, 152.9317979560718], [-116.28201409737994, 164.3884874287233], [-116.28201409737994, 168.00039765993856], [-114.89617807254055, 177.5942107955736], [-112.44987621999795, 183.89694240247948], [-109.83486389486616, 191.77276958286592], [-106.17143649007335, 198.07550118977173], [-102.24289262835465, 205.9513283701582], [-98.06128305775223, 215.54514150579323], [-93.87967348714982, 225.13895464142826], [-89.69806391654738, 234.7327677770633], [-84.11856757306352, 244.32658091269826], [-79.9369580024611, 253.92039404833318], [-75.76739917990083, 263.49350855800145], [-72.98367638217987, 273.0873216936364], [-68.80206681157743, 282.6811348292714], [-66.01834401385659, 292.27494796490646], [-63.23462121613561, 301.8377131615912], [-60.45089841841475, 311.4315262972262], [-57.66717562069391, 320.99429149391085], [-56.28133959585438, 330.58810462954585], [-54.89550357101497, 340.1819177651809], [-53.59402278247022, 348.0577449455673], [-52.111780773294115, 359.5144344182188], [-52.111780773294115, 370.97112389087044], [-52.111780773294115, 382.42781336352186], [-52.111780773294115, 393.88450283617345], [-52.111780773294115, 407.2868631497087], [-52.111780773294115, 418.7435526223603], [-53.69042876680679, 432.20800881379625], [-58.113053298250904, 443.59225309556376], [-62.306713616895365, 453.18606623119877], [-75.32152150234371, 471.7630830364467], [-79.88875501029281, 475.67512334418126], [-90.87903722467148, 486.44875815992685], [-93.7832675028131, 488.10464823727483], [-99.91709825623285, 492.30646930854556], [-105.61710208013761, 495.2353248828548], [-111.31710590404228, 498.1641804571642], [-115.53486771877093, 499.9649609162799], [-119.74057878545744, 501.76574137539586], [-123.95834060018612, 502.6661316049538], [-128.17610241491477, 503.566521834512], [-131.08033269305653, 505.22241191185987], [-135.2980945077852, 506.12280214141794], [-138.20232478592698, 507.7786922187659], [-142.4200866006555, 508.67908244832387], [-145.32431687879728, 510.33497252567196], [-149.54207869352592, 512.1357529847879], [-152.4463089716677, 513.791643062136], [-155.35053924980946, 515.4475331394838], [-157.1219992119954, 516.2030329872739], [-158.8934591741815, 516.9585328350639], [-159.70085929300095, 517.64158749197], [-159.70085929300095, 518.324642148876], [-160.5082594118204, 518.324642148876], [-159.79726527733763, 518.324642148876], [-152.45835971970973, 518.324642148876], [-143.2877404596854, 518.324642148876], [-134.1171211996611, 518.324642148876], [-124.94650193963679, 519.442367951086], [-115.7999841756967, 522.8058946706989], [-104.6289407406868, 526.3971062759475], [-94.85578407855846, 531.1888381872733], [-80.74435812128056, 539.2716516273281], [-64.19868105950222, 547.7891362126871], [-50.08725510222432, 555.8719496527419], [-39.71156103799183, 564.7827081314709], [-35.1563782780849, 568.6947484392056], [-19.490405823378524, 582.1488553176583], [-17.39357566405623, 585.7607655488735], [-7.981941443190362, 597.8798110524642], [-4.053397581471664, 605.7556382328507], [-1.4383852563398776, 613.6314654132372], [1.176627068791909, 621.5072925936236], [2.2250421484530545, 625.1192028248388], [3.7916393939236936, 638.5836590162749], [5.177475418763098, 648.1774721519098], [5.177475418763098, 657.771285287545], [5.177475418763098, 669.2279747601964], [5.177475418763098, 680.6846642328479], [5.177475418763098, 692.1413537054995], [2.0322301797796563, 705.6058098969355], [-2.679612304674297, 719.0702660883715], [-7.391454789128378, 732.5347222798073], [-13.66989451905297, 745.9991784712432], [-18.38173700350692, 759.4636346626793], [-25.009648426652063, 775.0910972676511], [-37.91599957972167, 804.1830160640588], [-49.84624014138275, 837.7458380693063], [-54.81114833472043, 853.3733006742781], [-62.12595239626414, 873.7717965646088], [-67.3559770465277, 891.7071559648842], [-74.32130941485097, 909.64251536516], [-85.03442442426172, 947.9763706557661], [-90.26444907452529, 965.9117300560418], [-93.74711525868699, 983.8056922043837], [-97.22978144284856, 1001.7410516046593], [-100.71244762701026, 1019.676411004935], [-102.37545085681748, 1035.3038736099068], [-102.37545085681748, 1050.9313362148785], [-102.37545085681748, 1064.3957924063145], [-102.37545085681748, 1075.852481878966], [-102.37545085681748, 1082.1552134858716], [-102.37545085681748, 1090.0310406662584], [-101.15832530456726, 1096.3337722731642], [-98.71202345202467, 1102.6365038800698], [-96.61519329270237, 1106.2484141112852], [-95.65113344933589, 1108.7322492273074], [-93.72301376260279, 1111.2160843433294], [-92.84330915553083, 1112.7270840389094], [-91.08389994138679, 1114.2380837344892], [-88.1917204112872, 1115.8939738118372], [-83.98600934460057, 1117.6947542709534], [-79.43082658469365, 1120.6236098452628], [-73.74287350883104, 1122.5796299991298], [-66.40396795120313, 1125.7258211460912], [-57.233348691178946, 1127.9716220634944], [-53.027637624492314, 1128.8720122930526], [-43.85701836446801, 1131.1178132104558], [-36.51811280684023, 1132.1630938217816], [-29.179207249212325, 1133.2083744331073], [-21.840301691584415, 1134.2536550444336], [-16.152348615721806, 1135.2264904648755], [-8.81344305809403, 1135.2264904648755], [-4.607731991407398, 1135.2264904648755], [-1.7155524613078086, 1135.2264904648755], [1.176627068791909, 1135.2264904648755], [2.9360362829358184, 1135.2264904648755], [3.7313856537132413, 1135.2264904648755], [4.526735024490662, 1135.2264904648755], [5.322084395268085, 1135.2264904648755], [6.11743376604538, 1135.2264904648755], [6.912783136822803, 1135.2264904648755], [6.912783136822803, 1135.2264904648755]], "pressures": [], "simulatePressure": true, "lastCommittedPoint": [-9.795820373673564, 1189.9372454137604]}, {"id": "-3SFj2kOJTxFOJ1MjLagH", "type": "text", "x": -279.06565250091444, "y": 607.4139983574696, "width": 365.50787353515625, "height": 90, "angle": 5.941516806119972, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 1138255818, "version": 564, "versionNonce": 1219045206, "isDeleted": false, "boundElements": null, "updated": 1730156716944, "link": null, "locked": false, "text": "Option 3 Exploration\n--- RULED OUT---", "fontSize": 36, "fontFamily": 1, "textAlign": "left", "verticalAlign": "top", "baseline": 77, "containerId": null, "originalText": "Option 3 Exploration\n--- RULED OUT---", "lineHeight": 1.25}, {"type": "freedraw", "version": 1003, "versionNonce": 1861602454, "isDeleted": false, "id": "Z6R-8wjVsAZ6O7a7TnEyZ", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 188.0724521900731, "y": 1474.3607589619996, "strokeColor": "#2f9e44", "backgroundColor": "#99e9f2", "width": 178.30286803064314, "height": 2189.820105943901, "seed": 625512330, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1730156716944, "link": null, "locked": false, "points": [[17.794608618822735, 0], [16.360569601815044, 0], [6.442803963181598, 0], [-6.608156166392995, 4.052597583271466], [-19.659116295967713, 13.894620285501723], [-33.72233926107721, 26.311938988923934], [-50.605437268033924, 41.20473601907386], [-61.63187172653872, 55.09935630457535], [-73.89953323337807, 71.30974663766062], [-83.03400024927612, 86.4021790167399], [-87.60123375722522, 93.9483952062795], [-94.4460586451277, 110.91739986529738], [-97.8564203410368, 120.32022479988251], [-101.27883278498804, 129.74301326936057], [-103.5684749129836, 139.16580173883855], [-109.79871165073992, 163.78084026186076], [-110.84712673040107, 170.66825979993268], [-112.07630303069345, 182.82605254974663], [-113.55854503986956, 204.84583153668376], [-114.7877213401618, 217.00362428649763], [-114.7877213401618, 223.9708979641414], [-114.7877213401618, 239.1631480176856], [-116.28201409737994, 261.26278114419443], [-116.28201409737994, 273.4205738940084], [-116.28201409737994, 282.84336236348645], [-116.28201409737994, 295.0011551133004], [-116.28201409737994, 317.1007882398094], [-116.28201409737994, 324.068061917453], [-114.89617807254055, 342.5742587632289], [-112.44987621999795, 354.73205151304273], [-109.83486389486616, 369.92430156658696], [-106.17143649007335, 382.08209431640074], [-102.24289262835465, 397.2743443699449], [-98.06128305775223, 415.7805412157208], [-93.87967348714982, 434.28673806149664], [-89.69806391654738, 452.79293490727235], [-84.11856757306352, 471.2991317530483], [-79.9369580024611, 489.8053285988239], [-75.76739917990083, 508.27159837481395], [-72.98367638217987, 526.7777952205898], [-68.80206681157743, 545.2839920663655], [-66.01834401385659, 563.7901889121414], [-63.23462121613561, 582.2364951532384], [-60.45089841841475, 600.7426919990143], [-57.66717562069391, 619.1889982401112], [-56.28133959585438, 637.695195085887], [-54.89550357101497, 656.201391931663], [-53.59402278247022, 671.3936419852068], [-52.111780773294115, 693.4932751117159], [-52.111780773294115, 715.5929082382249], [-52.111780773294115, 737.6925413647341], [-52.111780773294115, 759.792174491243], [-52.111780773294115, 785.6449521776286], [-52.111780773294115, 807.7445853041379], [-53.69042876680679, 833.7171441998812], [-58.113053298250904, 855.6770325821398], [-62.306713616895365, 874.1832294279153], [-75.32152150234371, 910.0177745607821], [-79.88875501029281, 917.5639907503214], [-90.87903722467148, 938.3460305738954], [-93.7832675028131, 941.5401961567692], [-99.91709825623285, 949.6453913233123], [-105.61710208013761, 955.2950716980205], [-111.31710590404228, 960.9447520727289], [-115.53486771877093, 964.4184071441036], [-119.74057878545744, 967.8920622154794], [-123.95834060018612, 969.6288897511669], [-128.17610241491477, 971.365717286855], [-131.08033269305653, 974.5598828697285], [-135.2980945077852, 976.2967104054165], [-138.20232478592698, 979.4908759882904], [-142.4200866006555, 981.2277035239781], [-145.32431687879728, 984.421869106852], [-149.54207869352592, 987.8955241782276], [-152.4463089716677, 991.0896897611019], [-155.35053924980946, 994.283855343975], [-157.1219992119954, 995.7411933911616], [-158.8934591741815, 997.1985314383478], [-159.70085929300095, 998.516124741283], [-159.70085929300095, 999.833718044219], [-160.5082594118204, 999.833718044219], [-159.79726527733763, 999.833718044219], [-152.45835971970973, 999.833718044219], [-143.2877404596854, 999.833718044219], [-134.1171211996611, 999.833718044219], [-124.94650193963679, 1001.9897798126589], [-115.7999841756967, 1008.4779286528711], [-104.6289407406868, 1015.405275260729], [-94.85578407855846, 1024.6483919161706], [-80.74435812128056, 1040.2399126675734], [-64.19868105950222, 1056.669901884482], [-50.08725510222432, 1072.261422635885], [-39.71156103799183, 1089.4500261787252], [-35.1563782780849, 1096.996242368265], [-19.490405823378524, 1122.9488377291163], [-17.39357566405623, 1129.9161114067592], [-7.981941443190362, 1153.2934107664175], [-4.053397581471664, 1168.485660819962], [-1.4383852563398776, 1183.6779108735063], [1.176627068791909, 1198.8701609270502], [2.2250421484530545, 1205.8374346046937], [3.7916393939236936, 1231.809993500438], [5.177475418763098, 1250.3161903462133], [5.177475418763098, 1268.8223871919893], [5.177475418763098, 1290.9220203184977], [5.177475418763098, 1313.0216534450076], [5.177475418763098, 1335.1212865715163], [2.0322301797796563, 1361.0938454672603], [-2.679612304674297, 1387.0664043630036], [-7.391454789128378, 1413.0389632587471], [-13.66989451905297, 1439.0115221544904], [-18.38173700350692, 1464.9840810502342], [-25.009648426652063, 1495.1290187386073], [-37.91599957972167, 1551.2465153227236], [-49.84624014138275, 1615.9882589805993], [-54.81114833472043, 1646.1331966689727], [-62.12595239626414, 1685.4813239430005], [-67.3559770465277, 1720.0781299125035], [-74.32130941485097, 1754.6749358820077], [-85.03442442426172, 1828.6198691255383], [-90.26444907452529, 1863.2166750950419], [-93.74711525868699, 1897.7336269249731], [-97.22978144284856, 1932.3304328944764], [-100.71244762701026, 1966.9272388639797], [-102.37545085681748, 1997.0721765523522], [-102.37545085681748, 2027.2171142407246], [-102.37545085681748, 2053.189673136468], [-102.37545085681748, 2075.289306262978], [-102.37545085681748, 2087.447099012791], [-102.37545085681748, 2102.6393490663363], [-101.15832530456726, 2114.7971418161496], [-98.71202345202467, 2126.954934565963], [-96.61519329270237, 2133.9222082436067], [-95.65113344933589, 2138.7134566179184], [-93.72301376260279, 2143.504704992229], [-92.84330915553083, 2146.419381086601], [-91.08389994138679, 2149.3340571809736], [-88.1917204112872, 2152.528222763847], [-83.98600934460057, 2156.0018778352237], [-79.43082658469365, 2161.6515582099314], [-73.74287350883104, 2165.4246663047006], [-66.40396795120313, 2171.493580912161], [-57.233348691178946, 2175.8256679839346], [-53.027637624492314, 2177.5624955196226], [-43.85701836446801, 2181.894582591395], [-36.51811280684023, 2183.910899615584], [-29.179207249212325, 2185.9272166397727], [-21.840301691584415, 2187.943533663963], [-16.152348615721806, 2189.8201059439016], [-8.81344305809403, 2189.8201059439016], [-4.607731991407398, 2189.8201059439016], [-1.7155524613078086, 2189.8201059439016], [1.176627068791909, 2189.8201059439016], [2.9360362829358184, 2189.8201059439016], [3.7313856537132413, 2189.8201059439016], [4.526735024490662, 2189.8201059439016], [5.322084395268085, 2189.8201059439016], [6.11743376604538, 2189.8201059439016], [6.912783136822803, 2189.8201059439016], [6.912783136822803, 2189.8201059439016]], "lastCommittedPoint": null, "simulatePressure": true, "pressures": []}, {"type": "text", "version": 652, "versionNonce": 2106290122, "isDeleted": false, "id": "3D5WA_WWTEFBLoJ_RGaqx", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 5.941516806119972, "x": -284.22368370095563, "y": 1639.6459756815482, "strokeColor": "#2f9e44", "backgroundColor": "transparent", "width": 364.0318603515625, "height": 90, "seed": 1127275670, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1730156716944, "link": null, "locked": false, "fontSize": 36, "fontFamily": 1, "text": "Option 4 Exploration\n--- SELECTED ---", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Option 4 Exploration\n--- SELECTED ---", "lineHeight": 1.25, "baseline": 77}, {"id": "sLMmTdJXcmyif3heEwCP0", "type": "text", "x": 197.49954686092866, "y": 405.72127311427766, "width": 18, "height": 45, "angle": 0, "strokeColor": "#2f9e44", "backgroundColor": "#99e9f2", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 181068490, "version": 16, "versionNonce": 117164502, "isDeleted": false, "boundElements": null, "updated": 1730156716944, "link": null, "locked": false, "text": "", "fontSize": 36, "fontFamily": 1, "textAlign": "left", "verticalAlign": "top", "baseline": 32, "containerId": null, "originalText": "", "lineHeight": 1.25}, {"id": "CVnPEmMmuDSiEJc8JZW1S", "type": "text", "x": 232.68038823537063, "y": -492.2505337328683, "width": 1042.6591796875, "height": 425, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#99e9f2", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 1337002774, "version": 53, "versionNonce": 1323230858, "isDeleted": false, "boundElements": null, "updated": 1730156716944, "link": null, "locked": false, "text": "Problems with app today:\n- very, very slow when loading from forge backend.\n- platform is fairly limited in how it works, limitations around max pages etc.\n- complicated indexing process as a result of limitations (max queue push/min, max chunks to process, ...)\n- 60s max time per \n- async operations without message sequencing introduces bugs with\n- blogs dont show up. intentionally excluded, but simple to add.  \n\nQuestions\n- What is the simplest solution to create reliable, fast and sequenced indexing?\n- Does it comply with platform limits?\n\n\n\n\n\n", "fontSize": 20, "fontFamily": 1, "textAlign": "left", "verticalAlign": "top", "baseline": 418, "containerId": null, "originalText": "Problems with app today:\n- very, very slow when loading from forge backend.\n- platform is fairly limited in how it works, limitations around max pages etc.\n- complicated indexing process as a result of limitations (max queue push/min, max chunks to process, ...)\n- 60s max time per \n- async operations without message sequencing introduces bugs with\n- blogs dont show up. intentionally excluded, but simple to add.  \n\nQuestions\n- What is the simplest solution to create reliable, fast and sequenced indexing?\n- Does it comply with platform limits?\n\n\n\n\n\n", "lineHeight": 1.25}, {"id": "UfL76kvABiePyfF3WLxj3", "type": "text", "x": 233.9893171444217, "y": -20.334398331406106, "width": 768.167724609375, "height": 45, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#99e9f2", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 1866539414, "version": 93, "versionNonce": 1070604054, "isDeleted": false, "boundElements": null, "updated": 1730156716944, "link": null, "locked": false, "text": "Solving the buggy page selection experience.", "fontSize": 36, "fontFamily": 1, "textAlign": "left", "verticalAlign": "top", "baseline": 32, "containerId": null, "originalText": "Solving the buggy page selection experience.", "lineHeight": 1.25}, {"type": "text", "version": 239, "versionNonce": 2081785162, "isDeleted": false, "id": "2locbwAlmYTPIl7vnwdim", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1567.12480875018, "y": -25.318083208519965, "strokeColor": "#1e1e1e", "backgroundColor": "#99e9f2", "width": 287.49591064453125, "height": 45, "seed": 1617886474, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1730156716944, "link": null, "locked": false, "fontSize": 36, "fontFamily": 1, "text": "Supporting Blogs", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Supporting Blogs", "lineHeight": 1.25, "baseline": 32}, {"type": "text", "version": 7228, "versionNonce": 357662358, "isDeleted": false, "id": "jC_PuruGS_PRU6jyh27Wd", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1566.1341071795716, "y": 42.86754753926738, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 923.2791137695312, "height": 325, "seed": 371429398, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1730156716946, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "No significant decision required here, but we should consider expanding scope slightly to verify\nJSD linked KB pages also work. \n\nThere are a few different types of content that we miss today.\n\n- blogs\n- pages that are top level pages but are not children \n  of a homepageId for some reason. This seems to cover the service desk pages.\n\nfor blogs:\n- check if there are any blogs in the space when loading the initial space\n  children.\n- if there are blogs, then render a 'blogs' section. ", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "No significant decision required here, but we should consider expanding scope slightly to verify\nJSD linked KB pages also work. \n\nThere are a few different types of content that we miss today.\n\n- blogs\n- pages that are top level pages but are not children \n  of a homepageId for some reason. This seems to cover the service desk pages.\n\nfor blogs:\n- check if there are any blogs in the space when loading the initial space\n  children.\n- if there are blogs, then render a 'blogs' section. ", "lineHeight": 1.25, "baseline": 318}], "appState": {"gridSize": null, "viewBackgroundColor": "#ffffff"}, "files": {}}