{"type": "excalidraw", "version": 2, "source": "https://marketplace.visualstudio.com/items?itemName=pomdtr.excalidraw-editor", "elements": [{"id": "RgWZKHRlSNhw2TLVdBBAI", "type": "text", "x": 184.3610480187532, "y": -73.12566185611576, "width": 1002.240719739641, "height": 349.0809906445854, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 997811360, "version": 1203, "versionNonce": 719570080, "isDeleted": false, "boundElements": null, "updated": 1725955350708, "link": null, "locked": false, "text": "Conceptual design for Jira.\n\n- Treat Jira issues as adhoc sources for text search.\n- \"Chunk\" projects into windows (1 week?) \n- Use a map-reduce style approach to interrogate the project\n  with an LLM.\n---- This creates a small-enough project timeline.\n- Give the AI some more general tools around search/retrieve by ID\n- AI can then review the \"timeline\" + a summary of the last week to get the current project status\n- Store the chunks in S3/DynamoDB so they can be retrieved at different resolutions\n\nHow would versions/epics/roadmaps factor in??\n- Complex.\n", "fontSize": 19.9474851796906, "fontFamily": 1, "textAlign": "left", "verticalAlign": "top", "baseline": 340.99999999999994, "containerId": null, "originalText": "Conceptual design for Jira.\n\n- Treat Jira issues as adhoc sources for text search.\n- \"Chunk\" projects into windows (1 week?) \n- Use a map-reduce style approach to interrogate the project\n  with an LLM.\n---- This creates a small-enough project timeline.\n- Give the AI some more general tools around search/retrieve by ID\n- AI can then review the \"timeline\" + a summary of the last week to get the current project status\n- Store the chunks in S3/DynamoDB so they can be retrieved at different resolutions\n\nHow would versions/epics/roadmaps factor in??\n- Complex.\n", "lineHeight": 1.25}], "appState": {"gridSize": null, "viewBackgroundColor": "#ffffff"}, "files": {}}