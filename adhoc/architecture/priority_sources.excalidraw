{"type": "excalidraw", "version": 2, "source": "https://marketplace.visualstudio.com/items?itemName=pomdtr.excalidraw-editor", "elements": [{"type": "rectangle", "version": 163, "versionNonce": 503930220, "isDeleted": false, "id": "vo210x5MTZWWDI2xU9msN", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -438.1715894640122, "y": 135.04467348390608, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 153.9910441413844, "height": 153.9910441413844, "seed": 356787564, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "GyRPsGPzc5FYNCDpiZp17"}, {"id": "fNCaPMLyL0DxDiWBcUjfF", "type": "arrow"}, {"id": "NMM9P0G8-wRUO0Bm2jv2U", "type": "arrow"}], "updated": 1721105167468, "link": null, "locked": false}, {"type": "text", "version": 142, "versionNonce": 1634772716, "isDeleted": false, "id": "GyRPsGPzc5FYNCDpiZp17", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -383.5160484724215, "y": 199.54019555459828, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 44.679962158203125, "height": 25, "seed": 1777323116, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1721104980913, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "User", "textAlign": "center", "verticalAlign": "middle", "containerId": "vo210x5MTZWWDI2xU9msN", "originalText": "User", "lineHeight": 1.25, "baseline": 21}, {"type": "arrow", "version": 788, "versionNonce": 372096364, "isDeleted": false, "id": "fNCaPMLyL0DxDiWBcUjfF", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -275.67998890802676, "y": 192.46857739121725, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 704.1424042944748, "height": 37.70041382311797, "seed": 220171244, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [{"type": "text", "id": "3uUB9ZuZTOnGxZdubtpP2"}], "updated": 1721105024825, "link": null, "locked": false, "startBinding": {"elementId": "vo210x5MTZWWDI2xU9msN", "focus": -0.1848429992593106, "gap": 8.500556414600993}, "endBinding": {"elementId": "o566sdLOcoHFyq1MdAAOQ", "focus": 0.5756825150684977, "gap": 16.820913639761045}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [704.1424042944748, -37.70041382311797]]}, {"type": "text", "version": 44, "versionNonce": 171460692, "isDeleted": false, "id": "3uUB9ZuZTOnGxZdubtpP2", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 38.99343646646142, "y": 177.44824834819693, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 369.69964599609375, "height": 25, "seed": 1413331308, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1721104967545, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Adds a \"Correction\" from past replies", "textAlign": "center", "verticalAlign": "middle", "containerId": "fNCaPMLyL0DxDiWBcUjfF", "originalText": "Adds a \"Correction\" from past replies", "lineHeight": 1.25, "baseline": 21}, {"type": "rectangle", "version": 287, "versionNonce": 1682485972, "isDeleted": false, "id": "o566sdLOcoHFyq1MdAAOQ", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 445.2833290262091, "y": 89.07023019806945, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 289.8976921650355, "height": 289.8976921650355, "seed": 1116144596, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "Zj-Y5-P_9hRk3Kjz23-XN"}, {"id": "fNCaPMLyL0DxDiWBcUjfF", "type": "arrow"}, {"id": "3cjvq8mBJxN_mELyU5UKQ", "type": "arrow"}, {"id": "FkIieQASOZrgt9-OyUT6T", "type": "arrow"}, {"id": "eL7apPHSl-cZvTPvOfPMU", "type": "arrow"}], "updated": 1721105183461, "link": null, "locked": false}, {"type": "text", "version": 478, "versionNonce": 1784477420, "isDeleted": false, "id": "Zj-Y5-P_9hRk3Kjz23-XN", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 499.19227337532845, "y": 209.0190762805872, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 182.07980346679688, "height": 50, "seed": 69167444, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1721105158690, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "SourcesAPI\naddPrioritySources", "textAlign": "center", "verticalAlign": "middle", "containerId": "o566sdLOcoHFyq1MdAAOQ", "originalText": "SourcesAPI\naddPrioritySources", "lineHeight": 1.25, "baseline": 46}, {"type": "arrow", "version": 321, "versionNonce": 1929310164, "isDeleted": false, "id": "3cjvq8mBJxN_mELyU5UKQ", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1220.2069012444615, "y": 165.6569734236204, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 110.58630441200785, "height": 3.669414890011126, "seed": 983665108, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1721105274408, "link": null, "locked": false, "startBinding": {"elementId": "TrdSh6oxV9zzL9Jh8D45j", "focus": -0.271702223812271, "gap": 5.495917768058916}, "endBinding": {"elementId": "1ot4JhPZyxe8a9JtJE5qz", "focus": 0.1390429526981432, "gap": 21.72827185764436}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [110.58630441200785, 3.669414890011126]]}, {"type": "rectangle", "version": 455, "versionNonce": 467541844, "isDeleted": false, "id": "1ot4JhPZyxe8a9JtJE5qz", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1352.5214775141137, "y": 50.7309906340941, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 289.8976921650355, "height": 289.8976921650355, "seed": 1375463148, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "sp6AdhGCbYGFUgeMzas9X"}, {"id": "3cjvq8mBJxN_mELyU5UKQ", "type": "arrow"}], "updated": 1721105274408, "link": null, "locked": false}, {"type": "text", "version": 711, "versionNonce": 1842383596, "isDeleted": false, "id": "sp6AdhGCbYGFUgeMzas9X", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1362.4304676396002, "y": 145.67983671661185, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 270.0797119140625, "height": 100, "seed": 1043868012, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1721105314193, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Indexer.index_priority_docs\n\nadd \"_priority\" to the end \nof the namespace ID", "textAlign": "center", "verticalAlign": "middle", "containerId": "1ot4JhPZyxe8a9JtJE5qz", "originalText": "Indexer.index_priority_docs\n\nadd \"_priority\" to the end of the namespace ID", "lineHeight": 1.25, "baseline": 96}, {"type": "rectangle", "version": 509, "versionNonce": 1776584940, "isDeleted": false, "id": "TrdSh6oxV9zzL9Jh8D45j", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 924.813291311367, "y": 56.431326948645506, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 289.8976921650355, "height": 289.8976921650355, "seed": 1207654612, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "UgsuzB5xcCPX2KPCf0LLk"}, {"id": "FkIieQASOZrgt9-OyUT6T", "type": "arrow"}, {"id": "3cjvq8mBJxN_mELyU5UKQ", "type": "arrow"}], "updated": 1721105014226, "link": null, "locked": false}, {"type": "text", "version": 812, "versionNonce": 1421642604, "isDeleted": false, "id": "UgsuzB5xcCPX2KPCf0LLk", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 971.922209720545, "y": 151.38017303116328, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 195.6798553466797, "height": 100, "seed": 1648434772, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1721105014226, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "DocumentService.\nCreate document\n\nURL: eesel://type/id", "textAlign": "center", "verticalAlign": "middle", "containerId": "TrdSh6oxV9zzL9Jh8D45j", "originalText": "DocumentService.\nCreate document\n\nURL: eesel://type/id", "lineHeight": 1.25, "baseline": 96}, {"type": "arrow", "version": 332, "versionNonce": 831863404, "isDeleted": false, "id": "FkIieQASOZrgt9-OyUT6T", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 749.8387764369064, "y": 228.25988574360593, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 158.1818297266093, "height": 8.001270989504462, "seed": 614889964, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1721105303707, "link": null, "locked": false, "startBinding": {"elementId": "o566sdLOcoHFyq1MdAAOQ", "focus": 0.015196597151141533, "gap": 14.657755245661917}, "endBinding": {"elementId": "TrdSh6oxV9zzL9Jh8D45j", "focus": -0.07024598191666749, "gap": 16.79268514785133}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [158.1818297266093, -8.001270989504462]]}, {"type": "arrow", "version": 102, "versionNonce": 1500110676, "isDeleted": false, "id": "NMM9P0G8-wRUO0Bm2jv2U", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -275.6078416037214, "y": 243.74956985812895, "strokeColor": "#1e1e1e", "backgroundColor": "#ffc9c9", "width": 167.15456973578677, "height": 113.85890982002866, "seed": 270179924, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1721105170481, "link": null, "locked": false, "startBinding": {"elementId": "vo210x5MTZWWDI2xU9msN", "focus": -0.2053141999030889, "gap": 8.572703718906354}, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [167.15456973578677, 113.85890982002866]]}, {"type": "rectangle", "version": 48, "versionNonce": 978233836, "isDeleted": false, "id": "u5sMBK8PEzYQNugQgR4_D", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -85.03548190495007, "y": 269.58988981728436, "strokeColor": "#1e1e1e", "backgroundColor": "#b2f2bb", "width": 195.41741969111308, "height": 195.41741969111308, "seed": 1380487788, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "QwVKQNEwCf6GpzS0CSbPu"}, {"id": "eL7apPHSl-cZvTPvOfPMU", "type": "arrow"}], "updated": 1721105197611, "link": null, "locked": false}, {"type": "text", "version": 72, "versionNonce": 1412297964, "isDeleted": false, "id": "QwVKQNEwCf6GpzS0CSbPu", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -76.93669637579978, "y": 329.7985996628409, "strokeColor": "#1e1e1e", "backgroundColor": "#ffc9c9", "width": 179.2198486328125, "height": 75, "seed": 698676588, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1721105193686, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Load CSV, split \ninto rows/validate\non frontend.", "textAlign": "center", "verticalAlign": "middle", "containerId": "u5sMBK8PEzYQNugQgR4_D", "originalText": "Load CSV, split into rows/validate on frontend.", "lineHeight": 1.25, "baseline": 71}, {"type": "arrow", "version": 36, "versionNonce": 343637332, "isDeleted": false, "id": "eL7apPHSl-cZvTPvOfPMU", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 122.49458776701715, "y": 364.87606966667005, "strokeColor": "#1e1e1e", "backgroundColor": "#ffc9c9", "width": 313.3138795047597, "height": 107.39882983023983, "seed": 82098156, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1721105183461, "link": null, "locked": false, "startBinding": {"elementId": "u5sMBK8PEzYQNugQgR4_D", "focus": 0.26846020843577983, "gap": 12.112649980854144}, "endBinding": {"elementId": "o566sdLOcoHFyq1MdAAOQ", "focus": 0.151441151230329, "gap": 9.474861754432197}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [313.3138795047597, -107.39882983023983]]}, {"type": "text", "version": 21, "versionNonce": 1271298921, "isDeleted": false, "id": "tdjxd-4bZkEroOuXD9W0c", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 499.23496302130184, "y": 50.062309778636745, "strokeColor": "#1e1e1e", "backgroundColor": "#b2f2bb", "width": 174.43984985351562, "height": 25, "seed": 1017157076, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1721699136789, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "New API blueprint", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "New API blueprint", "lineHeight": 1.25, "baseline": 18}, {"type": "text", "version": 81, "versionNonce": 377490343, "isDeleted": false, "id": "XNgAQT-UiwXLtog8fdW8V", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1005.9415633127037, "y": 14.945902361388903, "strokeColor": "#1e1e1e", "backgroundColor": "#b2f2bb", "width": 112.21989440917969, "height": 25, "seed": 1770128468, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1721699136790, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "New service", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "New service", "lineHeight": 1.25, "baseline": 18}, {"type": "text", "version": 63, "versionNonce": 437593673, "isDeleted": false, "id": "2i7BzeYaD4D5ufgLb86F-", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1436.6366263020316, "y": 9.045969991672223, "strokeColor": "#1e1e1e", "backgroundColor": "#b2f2bb", "width": 74.99992370605469, "height": 25, "seed": 291879380, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1721699136791, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Existing", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Existing", "lineHeight": 1.25, "baseline": 18}, {"type": "rectangle", "version": 225, "versionNonce": 884669223, "isDeleted": false, "id": "_nhD_7WrErXzQQoHXmPYE", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 252.42822756818833, "y": -444.98656262926124, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 919.1797124188506, "height": 267.2933202791226, "seed": 739522823, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [], "updated": 1721699161997, "link": null, "locked": false}, {"type": "text", "version": 502, "versionNonce": 572456519, "isDeleted": false, "id": "CC_vwUxx-1hPg1y5q4QNK", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 311.34010574876385, "y": -406.33046291089505, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 774.0792846679688, "height": 200, "seed": 995459111, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1721699161997, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "EESEL_ADHOC_SOURCES\nid (str)\ntitle (str)\nbody -- the whole macro (str)\ncreated_at (timestamp)\ncreated_by (str, user_id)\nsource_key (str) == canned_responses, zendesk_macros, corrected_replies, ...\n", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "EESEL_ADHOC_SOURCES\nid (str)\ntitle (str)\nbody -- the whole macro (str)\ncreated_at (timestamp)\ncreated_by (str, user_id)\nsource_key (str) == canned_responses, zendesk_macros, corrected_replies, ...\n", "lineHeight": 1.25, "baseline": 193}], "appState": {"gridSize": null, "viewBackgroundColor": "#ffffff"}, "files": {}}