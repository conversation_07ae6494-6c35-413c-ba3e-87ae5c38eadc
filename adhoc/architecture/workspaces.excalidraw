{"type": "excalidraw", "version": 2, "source": "https://marketplace.visualstudio.com/items?itemName=pomdtr.excalidraw-editor", "elements": [{"id": "3KicSNxMbsZHtxol2p9ja", "type": "rectangle", "x": 369.6947634607759, "y": -6.607712562486938, "width": 318.58983881350974, "height": 209.3170598778608, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "seed": 1684118439, "version": 157, "versionNonce": 212080521, "isDeleted": false, "boundElements": [{"type": "text", "id": "S9L0uu0aoi1rnXZZUH1RL"}, {"id": "yXJ8E2YIR0FlU5x160E3L", "type": "arrow"}, {"id": "58DDj0x8vlIRmAL_2oqUY", "type": "arrow"}], "updated": 1727921183475, "link": null, "locked": false}, {"id": "S9L0uu0aoi1rnXZZUH1RL", "type": "text", "x": 479.2997262024917, "y": 85.55081737644346, "width": 99.37991333007812, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 295996199, "version": 192, "versionNonce": 1712764521, "isDeleted": false, "boundElements": null, "updated": 1727921144052, "link": null, "locked": false, "text": "Workspace", "fontSize": 20, "fontFamily": 1, "textAlign": "center", "verticalAlign": "middle", "baseline": 18, "containerId": "3KicSNxMbsZHtxol2p9ja", "originalText": "Workspace", "lineHeight": 1.25}, {"type": "rectangle", "version": 203, "versionNonce": 1451291305, "isDeleted": false, "id": "npRvMYb5UWPIr-zpAzdyY", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -97.26794145056897, "y": 414.3402811375057, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 318.58983881350974, "height": 209.3170598778608, "seed": 1268433063, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [], "updated": 1727921207721, "link": null, "locked": false}, {"type": "rectangle", "version": 254, "versionNonce": 754890121, "isDeleted": false, "id": "-p7t7cA4qFJDYTq7lmBj5", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -134.99858630554675, "y": 388.1027253925728, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 318.58983881350974, "height": 209.3170598778608, "seed": 519710055, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [], "updated": 1727921207721, "link": null, "locked": false}, {"type": "rectangle", "version": 311, "versionNonce": 947985513, "isDeleted": false, "id": "R1NHvN9L9NR2F79dI6kXI", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -55.07026385375286, "y": 451.1913098076725, "strokeColor": "#1e1e1e", "backgroundColor": "#ffc9c9", "width": 318.58983881350974, "height": 209.3170598778608, "seed": 1886123591, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "F7xIrTNwB6sENl7Q_2b4m"}, {"id": "58DDj0x8vlIRmAL_2oqUY", "type": "arrow"}], "updated": 1727921207721, "link": null, "locked": false}, {"type": "text", "version": 360, "versionNonce": 953395017, "isDeleted": false, "id": "F7xIrTNwB6sENl7Q_2b4m", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 81.88467447390042, "y": 543.3498397466029, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 44.679962158203125, "height": 25, "seed": 809735527, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1727921207721, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "User", "textAlign": "center", "verticalAlign": "middle", "containerId": "R1NHvN9L9NR2F79dI6kXI", "originalText": "User", "lineHeight": 1.25, "baseline": 18}, {"type": "rectangle", "version": 249, "versionNonce": 794980711, "isDeleted": false, "id": "Rx1BaJsK05JqpQUM9TKNj", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 840.5703436288784, "y": 460.1329643389739, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 318.58983881350974, "height": 209.3170598778608, "seed": 1222112551, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "O1gERDaCoGcaG31H4f1dO"}, {"id": "yXJ8E2YIR0FlU5x160E3L", "type": "arrow"}], "updated": 1727921200787, "link": null, "locked": false}, {"type": "text", "version": 343, "versionNonce": 1023257735, "isDeleted": false, "id": "O1gERDaCoGcaG31H4f1dO", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 944.1052990463754, "y": 539.7914942779043, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 111.51992797851562, "height": 50, "seed": 1552726087, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1727921200787, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Namespace\n(aka \"Bot\")", "textAlign": "center", "verticalAlign": "middle", "containerId": "Rx1BaJsK05JqpQUM9TKNj", "originalText": "Namespace\n(aka \"Bot\")", "lineHeight": 1.25, "baseline": 43}, {"id": "yXJ8E2YIR0FlU5x160E3L", "type": "arrow", "x": 700.5161830628358, "y": 159.97978927913064, "width": 234.66592187576953, "height": 283.28676134076443, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffc9c9", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "seed": 966171369, "version": 109, "versionNonce": 2044950215, "isDeleted": false, "boundElements": [{"type": "text", "id": "9F2DMqd04zHBdEzGa4mnh"}], "updated": 1727921200788, "link": null, "locked": false, "points": [[0, 0], [234.66592187576953, 283.28676134076443]], "lastCommittedPoint": null, "startBinding": {"elementId": "3KicSNxMbsZHtxol2p9ja", "focus": -0.49070136429643874, "gap": 12.231580788550218}, "endBinding": {"elementId": "Rx1BaJsK05JqpQUM9TKNj", "focus": 0.14628319142797716, "gap": 16.86641371907882}, "startArrowhead": null, "endArrowhead": "arrow"}, {"id": "9F2DMqd04zHBdEzGa4mnh", "type": "text", "x": 593.4195381788662, "y": 302.07111400829183, "width": 88.79994201660156, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffc9c9", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 1763948551, "version": 9, "versionNonce": 1506210249, "isDeleted": false, "boundElements": null, "updated": 1727921171237, "link": null, "locked": false, "text": "has many", "fontSize": 20, "fontFamily": 1, "textAlign": "center", "verticalAlign": "middle", "baseline": 18, "containerId": "yXJ8E2YIR0FlU5x160E3L", "originalText": "has many", "lineHeight": 1.25}, {"id": "58DDj0x8vlIRmAL_2oqUY", "type": "arrow", "x": 229.14400235725574, "y": 430.64860893074274, "width": 167.06654855951857, "height": 215.74741107142302, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffc9c9", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "seed": 191338215, "version": 168, "versionNonce": 703015177, "isDeleted": false, "boundElements": [{"type": "text", "id": "CUA3vi3SVS8o2gAtfpKrj"}], "updated": 1727921207721, "link": null, "locked": false, "points": [[0, 0], [167.06654855951857, -215.74741107142302]], "lastCommittedPoint": null, "startBinding": {"elementId": "R1NHvN9L9NR2F79dI6kXI", "focus": 0.11705757005338831, "gap": 20.542700876929757}, "endBinding": {"elementId": "3KicSNxMbsZHtxol2p9ja", "focus": 0.17597957222902988, "gap": 12.191850543945861}, "startArrowhead": null, "endArrowhead": "arrow"}, {"id": "CUA3vi3SVS8o2gAtfpKrj", "type": "text", "x": 169.77441198845287, "y": 290.4532303811266, "width": 197.25985717773438, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffc9c9", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 19545705, "version": 19, "versionNonce": 452777991, "isDeleted": false, "boundElements": null, "updated": 1727921186688, "link": null, "locked": false, "text": "has access to many", "fontSize": 20, "fontFamily": 1, "textAlign": "center", "verticalAlign": "middle", "baseline": 18, "containerId": "58DDj0x8vlIRmAL_2oqUY", "originalText": "has access to many", "lineHeight": 1.25}, {"id": "hNeM9OMHjq7NXNQGgpfbf", "type": "text", "x": 730.9507418389171, "y": 15.474376645175482, "width": 773.1155522742414, "height": 81.52940830559311, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffc9c9", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 1363636807, "version": 285, "versionNonce": 1238545991, "isDeleted": false, "boundElements": null, "updated": 1727921260858, "link": null, "locked": false, "text": "User's get provisioned a personal workspace on sign-up\nCan create other workspaces if they want, otherwise, just invite users.\n", "fontSize": 21.741175548158157, "fontFamily": 1, "textAlign": "left", "verticalAlign": "top", "baseline": 73.00000000000003, "containerId": null, "originalText": "User's get provisioned a personal workspace on sign-up\nCan create other workspaces if they want, otherwise, just invite users.\n", "lineHeight": 1.25}], "appState": {"gridSize": null, "viewBackgroundColor": "#ffffff"}, "files": {}}