
#!/bin/bash

WIKI_API="https://wiki.nhrl.io/wiki/api.php"
CONTINUE_PARAM=""
SCRAPE_QUEUE_URL="https://sqs.us-east-1.amazonaws.com/059782158527/EeselAiCrawlerQueue"  # Replace with your SQS Queue URL
PAGE_COUNT=0
MAX_PAGES=10

while :; do
    RESPONSE=$(curl -s "$WIKI_API?action=query&list=allpages&format=json&aplimit=max$CONTINUE_PARAM")
    CONTINUE_PARAM=$(echo $RESPONSE | jq -r 'select(.continue) | "&apcontinue=" + .continue.apcontinue')

    PAGE_TITLES=$(echo $RESPONSE | jq -r '.query.allpages[] | .title')
    for TITLE in $PAGE_TITLES; do
        URL="https://wiki.nhrl.io/wiki/index.php/$TITLE"
        
        # Generate a UUID for the run_id
        # Put here if we want separate pages.
        RUN_ID=$(uuidgen)

        # Create the task in JSON format
        TASK_JSON=$(cat <<EOF
{
  "run_id": "$RUN_ID",
  "url": "$URL",
  "max_pages": 1,
  "ingest_jwt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0ZWFtX2lkIjoiMTcwOWI0YWItNWNiYS00MWEzLWE2YWEtMWE2OTM3ZjFiOTBmIiwiZXhwIjoxMDMzNDk3NzUxMH0.oT4H7CbnB5Pa_PEZJ78xB0SHRVSRWb6MNN6jgxHMwjc",
  "user_id": "auth0|650049009414f9ea13f6af91"
}
EOF
)

        # Send the task to SQS
        aws sqs send-message --queue-url "$SCRAPE_QUEUE_URL" --message-body "$TASK_JSON"
    done

    PAGE_COUNT=$((PAGE_COUNT + 1))
    if [ -z "$CONTINUE_PARAM" ] || [ "$PAGE_COUNT" -ge "$MAX_PAGES" ]; then
        break
    fi
done
