#!/bin/bash

WIKI_API="https://wiki.nhrl.io/wiki/api.php"
CONTINUE_PARAM=""

while :; do
    RESPONSE=$(curl -s "$WIKI_API?action=query&list=allpages&format=json&aplimit=max$CONTINUE_PARAM")
    CONTINUE_PARAM=$(echo $RESPONSE | jq -r 'select(.continue) | "&apcontinue=" + .continue.apcontinue')

    PAGE_TITLES=$(echo $RESPONSE | jq -r '.query.allpages[] | .title')
    for TITLE in $PAGE_TITLES; do
        URL="https://wiki.nhrl.io/wiki/index.php/$TITLE"
    done

    if [ -z "$CONTINUE_PARAM" ]; then
        break
    fi
done
