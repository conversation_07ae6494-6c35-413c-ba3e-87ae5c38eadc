import json
import re
import xmltodict
from time import sleep
import openai
import os

# Configuration for OpenAI API
openai.api_key = '-----'

def sanitize_text(text):
    """
    Strips out URLs and replaces them with a placeholder text.
    """
    return re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '**link redacted**', text)

SYSTEM_PROMPT = '''
Identify common issues and general knowledge that could be useful to multiple users.
YOU MUST ONLY use information provided to create your answer, YOU MUST NOT use any outside information.

Extract and structure your findings in the form of Question and Answer (Q&A) entries for a knowledge base, with reference quotes that contain the answer.
The "reference-quote" should contain the snippet of information used to formulate the "answer", not the "question".

If there is no viable answer, just respond with <result></result>

ALWAYS focus on information that can be generalized and applicable to a wider audience, suitable for putting on a public website.
DO NOT include information specific to this exchange, such as the subject/title of the ticket, the created date, or any other metadata.
DO NOT create candidate articles based on details that apply only to this specific exchange.
DO NOT say anything other than the Q&A items.
ALWAYS provide sufficient detail and relevant keywords so it can easily be retrieved in a search system.
DO NOT refer to the email in any candidate articles.
Articles should be written as if they were to appear on a public knowledge base.
NEVER say things like "According to the information provided", "Based on the ticket", etc.

ALWAYS include as much detail in your answer as is available in the information provided.
DO NOT recommend articles specific to a particular customer. 
DO NOT include PII, names or actual person's email addresses in articles.

DO NOT include articles that do not contain a helpful answer.

YOU MUST escape reserved characters like & with &amp; to prevent parse errors.
Remove any emojis

Respond with XML, for example:
<result>
    <qa>
        <q>How do I reset my password?</q>
        <a>Click the 'reset your password' button</a>
    </qa>
</result>
'''

def xml_to_json(xml_string):
    """
    Parse the XML string into a Python dictionary.
    """
    return xmltodict.parse(xml_string)
def process_conversations(file_path):
    result = []

    # Load the JSON data from the file
    with open(file_path, 'r') as file:
        conversations = json.load(file)

    total_conversations = len(conversations)

    for i, conversation in enumerate(conversations):

        if i > 50: # just stop at 50
            break

        formatted_conversation_json = json.dumps(conversation, indent=4)

        try:
            response = openai.ChatCompletion.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": SYSTEM_PROMPT},
                    {"role": "user", "content": formatted_conversation_json},
                    {"role": "assistant", "content": "<result>"}
                ],
                temperature=0,
                max_tokens=500
            )

            item = response['choices'][0]['message']['content']
            try:
                print(f"{i + 1}/{total_conversations} conversations processed.")
                result.append({
                    "article": xml_to_json("<result>" + item if not item.startswith("<result>") else item),
                    "source": conversation
                })

                # Write the current result to the output file progressively
                with open('output/processed_conversations.json', 'w') as file:
                    json.dump(result, file, indent=4)

            except Exception as f:
                print(f"Exception while parsing: {f}")

            # sleep(1)
        except Exception as e:
            print(f"Exception: {e}")

    # Final write to ensure all data is saved
    os.makedirs('output', exist_ok=True)
    with open('output/processed_conversations.json', 'w') as file:
        json.dump(result, file, indent=4)

if __name__ == "__main__":
    process_conversations('prepared_messages.json')
