import csv
import json
from datetime import datetime

def parse_csv_to_json(csv_file_path, json_file_path):
    email_chains = {}

    with open(csv_file_path, mode='r', encoding='utf-8-sig') as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            case_id = row['Case Id']
            subject = row['Subject']
            name = row['From Name']
            body = row['Text Body']
            date = row['Message Date']
            
            # Parse the date with the given format
            date_obj = datetime.strptime(date, '%d/%m/%Y, %I:%M %p')
            
            # Strip out lines starting with '>'
            body_lines = body.split('\n')
            filtered_body = '\n'.join(line for line in body_lines if not line.startswith('>'))
            
            email_entry = {
                'date': date_obj,
                'email': f"{name}: {filtered_body}"
            }
            
            if case_id not in email_chains:
                email_chains[case_id] = {'subject': subject, 'emails': []}
            
            email_chains[case_id]['emails'].append(email_entry)

    # Sort emails by date
    for case_id, data in email_chains.items():
        data['emails'].sort(key=lambda x: x['date'])
        # Remove the date from the final output
        data['emails'] = [email['email'] for email in data['emails']]

    result = [{'case_id': case_id, 'subject': data['subject'], 'emails': data['emails']} for case_id, data in email_chains.items()]

    with open(json_file_path, mode='w', encoding='utf-8') as jsonfile:
        json.dump(result, jsonfile, indent=4)

csv_file_path = './raw-messages-export.csv'
json_file_path = './messages.json'

parse_csv_to_json(csv_file_path, json_file_path)


