import jwt
import datetime
import requests

# Replace these with your actual values
secret = 'xx'  # This should match process.env.OAUTH_JWT_SECRET
namespace_id = 'xx'
upsert_token = 'xxx'
freshdesk_access_token = 'xx'
freshdesk_domain = 'https://designcrowd.freshdesk.com'
api_url = 'https://dashboard.eesel.ai/api/integrations/freshdesk/setup'

# Create the payload for the JWT
payload = {
    'namespaceId': namespace_id,
    'upsertToken': upsert_token,
    'exp': datetime.datetime.utcnow() + datetime.timedelta(minutes=30)  # Token expiration
}

# Generate the JWT
token = jwt.encode(payload, secret, algorithm='HS256')
print(f"Generated JWT: {token}")

# Set up the headers for the request
headers = {
    'freshdesk-access-token': freshdesk_access_token,
    'freshdesk-domain': freshdesk_domain,
    'authorization': token,
    'Content-Type': 'application/json'
}

# Make the request to set up the Freshdesk connection
response = requests.post(api_url, headers=headers, json={})

# Print the response from the server
print(f"Response Status Code: {response.status_code}")
print(f"Response Body: {response.json()}")
