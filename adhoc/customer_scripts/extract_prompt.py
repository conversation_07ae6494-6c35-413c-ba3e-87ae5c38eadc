import re

def extract_replies(text):
    # Use regex to find each reply by splitting on the "Title:" keyword
    replies = re.split(r"(?=Title:)", text)

    # Dictionary to store title and body of each reply
    macros = []

    for reply in replies:
        # Use regex to extract title and body
        title_match = re.search(r"Title:\s*(.*)", reply)
        body_match = re.search(r"Body:(.*)", reply, re.DOTALL)

        if title_match and body_match:
            title = title_match.group(1).strip()
            body = body_match.group(1).strip()

            macro = {
                "title": title,
                "body": body,
            }
            macros.append(macro)

    return macros

