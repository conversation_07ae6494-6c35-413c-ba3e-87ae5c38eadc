#!/bin/bash

# Function to extract filename from URL
get_filename() {
    echo $(basename "$1")
}

# Create PDF directory if it doesn't exist
mkdir -p PDF

# Download PDF files using curl
while IFS= read -r url; do
    filename=$(get_filename "$url")
    curl -L -o "PDF/$filename" "$url"
done <<EOF
    # <FILES GO HERE>
EOF

# Create CSV directory if it doesn't exist
mkdir -p CSV

# Download CSV file using curl
csv_url="# <FILES GO HERE>"
csv_filename=$(get_filename "$csv_url")
curl -L -o "CSV/$csv_filename" "$csv_url"
