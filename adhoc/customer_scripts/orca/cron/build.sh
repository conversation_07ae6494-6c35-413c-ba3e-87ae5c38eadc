#!/bin/bash

# Step 0: Clean directory ./build
echo "Cleaning ./build directory..."
rm -rf ./build
mkdir ./build

# Step 1: Copy requirements.txt into ./build
echo "Copying requirements.txt into ./build..."
cp requirements.txt ./build/

# Step 2: Pip install into the folder so it can be uploaded to Lambda
echo "Installing dependencies in ./build..."
pip install -r ./build/requirements.txt -t ./build/

cp ./lambda_function.py ./build

# Step 3: Zip up ./build into ./build.zip for uploading
echo "Creating build.zip..."
cd ./build
zip -r ../build.zip .
cd ..

echo "Done. The build.zip file is ready for uploading to AWS Lambda."