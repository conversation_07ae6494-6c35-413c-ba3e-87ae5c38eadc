import requests
import time
import pprint
from datetime import datetime, timedelta
import os
from markdownify import markdownify as md

project_version_id = 'fee00242-8150-419a-8a87-7707cc96e614'
api_token = os.getenv('API_TOKEN')
dashboard_base_url = "https://oracle.eesel.app"
eesel_token = os.getenv('EESEL_TOKEN') 

eesel_namespace = "1860bbcb-eb4d-4ec3-aff7-42e080e16c22"


def html_to_markdown(html_text):
    return md(html_text, strip=[
        "script",
        "style",
        "link",
        "meta",
        "img",
        "noscript",
        "head",
        "nav",
        "footer",
    ])

def safely_make_request(url, headers, method='get', data=None, params=None, max_retries=5, rate_limit_per_minute=100):
    delay_between_requests = 60.0 / rate_limit_per_minute

    for attempt in range(max_retries):
        try:
            if method == 'get':
                response = requests.get(url, headers=headers, params=params)
            # Add other methods as needed

            if response.status_code == 200:
                time.sleep(delay_between_requests)
                return response
            elif response.status_code == 429:
                reset_time = int(response.headers.get('x-ratelimit-reset', '60'))
                print(f"Rate limit hit. Attempt {attempt + 1} of {max_retries}. Waiting for {reset_time} seconds.")
                time.sleep(reset_time)
            else:
                print(f"Error {response.status_code} encountered: {response.text}")
                return None

        except requests.RequestException as e:
            print(f"Request failed: {e}")
            return None

        time.sleep(delay_between_requests)

    print(f"Exceeded maximum retries ({max_retries}) for URL: {url}")
    return None


def make_request(url):
    headers = {
        'accept': 'application/json',
        'api_token': api_token
    }

    response = safely_make_request(url, headers=headers)
    response.raise_for_status()  # Raise an exception for HTTP errors

    return response.json()

def make_request_to_dashboard(api_url):
    headers = {
        'accept': 'application/json',
        'Authorization': f'Bearer {eesel_token}'
    }

    response = requests.get(api_url, headers=headers)
    response.raise_for_status() 

    return response.json()

def make_post_to_dashboard(req_url, payload):
    headers = {
        'Authorization': f'Bearer {eesel_token}',
        'Content-Type': 'application/json'
    }
    response = requests.post(req_url, json=payload, headers=headers)
    response.raise_for_status()

    return response
    
def delete_documents(namespace_id, urls):
    api_url = f'{dashboard_base_url}/namespaces/{namespace_id}/urls/jwt'
    data = {
        'urls': urls
    }
    print("Deleted URLs")
    return make_post_to_dashboard(api_url, data)

def get_article(article_id):
    url = f'https://apihub.document360.io/v2/Articles/{article_id}/en?isForDisplay=true&isPublished=true&appendSASToken=true'
    response = make_request(url)["data"]
    return response

def get_category(article_id):
    url = f'https://apihub.document360.io/v2/Categories/{article_id}/content/en?isForDisplay=true&isPublished=false&appendSASToken=true'
    response = make_request(url)["category"]

    return response

def get_eesel_documents(namespace_id):
    api_url = f'{dashboard_base_url}/namespaces/{namespace_id}/documents/jwt'
    documents = make_request_to_dashboard(api_url)
    return documents

def get_all_categories(project_version_id):
    url = f'https://apihub.document360.io/v2/ProjectVersions/{project_version_id}/categories?excludeArticles=false&langCode=en&includeCategoryDescription=false'
    return make_request(url)["data"]

def transform_article_for_eesel(item):
    # Transform each item to the required format
    return {
        "pageTitle": item.get("title", ""),
        "pageBody": html_to_markdown(item.get("html_content", "")),
        "source": "https://docs.orcasecurity.io/" + item.get("slug", "")  # Assuming 'slug' field is the source
    }

def parse_modified_at(modified_at_str):
    """Parses the modified_at timestamp, handling both with and without fractional seconds."""
    for fmt in ('%Y-%m-%dT%H:%M:%S.%fZ', '%Y-%m-%dT%H:%M:%SZ'):
        try:
            return datetime.strptime(modified_at_str, fmt)
        except ValueError:
            continue
    raise ValueError(f"time data '{modified_at_str}' does not match any valid format")


def extract_articles_from_categories(categories, articles):
    if articles is None:
        articles = []

    for category in categories:
        if not category['hidden']:
            # If the category itself is an article, drop specific keys and add it to articles
            category_article = category.copy()
            if "articles" in category_article:
                category_article.pop('articles', None)
            if "child_categories" in category_article:
                category_article.pop('child_categories', None)
            
            category_article["is_category"] = True
            if category_article["id"] == "fee00242-8150-419a-8a87-7707cc96e614":
                print("Category")
            articles.append(category_article)
            
            # Extend articles with the articles list from the category
            articles.extend(category.get('articles', []))
            
            # Recursively process child categories
            child_categories = category.get('child_categories', [])
            if child_categories:
                extract_articles_from_categories(child_categories, articles)

    return articles

def filter_and_transform_articles(articles, exclude_patterns):
    resulting_articles = []

    for article in articles:
        slug = article.get("slug", "")
    
        if article.get("status") == 3 and not article.get("hidden") and not any(pattern in slug for pattern in exclude_patterns):
            resulting_articles.append({
                "project_version_id": article.get('project_version_id', ''),
                "slug": slug,
                "id": article.get('id', ''),
                "title": article.get('title', ''),
                "content": article.get('content', ''),
                "html_content": article.get('html_content', ''),
                "hidden": article.get('hidden', None),
                "url": get_url_from_slug(slug),
                "is_category": article.get('is_category', False),
                "modified_at": article.get("modified_at", None)
            })

    return resulting_articles

def get_all_articles(project_version_id, exclude_patterns):
    categories = get_all_categories(project_version_id)
    articles = extract_articles_from_categories(categories, [])
    filtered_articles = filter_and_transform_articles(articles, exclude_patterns)
    return filtered_articles

def get_url_from_slug(slug):
    return "https://docs.orcasecurity.io/" + slug 

def transform_article_to_eesel_payload(item):
    return {
        "pageTitle": item.get("title", ""),
        "pageBody": "todo", #html_to_markdown(item.get("html_content", "")),
        "source": "https://docs.orcasecurity.io/" + item.get("slug", "")  # Assuming 'slug' field is the source
    }

def diff_articles_and_documents(articles, documents_in_eesel):
    articles_urls = {article['url'] for article in articles}
    documents_urls = {doc['url'] for doc in documents_in_eesel}

    articles_to_delete = [doc for doc in documents_in_eesel if doc['url'] not in articles_urls]
    
    # Get the current time in UTC
    now = datetime.utcnow()
    
    # Set to track unique article URLs
    unique_articles_urls = set()

    # Filter articles that are either not in documents or have been modified in the last 24 hours
    articles_to_index = []
    for article in articles:
        if 'modified_at' in article:
            modified_at = parse_modified_at(article['modified_at'])
            if article['url'] not in documents_urls or modified_at > now - timedelta(days=1):
                if article['url'] not in unique_articles_urls:
                    articles_to_index.append(article)
                    unique_articles_urls.add(article['url'])
        else:
            if article['url'] not in documents_urls and article['url'] not in unique_articles_urls:
                articles_to_index.append(article)
                unique_articles_urls.add(article['url'])

    return articles_to_delete, articles_to_index

def poll_documents(namespace_id, articles, max_poll_duration=30, poll_interval=5):
    start_time = time.time()
    consecutive_no_change_count = 0
    last_documents_to_delete_count = None

    while time.time() - start_time < max_poll_duration:
        print("Polling documents")
        documents_in_eesel = get_eesel_documents(namespace_id)

        remaining_documents_to_delete, _ = diff_articles_and_documents(articles, documents_in_eesel)
        current_documents_to_delete_count = len(remaining_documents_to_delete)
        
        print(f"{current_documents_to_delete_count} documents remaining to delete.")

        if last_documents_to_delete_count is not None and current_documents_to_delete_count == last_documents_to_delete_count:
            consecutive_no_change_count += 1
        else:
            consecutive_no_change_count = 0
        
        last_documents_to_delete_count = current_documents_to_delete_count
        
        if consecutive_no_change_count >= 3:
            break
        
        time.sleep(poll_interval)
    
    return remaining_documents_to_delete

def delete_and_poll_documents(namespace_id, articles, documents_to_delete):
    delete_documents(namespace_id, [doc['url'] for doc in documents_to_delete])
    
    num_docs = len(documents_to_delete)
    print(f"Schedued {num_docs} for deletion")

    for _ in range(5):  # Max 2 minutes with 5 seconds interval (24 * 5 = 120 seconds)
        print("Poll starting")
        remaining_documents_to_delete = poll_documents(namespace_id, articles)

        print("Poll finished")
        if not remaining_documents_to_delete:
            print("Finished deleting")
            break
        
        if len(remaining_documents_to_delete) == len(documents_to_delete):
            print(f"Still documents remaining.")
            delete_documents(namespace_id, [doc['url'] for doc in remaining_documents_to_delete])
        
        documents_to_delete = remaining_documents_to_delete

def chunk_data(data, chunk_size):
    for i in range(0, len(data), chunk_size):
        yield [item for item in data[i:i + chunk_size]]

def post_data_to_api(chunks):
    for chunk in chunks:
        success = False
        retries = 2  # Number of retries

        while not success and retries >= 0:
            try:
                print("inserting chunk of length " + str(len(chunk)))
                success = True
                response = make_post_to_dashboard('https://oracle.eesel.app/ingest', payload=chunk)
                if response.status_code == 200:
                    print("Chunk processed successfully")
                    success = True
                    time.sleep(5)
                else:
                    print("Error processing chunk:", response.json())
                    retries -= 1
                    time.sleep(15)  # Wait for 15 seconds before retrying

            except Exception as e:
                print("An error occurred:", e)
                retries -= 1
                time.sleep(15)

            if retries < 0 and not success:
                print("Failed to process chunk after retries.")

exclude_patterns = [
    "getting-orca-support",
    "whats-new",
    "api-changes-2023-03-06",
    "api-changes-2023-02-02"
]

def lambda_handler(event, context):
    articles = get_all_articles(project_version_id, exclude_patterns)
    documents_in_eesel = get_eesel_documents(eesel_namespace)
    documents_to_delete, articles_to_index = diff_articles_and_documents(articles, documents_in_eesel)

    # # first we delete the documents.
    if len(documents_to_delete) > 0:
        # delete_and_poll_documents(eesel_namespace, articles, documents_to_delete)
        print("deleting docs")
    else:
        print("no documents to delete.")

    if len(articles_to_index) == 0:
        print("no articles to delete")
    else:
        transformed_articles_to_index = []
        for article in articles_to_index:
            if article["is_category"]:
                # fetch the category
                print("Category:")
                whole_category = get_category(article["id"])
                eesel_payload = transform_article_for_eesel(whole_category)
                pprint.pprint(eesel_payload)

            else:
                # fetch the article
                print("Article:")
                whole_article = get_article(article["id"])
                eesel_payload = transform_article_for_eesel(whole_article)
                pprint.pprint(eesel_payload)
            
            transformed_articles_to_index.append(eesel_payload)
        
        # flush batch
        chunks = chunk_data(transformed_articles_to_index, 5)
        post_data_to_api(chunks)

    
    # Return the greeting message with the current time
    response = {
        'message': "success"
    }
    
    return response