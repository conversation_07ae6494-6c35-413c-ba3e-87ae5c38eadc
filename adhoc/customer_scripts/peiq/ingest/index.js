const fs = require('fs');
const path = require('path');

// Directory containing the JSON files
const directoryPath = '../post_processed_batches';

// Pat Test Namespace
const token = "****";

// Function to read JSON file and parse its content
function readJsonFile(filePath) {
    const data = fs.readFileSync(filePath);
    return JSON.parse(data);
}

// Function to simulate sending data (dry run)
async function sendDataDryRun(data) {
    console.log("Sending Data.")
    await fetch("https://oracle.eesel.app/ingest", {
        method: 'POST', // Specifying the method
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`, // Using Bearer token for authentication
        },
        body: JSON.stringify(data), // Stringify the data object to send as the request body
    })
    .then(response => response.json()) // Parsing the JSON response
    .then(data => console.log(data)) // Handling the data from the response
    .catch((error) => console.error('Error:', error)); // Handling errors
}

// Function to process files and prepare data batches
async function processFiles() {
    const files = fs.readdirSync(directoryPath).filter(file => file.startsWith('post_processed_batch_') && file.endsWith('.json'));

    let concatenatedData = [];

    for (const file of files.sort((a, b) => parseInt(a.match(/(\d+)/)[0], 10) - parseInt(b.match(/(\d+)/)[0], 10))) {
        const filePath = path.join(directoryPath, file);
        const fileData = readJsonFile(filePath);
        
        if (concatenatedData.length + fileData.length <= 7) {
            concatenatedData = concatenatedData.concat(fileData);
        } else {
            // Simulate sending data if concatenatedData is not empty
            if (concatenatedData.length) {
                await sendDataDryRun(concatenatedData);
                await new Promise(resolve => setTimeout(resolve, 5000)); // Wait for 5 seconds
            }
            concatenatedData = fileData; // Start a new batch with the current file's data
        }
    }

    // Don't forget to simulate sending the last batch if it hasn't been sent yet
    if (concatenatedData.length) {
        await sendDataDryRun(concatenatedData);
    }
}

processFiles();
