import requests
import re

# Your Confluence setup
base_url = 'https://peiq.atlassian.net/wiki/rest/api'
space_key = 'PPSD'

# Function to get all page IDs in a space
def get_page_ids(space_key):
    page_ids = []
    start_at = 0
    limit = 50  # Adjust based on your needs
    while True:
        url = f"{base_url}/content?spaceKey={space_key}&limit={limit}&start={start_at}"
        response = requests.get(url)
        data = response.json()
        page_ids.extend([page['id'] for page in data['results']])
        start_at += limit
        if 'next' not in data['_links']:
            break
    return page_ids

# Function to extract YouTube links from page content
def extract_youtube_links(page_id):
    url = f"{base_url}/content/{page_id}?expand=body.storage"
    response = requests.get(url)
    page_content = response.json().get('body', {}).get('storage', {}).get('value', '')
    youtube_links = re.findall(r'https?://(?:www\.)?(?:youtube\.com/watch\?v=|youtu\.be/)([\w-]+)', page_content)
    return youtube_links

# Main function to iterate over pages and extract YouTube links
def main():
    page_ids = get_page_ids(space_key)
    print("Found pages: " + str(len(page_ids)))
    all_youtube_links = []
    for page_id in page_ids:
        youtube_links = extract_youtube_links(page_id)
        if youtube_links:
            all_youtube_links.extend(youtube_links)
            print(f"Page ID {page_id} has YouTube links: {youtube_links}")

    print("All YouTube links extracted from the space:")
    print(all_youtube_links)

if __name__ == "__main__":
    main()