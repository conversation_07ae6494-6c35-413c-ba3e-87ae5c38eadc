const fs = require('fs').promises;
const { getVideoDetails } = require('youtube-caption-extractor');

const YT_LINKS_FILE = '../all_yt_links.json';

// Function to process and save a batch of video details to a file
const processAndSaveBatch = async (batch, batchIndex, lang = 'en') => {
  const batchPromises = batch.map(videoID =>
    getVideoDetails({ videoID, lang })
      .then(videoDetails => ({
        url: `https://www.youtube.com/watch?v=${videoID}`,
        video_details: videoDetails,
      }))
      .catch(error => console.error(`Error fetching video details for ${videoID}:`, error))
  );

  // Wait for the batch to resolve
  const results = await Promise.all(batchPromises);
  const filteredResults = results.filter(result => result !== undefined);

  // Write the results of this batch to a file
  const batchFileName = `../batch_${batchIndex}.json`;
  await fs.writeFile(batchFileName, JSON.stringify({ batch: batchIndex, videos: filteredResults }, null, 2));
  console.log(`Batch ${batchIndex} saved successfully.`);
};

const fetchAllVideoDetails = async (lang = 'en') => {
  try {
    // Load video IDs from JSON file
    const videoIDs = JSON.parse(await fs.readFile(YT_LINKS_FILE, 'utf8'));
    const batchSize = 3;

    for (let i = 0; i < videoIDs.length; i += batchSize) {
      const batch = videoIDs.slice(i, i + batchSize);
      const batchIndex = i / batchSize + 1;
      console.log(`Processing batch: ${batchIndex}/${Math.ceil(videoIDs.length / batchSize)}`);

      // Process and save the current batch
      await processAndSaveBatch(batch, batchIndex, lang);
    }

    console.log('All video details processed and saved.');

  } catch (error) {
    console.error('Error processing video details:', error);
  }
};

fetchAllVideoDetails("de");