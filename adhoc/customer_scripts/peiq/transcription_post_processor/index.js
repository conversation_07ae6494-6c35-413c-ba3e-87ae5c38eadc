const fs = require('fs').promises;
const path = require('path');

const BATCH_DIR = '../preprocessed_transcriptions'; // Directory containing the batch files
const POST_PROCESSED_DIR = '../post_processed_batches'; // Directory to save post-processed batches

// Updated processBatchFile function for stricter deduplication
const processBatchFile = async (filePath, videoUrlsSet) => {
  try {
    const data = JSON.parse(await fs.readFile(filePath, 'utf8'));
    // Initialize an empty array for processedVideos
    const processedVideos = [];
    // Use a local set for deduplication within this batch
    const batchLocalSet = new Set();

    for (const video of data.videos) {
      const videoUrl = video.url; // Use video URL for uniqueness check
      // Check both global and local deduplication
      if (!videoUrlsSet.has(videoUrl) && !batchLocalSet.has(videoUrl)) {
        // Add to local and global sets
        videoUrlsSet.add(videoUrl);
        batchLocalSet.add(videoUrl);
        // Push processed video to the array
        processedVideos.push({
          pageTitle: video.video_details.title,
          pageBody: video.video_details.subtitles.map(e => e.text).join(' '),
          source: videoUrl,
        });
      }
    }
    return processedVideos;
  } catch (error) {
    console.error(`Error processing file ${filePath}:`, error);
    return [];
  }
};

const writePostProcessedBatches = async (videos, batchIndex) => {
  if (videos.length > 0) {
    const batchFileName = `post_processed_batch_${batchIndex}.json`;
    await fs.writeFile(path.join(POST_PROCESSED_DIR, batchFileName), JSON.stringify(videos, null, 2));
    console.log(`${batchFileName} saved successfully.`);
  } else {
    console.log(`No videos to save for batch ${batchIndex}, skipping file creation.`);
  }
};

const main = async () => {
  try {
    await fs.mkdir(POST_PROCESSED_DIR, { recursive: true }); // Ensure the output directory exists
    const files = await fs.readdir(BATCH_DIR);
    const batchFiles = files.filter(file => file.startsWith('batch_') && file.endsWith('.json'));
    let batchIndex = 0;
    const videoUrlsSet = new Set(); // Set to track video URLs globally

    for (const file of batchFiles) {
      const filePath = path.join(BATCH_DIR, file);
      const processedVideos = await processBatchFile(filePath, videoUrlsSet);
      await writePostProcessedBatches(processedVideos, batchIndex);
      batchIndex += Math.ceil(processedVideos.length / 10);
    }

    console.log('All batches have been processed and saved.');
  } catch (error) {
    console.error('Error in main process:', error);
  }
};

main();
