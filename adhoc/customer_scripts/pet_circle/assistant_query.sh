#!/bin/bash

set -a
source .env
set +a

SYSTEM_PROMPT=$(cat system_prompt.txt | sed 's/"/\\"/g' | tr '\n' ' ')

curl -v "https://oracle.eesel.app/helpdesk/v4/assistant/webhook" \
-X POST \
-H "Content-Type: application/json" \
-H "Authorization: Bearer $BEARER_TOKEN" \
-d '{
    "system_prompt": "'"$SYSTEM_PROMPT"'",
    "history": [
        ["assistant", "Hello, can you tell me your pets name and grievance?"]
    ],
    "tools": [
        "retrieve_relevant_documents",
        "generic_operation_search_all",
        "retrieve_sibling_documents"
    ],
    "next_message": "Hello, my pets name is poochums and i need vaccine advice"
}'
