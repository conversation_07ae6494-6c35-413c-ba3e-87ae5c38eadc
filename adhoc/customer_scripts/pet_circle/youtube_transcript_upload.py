import json
import os
import logging
import argparse
import requests
from typing import List, Dict

logger = logging.getLogger("app.sources.uploaders.eesel_uploader")


class EeselUploader:
    def __init__(self, bearer_token: str):
        """
        Initialize the Eesel uploader

        Args:
            bearer_token (str): Bearer token for authentication
        """
        self.api_url = "https://oracle.eesel.app/ingest-async"
        self.headers = {
            "Authorization": f"Bearer {bearer_token}",
            "Content-Type": "application/json"
        }

    def read_sources_from_json(self, json_file_path: str, limit: int = None) -> List[Dict]:
        """
        Read and transform sources from JSON file

        Args:
            json_file_path (str): Path to the JSON file
            limit (int): Maximum number of sources to process


        Returns:
            List[Dict]: Transformed sources ready for upload
        """
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                sources = json.load(f)
                print(sources)
            # Apply limit if specified
            if limit:
                sources = sources[:limit]

            # Transform to expected format
            transformed_sources = [
                {
                    "source": source["url"],
                    "pageTitle": source["title"],
                    "pageBody": source["raw_body"]
                }
                for source in sources
            ]
            logger.info("Source added")
            return transformed_sources

        except Exception as e:
            logger.error(f"Error reading sources: {str(e)}")
            return []

    def upload_sources(self, sources: List[Dict]) -> bool:
        """
        Upload sources to Eesel backend

        Args:
            sources (List[Dict]): List of sources to upload

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            response = requests.post(
                self.api_url,
                headers=self.headers,
                json=sources
            )

            if response.status_code == 200:
                logger.info(f"Successfully uploaded {len(sources)} sources")
                return True
            else:
                logger.error(f"Upload failed with status code {response.status_code}")
                logger.error(f"Response: {response.text}")
                return False

        except Exception as e:
            logger.error(f"Error uploading sources: {str(e)}")
            return False


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Upload sources to Eesel backend')
    parser.add_argument(
        '--token',
        '-t',
        type=str,
        required=True,
        help='Bearer token for authentication'
    )
    parser.add_argument(
        '--limit',
        '-l',
        type=int,
        help='Limit number of documents to process (default: 2)',
        default=2
    )
    return parser.parse_args()


if __name__ == "__main__":
    # Parse arguments
    args = parse_arguments()

    current_dir = os.path.dirname(os.path.abspath(__file__))
    temp_dir = os.path.join(os.path.dirname(current_dir), "temp_csv")
    json_path = os.path.join(temp_dir, 'sources.json')

    # Initialize uploader
    uploader = EeselUploader(args.token)

    # Read and transform sources
    sources = uploader.read_sources_from_json(json_path)

    if not sources:
        logger.error("No sources found to upload")
        exit(1)

    logger.info(f"Found {len(sources)} sources to upload")

    # Upload sources
    if uploader.upload_sources(sources):
        logger.info("Upload completed successfully")
    else:
        logger.error("Upload failed")

