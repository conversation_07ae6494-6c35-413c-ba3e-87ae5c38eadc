from dataclasses import dataclass
from typing import List, Optional
import csv
from langchain_openai import ChatOpenAI
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import PromptTemplate
from langchain.output_parsers import ResponseSchema, StructuredOutputParser
from langchain_core.runnables import RunnableParallel, RunnablePassthrough
from langchain.globals import set_debug

def flatten(*vars):
    flat = {}
    for var in vars:
        keys = [k for k in var]
        for key in keys:
            if isinstance(var[key], dict):
                flat.update(var[key])
            else:
                flat[key] = var[key]
    return flat

# Define the data structure for the output
@dataclass
class MacroEvaluationResult:
    macro: str
    follow_on_action: bool
    follow_on_rationale: str
    good_candidate: bool
    candidate_rationale: str
    improved_description: str
    description_rationale: str

# Read the macros from the file and split into paragraphs
def read_macros(file_path: str) -> List[str]:
    with open(file_path, 'r') as file:
        content = file.read()
    macros = content.split('\n\n')
    return macros

# Setup LLM and prompts
llm = ChatOpenAI(model="gpt-4o")

combined_schemas = [
    ResponseSchema(
        name="follow_on_action",
        description="A boolean indicating whether the macro is intended to follow on from some action a human support agent took, like resetting a password, processing a refund, confirming some process has occurred, etc. Something that a human decision would need to have been made for",
        type="boolean"
    ),
    ResponseSchema(
        name="follow_on_rationale",
        description="A point-by-point explanation of your rationale for `follow_on_action`."
    ),
    ResponseSchema(
        name="good_candidate",
        type="boolean",
        description="A boolean indicating whether this macro would be a good candidate for an AI system to know about, given it cannot take specific actions for the customer."
    ),
    ResponseSchema(
        name="candidate_rationale",
        description="A point-by-point explanation of your rationale for `good_candidate`."
    ),
    ResponseSchema(
        name="improved_description",
        description="An improved description/title about when that macro should most likely be used."
    ),
    ResponseSchema(
        name="description_rationale",
        description="A point-by-point explanation of your rationale for `improved_description`."
    ),
]

combined_parser = StructuredOutputParser.from_response_schemas(combined_schemas)

combined_prompt = PromptTemplate(
    template="""
        Assess whether this e-commerce support macro, used by human support agents, would be a good candidate for an AI system to know about.

        A good candidate MUST meets the following criteria:
            - Is a response to an inquiry from human help seekers, preferably informational or answering questions where the answer would generally be found in a knowledge base
            - MUST NOT involve an action, such as resetting passwords, sending invoices, etc.
            - Did not involve a decision by a human involved in the support case, such as evaluating eligibility for a refund.
            - MUST NOT BE confirmation or acknowledgement email, such as a cancellation, refund, return, etc.

        Good candidates MUST NOT BE:
            - Macros that are proactive or sent cold to a customer or otherwise, e.g. a cease and desist.
            - Confirmation that some action has occurred, such as a refund being issued, an email being sent.
            - Macros that required a decision from a human support agent, such as deciding on refund eligibility.
        
        Macro = {macro}

        {format_instructions}
    """,
    input_variables=["macro"],
    partial_variables={"format_instructions": combined_parser.get_format_instructions()}
)

chain = (
    combined_prompt | llm | combined_parser
)

import pprint

def evaluate_macros(macros: List[str]) -> List[MacroEvaluationResult]:
    results = []
    max = 100
    i = 0
    for macro in macros:
        try:
            if i >= 100:
                res = chain.invoke({"macro": macro})
                evaluation_result = MacroEvaluationResult(
                    macro=macro,
                    follow_on_action=res["follow_on_action"],
                    follow_on_rationale=res["follow_on_rationale"],
                    good_candidate=res["good_candidate"],
                    candidate_rationale=res["candidate_rationale"],
                    improved_description=res["improved_description"],
                    description_rationale=res["description_rationale"]
                )
                results.append(evaluation_result)

                print(macro)
                print()
                pprint.pprint(res)
                print()
                print()
                print()

            i += 1
            # if i == max:
            #     break

        except Exception as e:
            print(f"Exception encountered: {e}, skipping")
    return results

def write_results_to_csv(results: List[MacroEvaluationResult], output_file: str):
    with open(output_file, mode='w', newline='') as file:
        writer = csv.writer(file)
        writer.writerow([
            "Macro", 
            "Follow On Action", 
            "Follow On Rationale", 
            "Good Candidate", 
            "Candidate Rationale", 
            "Improved Description", 
            "Description Rationale"
        ])
        for result in results:
            writer.writerow([
                result.macro, 
                result.follow_on_action, 
                result.follow_on_rationale, 
                result.good_candidate, 
                result.candidate_rationale, 
                result.improved_description, 
                result.description_rationale
            ])

# Main execution
if __name__ == "__main__":
    macros = read_macros("./data/SixZero Macros.txt")
    evaluation_results = evaluate_macros(macros)
    write_results_to_csv(evaluation_results, "./output/macro_evaluation_results.csv")
