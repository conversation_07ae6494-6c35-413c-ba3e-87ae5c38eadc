import requests
import logging
import json
import csv

# Replace with your Front API token and the base URL
API_TOKEN = "xxxx"
BASE_URL = 'https://six-zero.api.frontapp.com'

headers = {
    'Authorization': f'Bearer {API_TOKEN}'
}

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

def get_paginated_results(url):
    results = []
    while url:
        logging.debug(f"Requesting data from: {url}")
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        data = response.json()
        results.extend(data['_results'])
        url = data['_pagination'].get('next')
        logging.debug(f"Pagination URL: {url}")
    return results

def get_child_folders(folder_id):
    url = f"{BASE_URL}/message_template_folders/{folder_id}/message_template_folders"
    return get_paginated_results(url)

def get_templates_in_folder(folder_id):
    url = f"{BASE_URL}/message_template_folders/{folder_id}/message_templates"
    return get_paginated_results(url)

def retrieve_templates_recursively(folder_id, path=''):
    logging.info(f"Processing folder: {folder_id} with path: '{path}'")
    flat_list = []
    
    # Get templates directly in the current folder
    templates = get_templates_in_folder(folder_id)
    for template in templates:
        template_name = f"{path} > {template['name']}" if path else template['name']
        flat_list.append({
            "folder_path": path,
            "template_name": template['name'],
            "template_body": template.get('body')
        })
        logging.info(f"Added template: {template_name}")
    
    # Get child folders and recursively retrieve templates
    child_folders = get_child_folders(folder_id)
    for child_folder in child_folders:
        child_path = f"{path} > {child_folder['name']}" if path else child_folder['name']
        flat_list.extend(retrieve_templates_recursively(child_folder['id'], child_path))
    
    return flat_list

def export_to_json(data, filename='templates.json'):
    logging.info(f"Exporting data to JSON file: {filename}")
    with open(filename, 'w') as json_file:
        json.dump(data, json_file, indent=4)

def export_to_csv(data, filename='templates.csv'):
    logging.info(f"Exporting data to CSV file: {filename}")
    with open(filename, 'w', newline='') as csv_file:
        writer = csv.DictWriter(csv_file, fieldnames=["folder_path", "template_name", "template_body"])
        writer.writeheader()
        for item in data:
            writer.writerow(item)

def main():
    # Replace with your starting folder ID
    root_folder_id = 'rsf_kfh4'
    
    logging.info(f"Starting retrieval from root folder: {root_folder_id}")
    templates = retrieve_templates_recursively(root_folder_id)
    
    logging.info(f"Total templates retrieved: {len(templates)}")
    
    # Export data to JSON and CSV files
    export_to_json(templates)
    export_to_csv(templates)

if __name__ == "__main__":
    main()