import csv

# Load your CSV file
input_file = './data/macros_cleaned.csv'
output_file = 'reformatted_output.txt'

with open(input_file, 'r') as csvfile:
    reader = csv.DictReader(csvfile)
    
    with open(output_file, 'w') as txtfile:
        for row in reader:
            macro = "".join(row['Macro'].lstrip().split('\n')[1:])
            txtfile.write(f"{row['Improved Description']}:\n{macro}\n\n")

print("Reformatted data saved to reformatted_output.txt")