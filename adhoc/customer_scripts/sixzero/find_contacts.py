import requests
import argparse
import json
import logging
import boto3  # Add this import
import concurrent.futures

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Define the base URL and your API token
BASE_URL = "https://api2.frontapp.com"
API_TOKEN = "xxx"

# Initialize AWS Location Service client with specific profile
session = boto3.Session(profile_name='eeselAiDeployer')
location_client = session.client('location', region_name='us-east-1')

def get_contact_groups():
    url = f"{BASE_URL}/contact_groups"
    headers = {
        "Authorization": f"Bearer {API_TOKEN}"
    }
    response = requests.get(url, headers=headers)
    if response.status_code == 200:
        return response.json()
    else:
        print(f"Error: {response.status_code}")
        return None

def get_contacts_in_group(group_id):
    url = f"{BASE_URL}/contact_groups/{group_id}/contacts"
    headers = {
        "Authorization": f"Bearer {API_TOKEN}"
    }
    all_contacts = []
    while url:
        logging.info(f"Fetching contacts from URL: {url}")
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            data = response.json()
            all_contacts.extend(data["_results"])
            logging.info(f"Fetched {len(data['_results'])} contacts")
            next_page_token = data["_pagination"].get("next")
            if next_page_token:
                url = next_page_token  # Use the next page URL
                logging.info(f"Next page URL: {url}")
            else:
                url = None
                logging.info("No more pages to fetch")
        else:
            logging.error(f"Error: {response.status_code}")
            return None
    return all_contacts

def get_lat_long_from_address(address):
    try:
        response = location_client.search_place_index_for_text(
            IndexName='EeselAiPlaceIndex',  # Replace with your AWS Location Service Place Index name
            Text=address,
            MaxResults=1
        )
        if response['Results']:
            position = response['Results'][0]['Place']['Geometry']['Point']
            return position[1], position[0]  # Latitude, Longitude
    except Exception as e:
        logging.error(f"Error fetching lat/long for address {address}: {e}")
    return None, None

def fetch_contact_info(contact):
    address = f"{contact['custom_fields'].get('Address', 'N/A')}, {contact['custom_fields'].get('city', 'N/A')}, {contact['custom_fields'].get('CState', 'N/A')}, {contact['custom_fields'].get('Country', 'N/A')}"
    lat, long = get_lat_long_from_address(address)

    print(contact["custom_fields"])

    contact_info = {
        "id": contact["id"],
        "name": contact["name"],
        "emails": [handle["handle"] for handle in contact["handles"] if handle["source"] == "email"],
        "address": {
            "street": contact["custom_fields"].get("Address", "N/A"),
            "city": contact["custom_fields"].get("city", "N/A"),
            "state": contact["custom_fields"].get("CState", "N/A"),
            "country": contact["custom_fields"].get("Country", "N/A"),
            "latitude": lat,
            "longitude": long
        },
        'demos': contact["custom_fields"].get("Demos", "N/A"),
        "role": contact["custom_fields"].get("Role", "N/A")
    }
    return contact_info

def get_contacts_in_groups(group_ids):
    all_contacts = []
    # Loop through all provided group IDs
    for group_id in group_ids:
        logging.info(f"Fetching contacts for group ID: {group_id}")
        contacts = get_contacts_in_group(group_id)
        if contacts:
            # pprint.pprint(contacts)
            # Exclude contacts with role "past-ambassador"
            active_contacts = [contact for contact in contacts if contact['custom_fields'].get('Status') == 'active' and contact['custom_fields'].get('Role') != 'past-ambassador']
            with concurrent.futures.ThreadPoolExecutor(max_workers=15) as executor:
                results = list(executor.map(fetch_contact_info, active_contacts))
                all_contacts.extend(results)
    return all_contacts

def get_filtered_groups():
    groups = get_contact_groups()
    if not groups:
        return []

    filtered_groups = []
    for group in groups["_results"]:
        name = group["name"]
        if name.startswith("Wholesale"):
            group_type = "Wholesale"
        elif name.startswith("Ambassador"):
            group_type = "Ambassador"
        elif name.startswith("Coach"):
            group_type = "Coach"
        else:
            continue
        filtered_groups.append({
            "type": group_type,
            "name": name,
            "id": group["id"]
        })
    return filtered_groups

def main():
    parser = argparse.ArgumentParser(description="Front CLI Utility")
    parser.add_argument("--list-groups", action="store_true", help="List all contact groups")
    parser.add_argument("--list-contacts", type=str, nargs='+', help="List all contacts in contact groups by group IDs")

    args = parser.parse_args()

    if args.list_groups:
        filtered_groups = get_filtered_groups()
        for group in filtered_groups:
            print(f"{group['type']} - {group['name']}: {group['id']}")
    elif args.list_contacts:
        # Pass the list of group IDs directly
        contacts = get_contacts_in_groups(args.list_contacts)
        if contacts:
            print(json.dumps(contacts, indent=4))  # Pretty print the contacts
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
