import requests

FRONT_API_URL = "https://api2.frontapp.com"
API_TOKEN = "xxxx"

def get_users():
    url = f"{FRONT_API_URL}/teammates"
    headers = {
        "Authorization": f"Bearer {API_TOKEN}"
    }
    response = requests.get(url, headers=headers)
    response.raise_for_status()
    return response.json()

def get_inboxes():
    url = f"{FRONT_API_URL}/inboxes"
    headers = {
        "Authorization": f"Bearer {API_TOKEN}"
    }
    response = requests.get(url, headers=headers)
    response.raise_for_status()
    return response.json()

def list_users_and_inboxes():
    users = get_users()
    inboxes = get_inboxes()

    print("Users:")
    for user in users['_results']:
        if user['email'] == '<EMAIL>':
            print(f"Full User <NAME_EMAIL>: {user}")
        else:
            print(f"Email: {user['email']}, User ID: {user['id']}")

    print("\nShared Inboxes:")
    for inbox in inboxes['_results']:
        # if 'type' in inbox and inbox['type'] == 'shared':
        print(f"Inbox Name: {inbox['name']}, Inbox ID: {inbox['id']}")

if __name__ == "__main__":
    list_users_and_inboxes()
