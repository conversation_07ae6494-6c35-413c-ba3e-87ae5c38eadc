import sys
from typing import List, Dict, Any
import json
import os
import boto3
from geopy.distance import geodesic


# Initialize AWS Location Service client
session = boto3.Session(profile_name='eeselAiDeployer')
location_client = session.client('location', region_name='us-east-1')

def get_coordinates(address: str) -> Dict[str, float]:
    response = location_client.search_place_index_for_text(
        IndexName='EeselAiPlaceIndex',  # Replace with your Place Index name
        Text=address
    )
    coordinates = response['Results'][0]['Place']['Geometry']['Point']
    return {'latitude': coordinates[1], 'longitude': coordinates[0]}

def compute_distance(coord1: Dict[str, float], coord2: Dict[str, float]) -> float:
    return geodesic((coord1['latitude'], coord1['longitude']), (coord2['latitude'], coord2['longitude'])).miles

def find_relevant_contacts(data, target_address: str):
    relevant_contacts = []
    try:
        target_coordinates = get_coordinates(target_address)
        for contact in data:
            address = contact.get('address', {})
            latitude = address.get('latitude')
            longitude = address.get('longitude')
            if latitude is not None and longitude is not None:
                contact['distance'] = compute_distance(target_coordinates, {'latitude': latitude, 'longitude': longitude})
                relevant_contacts.append(contact)
    except KeyError as e:
        print(f"Error finding relevant contacts: {e}")
    
    # Sort contacts by distance
    relevant_contacts = sorted(relevant_contacts, key=lambda x: x['distance'])
    
    # Return top 2 closest contacts
    return relevant_contacts[:2]

# Example usage
if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python predict_ambassador.py <target_address>")
        sys.exit(1)
    
    target_address = sys.argv[1]
    
    with open('all_ambassadors_2.json', 'r') as file:
        data = json.load(file)
    
    relevant_contacts = find_relevant_contacts(data, target_address)
    print("Top 2 closest contacts:")
    print(json.dumps(relevant_contacts, indent=4))
    if not relevant_contacts:
        print("Explanation: Failed to find relevant contacts.")

