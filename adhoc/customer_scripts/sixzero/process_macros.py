import json
import html2text

# Load the JSON data
with open('templates.json', 'r') as file:
    data = json.load(file)

# Initialize the HTML to Markdown converter
converter = html2text.HTML2Text()
converter.ignore_links = True  # Ignore converting links for simplicity

# Process the canned answers
result = []
for answer in data:
    if 'draft' not in answer.get('template_name', '') and 'draft' not in answer.get('body', ''):
        folder_path = answer.get('folder_path')
        template_name = folder_path + " > " + answer.get('template_name', '')
        body_html = answer.get('template_body', '')
        body_markdown = converter.handle(body_html).strip().replace('\n', ' ')
        result.append(f"{template_name}\n{body_markdown}\n")

# Print the result
print("\n".join(result))
