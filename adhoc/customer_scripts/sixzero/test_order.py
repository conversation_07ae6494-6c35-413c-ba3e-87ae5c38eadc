import os
import requests
import json

SHOP_NAME = 'six-zero-7668'

def get_order_by_number(order_number: str) -> str:
    """Retrieves the details of a specific order by its order number using Shopify API.
    
    Args:
        order_number: The number of the order.
    Returns:
        The details of the specified order in JSON format.
    """
    base_url = f"https://{SHOP_NAME}.myshopify.com/admin/api/2021-04"
    api_key = os.getenv("SHOPIFY_KEY")
    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": api_key
    }
    
    orders_url = f"{base_url}/orders.json?name={order_number}"
    response = requests.get(orders_url, headers=headers)
    response.raise_for_status()
    
    orders = response.json().get('orders', [])
    
    if not orders:
        raise ValueError("No order found with the provided order number.")
    
    return json.dumps(orders[0], indent=4)
def get_recent_order_ids_by_email(email: str, limit: int = 5) -> list:
    """Retrieves a list of recent order IDs for a given customer email address.
    
    Args:
        email: The email address of the customer.
        limit: The number of recent orders to retrieve.
    
    Returns:
        A list of recent order IDs.
    """
    base_url = f"https://{SHOP_NAME}.myshopify.com/admin/api/2021-04"
    api_key = os.getenv("SHOPIFY_KEY")
    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": api_key
    }
    
    # Step 1: Get the customer ID associated with the email address
    customers_url = f"{base_url}/customers/search.json?query=email:{email}"
    customer_response = requests.get(customers_url, headers=headers)
    customer_response.raise_for_status()
    
    customers = customer_response.json().get('customers', [])
    
    if not customers:
        raise ValueError("No customer found with the provided email address.")
    
    customer_id = customers[0]['id']
    
    # Step 2: Get the orders for the customer
    orders_url = f"{base_url}/orders.json?customer_id={customer_id}&status=any&limit={limit}&order=created_at desc"
    orders_response = requests.get(orders_url, headers=headers)
    orders_response.raise_for_status()
    orders = orders_response.json().get('orders', [])
    
    if not orders:
        raise ValueError("No orders found.")
    
    for order in orders:
        print(json.dumps(order, indent=4))
    return [order['id'] for order in orders]

if __name__ == "__main__":
    import sys
    if len(sys.argv) == 2:
        order_number = sys.argv[1]
        try:
            order_details = get_order_by_number(order_number)
            print(order_details)
        except Exception as e:
            print(f"Error: {e}")
    elif len(sys.argv) == 3 and sys.argv[1] == "--email":
        email = sys.argv[2]
        try:
            order_ids = get_recent_order_ids_by_email(email)
            print("Recent Order IDs for email:", order_ids)
        except Exception as e:
            print(f"Error: {e}")
    else:
        print("Usage: python test_order.py <order_number> or python test_order.py --email <email_address>")
