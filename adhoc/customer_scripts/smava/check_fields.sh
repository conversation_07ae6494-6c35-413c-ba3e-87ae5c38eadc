#!/bin/bash
# Authorization token
auth_token="xxxxx"


# Array of ticket IDs
ticket_ids=(
    4379927
    4379928
    4379929
    4379931
    4379930
    4379932
    4379933
    4379934
    4379936
    4379935
    4379938
    4379940
)

# Authorization token

# Loop through each ticket ID
for ticket_id in "${ticket_ids[@]}"; do
    echo "Processing ticket ID: $ticket_id"
    
    # Make the cURL request and extract the field value
    field_value=$(curl -s -X GET "https://smava.zendesk.com/api/v2/tickets/${ticket_id}.json" \
        -H "Authorization: Basic $auth_token" \
        -H "Content-Type: application/json" | \
        jq '.ticket.custom_fields[] | select(.id == 15757679068061) | .value')
    
    # Check if the field value is null
    if [ "$field_value" == "null" ]; then
        echo "Field value for ticket $ticket_id is null"
    else
        echo "Field value for ticket $ticket_id: $field_value"
    fi
done
