import requests
import csv
import os
import re  # Add this import for regular expressions

zendesk_subdomain = "smava"

# API endpoint
url = f"https://{zendesk_subdomain}.zendesk.com/api/v2/macros.json"

# Group IDs to filter by
group_ids = {
    4721156610589,
    360000413457,
    360007012397,
    4404584120605,
    4402017514013,
    360009937317
}

excluded_titles = {
    "CS::Mail::Datenlöschung aufgrund von Datenmissbrauch",
    "CS::Mail::Weiterleitung EKS smava",
    "CS::Mail::Weiterleitung EKS FC"
}

def fetch_macros(url):
    macros = []
    while url:
        response = requests.get(url, headers={
            "Authorization": "Basic " + os.getenv("ZENDESK_AUTH")
        })
        response.raise_for_status()
        data = response.json()

        for macro in data['macros']:
            if macro['active'] and macro['title'].startswith("CS::Mail"):
                if macro['title'] in excluded_titles:
                    continue

                restriction = macro.get('restriction')
                if restriction and restriction['type'] == 'Group':
                    print(restriction)
                    if any(group_id in group_ids for group_id in restriction.get('ids', [])):
                        for action in macro['actions']:
                            if action['field'] == 'comment_value_html' and action['value'].strip():
                                macros.append(macro)
                                # print(f"Got macro: {c}")
                                break

        url = data.get('next_page')

    return macros

# Fetch and filter macros
filtered_macros = fetch_macros(url)

# Deduplicate macros based on title and raw_body
unique_macros = {}
for macro in filtered_macros:
    key = (macro['title'], macro.get("raw_body", ""))
    if key not in unique_macros:
        unique_macros[key] = macro

# Convert back to a list
filtered_macros = list(unique_macros.values())

# Group macros based on title content
grouped_macros = {
    "smava": [],
    "FC": [],
    "common": []
}

for macro in filtered_macros:
    if re.search(r'\bsmava\b', macro['title'], re.IGNORECASE):  # Updated to use regex for case-insensitive whole word check
        grouped_macros["smava"].append(macro)
    elif re.search(r'\bFC\b', macro['title'], re.IGNORECASE):  # Updated to use regex for case-insensitive whole word check
        grouped_macros["FC"].append(macro)
    else:
        grouped_macros["common"].append(macro)

# Print or process the grouped macros
for group, macros in grouped_macros.items():
    print(f"Group: {group}")
    for macro in macros:
        print(f"  Title: {macro['title']}")
        for action in macro['actions']:
            if action['field'] == "comment_value_html":
                print(action['value'])
                macro["raw_body"] = action['value']
        print("\n")

# Create separate CSV files for each group
for group in grouped_macros:
    csv_file = f"{group}_filtered_macros.csv"  # Create a filename based on the group
    with open(csv_file, mode='w', newline='', encoding='utf-8') as file:
        writer = csv.DictWriter(file, fieldnames=["title", "raw_body"])
        writer.writeheader()

        for macro in grouped_macros[group]:
            new_macro = {
                "title": macro["title"],
                "raw_body": macro["raw_body"]
            }
            writer.writerow(new_macro)

# You can replace the print statements with whatever processing you need to do.
