import pandas as pd
import matplotlib.pyplot as plt  # Add this import at the top

import logging  # Import the logging module

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logging.info("Loading SKL")
from sklearn.metrics import confusion_matrix

# Load the data from CSV and filter N/A values
logging.info("Loading data from Smava_Gap_Analysis.csv")
df = pd.read_csv('~/Downloads/Smava_Gap_Analysis.csv', index_col=0)

# Thorough N/A filtering for both Deflection and Gap In Training
logging.info("Filtering out N/A values")

# Filter Deflection
df = df[~df['Deflection'].isna()]  # Remove NaN values
df['Deflection'] = df['Deflection'].astype(str)  # Convert to string
df = df[df['Deflection'] != 'N/A']  # Remove 'N/A' string values
df = df[df['Deflection'].str.strip() != '']  # Remove empty strings

# Filter Gap In Training
df = df[~df['Gap In Training'].isna()]  # Remove NaN values
df['Gap In Training'] = df['Gap In Training'].astype(str)  # Convert to string
df = df[df['Gap In Training'] != 'N/A']  # Remove 'N/A' string values
df = df[df['Gap In Training'].str.strip() != '']  # Remove empty strings

logging.info(f"Remaining rows after N/A filtering: {len(df)}")

# Add debug logging to see unique values
logging.info(f"Unique values in Deflection column: {df['Deflection'].unique()}")
logging.info(f"Unique values in Gap In Training column: {df['Gap In Training'].unique()}")

# Add week number calculation (assuming there's a timestamp column)
logging.info("Adding week number calculation")
df['Week'] = pd.to_datetime(df['Created At']).dt.isocalendar().week

logging.info("Inspecting the first few rows of the DataFrame")
print(df.head())

# Create a binary variable for 'Smava Human Rating'
logging.info("Creating binary variable for 'Smava Human Rating'")
def is_acceptable_rating(rating):
    if isinstance(rating, str):
        if rating.startswith('1_') or rating.startswith('2_'):
            return 1
        else:
            return 0
    else:
        return 0

df['Human_Acceptable'] = df['Smava Human Rating'].apply(is_acceptable_rating)

df['Deflection'] = df['Deflection'].astype(str)
df['Gap In Training'] = df['Gap In Training'].astype(str)

# Convert 'Deflection' and 'Gap in Training' to binary variables
logging.info("Converting 'Deflection' and 'Gap in Training' to binary variables")
df['Deflection_Binary'] = df['Deflection'].map({'True': 1, 'False': 0})
df['Gap_Binary'] = df['Gap In Training'].map({'True': 1, 'False': 0})


# Fill any missing values with 0
logging.info("Filling missing values with 0")
df['Deflection_Binary'] = df['Deflection_Binary'].fillna(0).astype(int)
df['Gap_Binary'] = df['Gap_Binary'].fillna(0).astype(int)

# Function to compute confusion matrix and metrics
def compute_metrics(group):
    logging.info("Computing metrics for a group of size: %d", len(group))
    human = group['Human_Acceptable']
    deflection = group['Deflection_Binary']
    gap = group['Gap_Binary']
    
    # Calculate correlations
    corr_deflection_human = human.corr(deflection)
    corr_gap_human = human.corr(gap)
    corr_gap_deflection = gap.corr(deflection)
    
    # Calculate percentages of human acceptable and not acceptable
    total_samples = len(group)
    acceptable_count = human.sum()
    not_acceptable_count = total_samples - acceptable_count
    acceptable_percentage = (acceptable_count / total_samples) * 100 if total_samples > 0 else 0
    not_acceptable_percentage = (not_acceptable_count / total_samples) * 100 if total_samples > 0 else 0

    # Confusion matrix for Deflection
    cm_deflection = confusion_matrix(human, deflection, labels=[1, 0])
    tp_d, fn_d, fp_d, tn_d = cm_deflection.ravel()
    false_positive_rate_d = fp_d / (fp_d + tn_d) if (fp_d + tn_d) > 0 else 0
    false_negative_rate_d = fn_d / (fn_d + tp_d) if (fn_d + tp_d) > 0 else 0
    
    # Confusion matrix for Gap in Training (inverse relationship)
    cm_gap = confusion_matrix(human, gap, labels=[1, 0])
    tp_g, fn_g, fp_g, tn_g = cm_gap.ravel()
    false_positive_rate_g = fp_g / (fp_g + tn_g) if (fp_g + tn_g) > 0 else 0
    false_negative_rate_g = fn_g / (fn_g + tp_g) if (fn_g + tp_g) > 0 else 0
    
    # Add accuracy calculation
    accuracy = (tp_d + tn_d) / total_samples if total_samples > 0 else 0
    
    return pd.Series({
        'Total Samples': total_samples,
        'Acceptable %': acceptable_percentage,
        'Not Acceptable %': not_acceptable_percentage,
        'Correlation Deflection-Human': corr_deflection_human,
        'Correlation Gap-Human': corr_gap_human,
        'Correlation Gap-Deflection': corr_gap_deflection,
        'Deflection True Positives': tp_d,
        'Deflection True Negatives': tn_d,
        'Deflection False Positives': fp_d,
        'Deflection False Negatives': fn_d,
        'Deflection False Positive %': false_positive_rate_d * 100,
        'Deflection False Negative %': false_negative_rate_d * 100,
        'Gap True Positives': tp_g,
        'Gap True Negatives': tn_g,
        'Gap False Positives': fp_g,
        'Gap False Negatives': fn_g,
        'Gap False Positive %': false_positive_rate_g * 100,
        'Gap False Negative %': false_negative_rate_g * 100,
        'Accuracy %': accuracy * 100,
    })

# Compute overall metrics
logging.info("Computing overall metrics")
overall_metrics = compute_metrics(df)

# Compute metrics by week
logging.info("Computing metrics by week")
metrics_by_week = df.groupby('Week').apply(compute_metrics)

# Compute metrics broken down by 'Smava Field 2'
logging.info("Computing metrics by 'Smava Field 2'")
metrics_by_category = df.groupby('Smava Field 2').apply(compute_metrics)

# Output the results
logging.info("Outputting overall metrics")
print("Overall Metrics:")
print(overall_metrics)

logging.info("Outputting metrics by week")
print("\nMetrics by Week:")
print(metrics_by_week)

logging.info("Outputting metrics by Smava Field 2")
print("\nMetrics by Smava Field 2:")
print(metrics_by_category)

# Add additional correlation analysis for the entire dataset
logging.info("Computing correlation matrix for all variables")
correlation_matrix = df[['Human_Acceptable', 'Deflection_Binary', 'Gap_Binary']].corr()
print("\nOverall Correlation Matrix:")
print(correlation_matrix)

# Add visualization of accuracy over time
logging.info("Creating accuracy over time visualization")
plt.figure(figsize=(12, 6))
metrics_by_week['Accuracy %'].plot(marker='o')
plt.title('Accuracy Over Time (by Week)')
plt.xlabel('Week Number')
plt.ylabel('Accuracy (%)')
plt.grid(True)
plt.tight_layout()
plt.savefig('accuracy_over_time.png')
plt.close()

# Print accuracy statistics
print("\nAccuracy Statistics:")
print(f"Overall Accuracy: {overall_metrics['Accuracy %']:.2f}%")
print("\nWeekly Accuracy:")
print(metrics_by_week['Accuracy %'])
