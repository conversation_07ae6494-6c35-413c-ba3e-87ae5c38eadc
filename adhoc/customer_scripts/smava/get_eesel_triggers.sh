#!/bin/bash

# Check if ZENDESK_AUTH is set
if [ -z "$ZENDESK_AUTH" ]; then
    echo "Error: ZENDESK_AUTH environment variable is not set"
    echo "Please set it with your Base64 encoded 'email:token' credentials"
    exit 1
fi

ZENDESK_DOMAIN="smava.zendesk.com"

# Get triggers containing "eesel"
echo "Fetching triggers containing 'eesel'..."
response=$(curl -s -X GET "https://$ZENDESK_DOMAIN/api/v2/triggers/search?query=eesel" \
    -H "Authorization: Basic $ZENDESK_AUTH" \
    -H "Content-Type: application/json")

echo "Debug - Trigger Response:"
echo "$response" | jq '.'

echo "Filtered Triggers:"
echo "$response" | jq -r '.results[]? | {id: .id, title: .title}'

# Get specific webhook configuration
echo -e "\nFetching specific webhook configuration..."
response=$(curl -s -X GET "https://$ZENDESK_DOMAIN/api/v2/webhooks/01J1RE3BWCCV4BEYJETTN6F50Z" \
    -H "Authorization: Basic $ZENDESK_AUTH" \
    -H "Content-Type: application/json")

echo "Webhook Configuration:"
echo "$response" | jq '.'
