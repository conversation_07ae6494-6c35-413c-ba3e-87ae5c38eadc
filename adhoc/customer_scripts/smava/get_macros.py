import os
import requests
import json
import sys
from markdownify import markdownify as md, MarkdownConverter
import re

class CustomMarkdownConverter(MarkdownConverter):
    def convert_img(self, el, text, convert_as_inline):
        return "[IMAGE]"
    
md_converter = CustomMarkdownConverter(strip=[
    "script",
    "style",
    "link",
    "meta",
    "noscript",
    "head",
    "nav",
    "footer",
])

def convert_to_markdown(text):
    if text is None:
        return ""
    # Convert HTML to markdown
    markdown_text = md_converter.convert(text)
    # Replace any line containing only whitespace with an empty line
    markdown_text = re.sub(r'^\s+$', '', markdown_text, flags=re.MULTILINE)
    # Replace 3 or more consecutive newlines with 2 newlines
    markdown_text = re.sub(r'\n{3,}', '\n\n', markdown_text)
    # Remove zero-width spaces and other invisible characters
    markdown_text = re.sub(r'[\u200B\u200C\u200D\u2060\uFEFF]', '', markdown_text)
    # Trim trailing whitespace and ensure exactly one newline at the end
    markdown_text = markdown_text.rstrip() + '\n'
    return markdown_text

def write_markdown_files(no_brand_macros, brand_macros):
    try:
        # Prepare base content (no brand macros)
        base_content = []
        for macro in no_brand_macros:
            title = macro['title']
            # Check for HTML comment first, fall back to regular comment
            comment = next((action['value'] for action in macro['actions'] 
                          if action['field'] == 'comment_value_html'), None)
            if comment is None:
                comment = next((action['value'] for action in macro['actions'] 
                              if action['field'] == 'comment_value'), '')
            base_content.append(f"Title: {title}\nBody: {convert_to_markdown(comment)}\n\n----\n\n")
        
        # Write files for each brand, including base content
        for brand_id, macros in brand_macros.items():
            content = base_content.copy()  # Start with no_brand macros
            
            # Add brand-specific macros
            for macro in macros:
                title = macro['title']
                # Check for HTML comment first, fall back to regular comment
                comment = next((action['value'] for action in macro['actions'] 
                              if action['field'] == 'comment_value_html'), None)
                if comment is None:
                    comment = next((action['value'] for action in macro['actions'] 
                                  if action['field'] == 'comment_value'), '')
                content.append(f"Title: {title}\nBody: {convert_to_markdown(comment)}\n\n----\n\n")
            
            # Write to markdown file
            filename = f'zendesk_macros_brand_{brand_id}.md'
            with open(filename, 'w', encoding='utf-8') as f:
                f.writelines(content)
            
            print(f"Created markdown file for brand {brand_id}")
        
    except IOError as e:
        print(f"Error writing to file: {e}", file=sys.stderr)
        sys.exit(1)

def get_macros():
    subdomain = os.getenv('ZENDESK_SUBDOMAIN')
    api_key = os.getenv('ZENDESK_AUTH')
    
    url = f'https://{subdomain}.zendesk.com/api/v2/macros.json'
    headers = {
        'Authorization': f'Basic {api_key}'
    }
    
    # Define exclusion list and allowed group IDs
    excluded_macros = {
        "CS::Mail::Datenlöschung aufgrund von Datenmissbrauch",
        "CS::Mail::Weiterleitung EKS smava",
        "CS::Mail::Weiterleitung EKS FC"
    }
    
    allowed_group_ids = {
        4721156610589,
        360000413457,
        360007012397,
        4404584120605,
        4402017514013,
        360009937317
    }
    
    all_macros = []
    while url:
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            try:
                json_data = response.json()
                macros = json_data.get('macros', [])
                
                # Filter macros based on criteria
                filtered_macros = [
                    macro for macro in macros
                    if (
                        macro['title'].startswith('CS::Mail::') and
                        macro['title'] not in excluded_macros and
                        macro.get('restriction') and
                        macro['restriction'].get('ids') and
                        any(group_id in allowed_group_ids for group_id in macro['restriction']['ids']) and
                        macro.get('active', False)  # Only include active macros
                    )
                ]
                
                all_macros.extend(filtered_macros)
                url = json_data.get('next_page')
            except ValueError:
                print("Error: Response is not in JSON format.", file=sys.stderr)
                sys.exit(1)
        else:
            print(f"Error: {response.status_code} - {response.text}", file=sys.stderr)
            sys.exit(1)
    
    # Categorize macros by brand_id
    no_brand_macros = []
    brand_macros = {}
    
    for macro in all_macros:
        brand_id = None
        for action in macro.get('actions', []):
            if action.get('field') == 'brand_id':
                brand_id = action.get('value')
                break
        
        if brand_id:
            if brand_id not in brand_macros:
                brand_macros[brand_id] = []
            brand_macros[brand_id].append(macro)
        else:
            no_brand_macros.append(macro)
    
    # Write categorized macros to files
    try:
        write_markdown_files(no_brand_macros, brand_macros)
        
        print(f"\nSummary of macros processed:")
        print(f"- No brand ID: {len(no_brand_macros)} macros")
        for brand_id, macros in brand_macros.items():
            print(f"- Brand ID {brand_id}: {len(macros)} macros")
    except IOError as e:
        print(f"Error writing to file: {e}", file=sys.stderr)
        sys.exit(1)

def main():
    try:
        get_macros()
    except BrokenPipeError:
        devnull = os.open(os.devnull, os.O_WRONLY)
        os.dup2(devnull, sys.stdout.fileno())
        sys.exit(1)

if __name__ == "__main__":
    main()
