#!/bin/bash

source .env

ticket_id=$1
if [ -z "$ticket_id" ]; then
    echo "Error: Please provide a ticket ID"
    echo "Usage: $0 <ticket_id>"
    exit 1
fi

if [ -z "$ZENDESK_AUTH" ]; then
    echo "Error: ZENDESK_AUTH not set. Please set with your base64 encoded 'email:token'"
    exit 1
fi

echo -e "\nFetching comments for ticket ID: $ticket_id"
curl -s -X GET "https://smava.zendesk.com/api/v2/tickets/$ticket_id.json" \
    -H "Authorization: Basic $ZENDESK_AUTH" \
    -H "Content-Type: application/json" | jq '.' 
