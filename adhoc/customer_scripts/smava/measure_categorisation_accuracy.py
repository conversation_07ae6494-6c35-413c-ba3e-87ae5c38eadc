import requests
from datetime import datetime, timedelta
import json
import csv

# Replace these values with your actual credentials and field ID
ZENDESK_SUBDOMAIN = 'smava'
FIELD_ID_1 = "19840724383261"
FIELD_ID_2 = "19840725382429"

# Calculate the date 14 days ago
date_14_days_ago = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')

# Zendesk API endpoint to search tickets
url = f'https://{ZENDESK_SUBDOMAIN}.zendesk.com/api/v2/search.json'

# Search query to find tickets updated in the last 14 days with a specific field ID
query = f'type:ticket updated>{date_14_days_ago} (custom_field_{FIELD_ID_2}:* custom_field_{FIELD_ID_1}:*) order_by:created sort:asc'

# Headers for the request
headers = {
    'Content-Type': 'application/json',
    'Authorization': 'Basic xxxx'
}


def get_field_value(custom_fields, field_id):
    return next((item["value"] for item in custom_fields if item["id"] == int(field_id)), None)

MAX_PAGES = 10
next_page = url
num_pages = 0
result_set = []

while next_page != None and num_pages < MAX_PAGES:
    # Make the request to the Zendesk API
    response = requests.get(next_page, headers=headers, params={'query': query})

    # Check for successful response
    data = response.json()

    if response.status_code == 200:
        tickets = response.json().get('results', [])
        print(f"Found {len(tickets)} tickets with triage values in the last 14 days.")

        for ticket in tickets:
            print(f"Ticket ID: {ticket['id']}")

            final_categorisation_l1 = get_field_value(ticket["custom_fields"], "360001505817")
            final_categorisation_l2 = get_field_value(ticket["custom_fields"], "9080259303325")
            esl_initial_category_1 = get_field_value(ticket["custom_fields"], "19840724383261")
            esl_initial_category_2 = get_field_value(ticket["custom_fields"], "19840725382429")
            result_set.append({
                "id": ticket["id"],
                "subject": ticket["subject"],
                "description": ticket["description"],
                "final_categorisation_l1": final_categorisation_l1,
                "final_categorisation_l2": final_categorisation_l2,
                "esl_initial_category_1": esl_initial_category_1,
                "esl_initial_category_2": esl_initial_category_2,
                "l1_match": final_categorisation_l1 == esl_initial_category_1,
                "l2_match": final_categorisation_l2 == esl_initial_category_2,
            })
    else:
        print(f"Failed to retrieve tickets. Status code: {response.status_code}, Response: {response.text}")

    if "next_page" in data:
        next_page = data["next_page"]
    else:
        next_page = None
    num_pages += 1

if result_set:
    keys = result_set[0].keys()

    # Write result_set to CSV file
    with open('output.csv', 'w', newline='') as output_file:
        dict_writer = csv.DictWriter(output_file, fieldnames=keys)
        dict_writer.writeheader()
        dict_writer.writerows(result_set)

    print("JSON data has been written to output.csv")
else:
    print("No data to write")

total = len(result_set)
l1_true_count = sum(1 for ticket in result_set if ticket["l1_match"])
l1_false_count = total - l1_true_count

l2_true_count = sum(1 for ticket in result_set if ticket["l2_match"])
l2_false_count = total - l2_true_count

l1_true_percentage = (l1_true_count / total) * 100
l1_false_percentage = (l1_false_count / total) * 100

l2_true_percentage = (l2_true_count / total) * 100
l2_false_percentage = (l2_false_count / total) * 100

# Print the summary stats
print(f"L1 Match Statistics:")
print(f"True: {l1_true_percentage:.2f}%")
print(f"False: {l1_false_percentage:.2f}%")

print(f"\nL2 Match Statistics:")
print(f"True: {l2_true_percentage:.2f}%")
print(f"False: {l2_false_percentage:.2f}%")