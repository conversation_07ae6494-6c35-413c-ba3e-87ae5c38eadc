import pandas as pd
import pprint
import logging

# Set up logger
logger = logging.getLogger(__name__)

# --- Read the main mapping CSV for categorization ---
df = pd.read_csv("~/Downloads/Eesel - Categorization _ drafts - Catagories NEW.csv")
# Forward-fill missing values using ffill() instead of fillna(method='ffill')
df = df.ffill()
mapping_df = df

# --- Read the lead-status mapping CSV ---
# Read as comma-separated instead of tab-separated
lead_status_mapping_df = pd.read_csv("~/Downloads/Eesel - Categorization _ drafts - NEW Field Lead Status.csv")

# Split the combined column into separate columns if needed
if 'value,Status' in lead_status_mapping_df.columns:
    lead_status_mapping_df[['value', 'Status']] = lead_status_mapping_df['value,Status'].str.split(',', n=1, expand=True)
    lead_status_mapping = lead_status_mapping_df.set_index('value')['Status'].to_dict()
else:
    print("Warning: Expected column 'value,Status' not found in the CSV file")
    lead_status_mapping = {}

# --- Define the filtering columns ---
filtering_columns = [
    "Lead- Type (Field-ID: **************)",
    "Lead-Status (Field -ID: **************)",
    "Berater (Field-ID: **************)",
    "AGB valide (Field-ID: **************)"
]

def get_possible_categories(support_ticket, mapping_df, filtering_columns):
    """
    Given a support_ticket dict with keys matching the mapping columns,
    return a list of candidate categories and their definitions.
    
    Args:
        support_ticket (dict): Dictionary containing ticket field values
        mapping_df (DataFrame): DataFrame containing the mapping rules
        filtering_columns (list): List of columns to use for filtering
    """
    possible = []
    
    for idx, row in mapping_df.iterrows():
        logger.debug(f"Evaluating rule {idx}:")
        rule_matches = True
        rule_details = {}
        
        for col in filtering_columns:
            rule_value = row[col]
            ticket_value = support_ticket.get(col)
            
            if pd.notna(rule_value):
                rule_details[col] = rule_value
                
                if col == "Lead- Type (Field-ID: **************)":
                    rule_value_str = str(rule_value).strip().lower()
                    if rule_value_str == "all":
                        if ticket_value is not None and str(ticket_value).strip() != "":
                            logger.debug(f"  -> Rule {idx} passes for column '{col}': rule is 'ALL' and ticket value '{ticket_value}' is provided")
                            continue
                        else:
                            logger.debug(f"  -> Rule {idx} fails for column '{col}': rule is 'ALL' but ticket value is empty")
                            rule_matches = False
                            break
                    elif rule_value_str == "undefined":
                        if ticket_value is None or str(ticket_value).strip() == "":
                            logger.debug(f"  -> Rule {idx} passes for column '{col}': rule is 'undefined' and ticket value is empty")
                            continue
                        else:
                            logger.debug(f"  -> Rule {idx} fails for column '{col}': rule is 'undefined' but ticket value '{ticket_value}' is provided")
                            rule_matches = False
                            break
                
                else:
                    if str(rule_value).strip().lower() == "irrelevant":
                        logger.debug(f"  -> Rule {idx} passes for column '{col}': rule is 'irrelevant'")
                        continue
                
                options = [option.strip() for option in str(rule_value).split(",")]
                if ticket_value in options:
                    logger.debug(f"  -> Rule {idx} passes for column '{col}': ticket value '{ticket_value}' is in {options}")
                else:
                    logger.debug(f"  -> Rule {idx} fails for column '{col}': ticket value '{ticket_value}' is not in {options}")
                    rule_matches = False
                    break
        
        logger.debug(f"Rule {idx} details: {rule_details}")
        if rule_matches:
            logger.debug(f"Rule {idx} matched.\n")
            possible.append({
                "Kategorie": row["Kategorie"],
                "Definition": row["Definition (engl)"]
            })
        else:
            logger.debug(f"Rule {idx} did not match.\n")
        logger.debug("-" * 60)
        
    return possible

# --- Example usage ---
# Define a support ticket with field values (adjust these as needed)
support_ticket = {
    "Lead- Type (Field-ID: **************)": "Final without offers",
    "Lead-Status (Field -ID: **************)": "declined_bank / declined_customer", 
    "Berater (Field-ID: **************)": "FALSE",
    "AGB valide (Field-ID: **************)": "FALSE"
}

# --- Apply the lead-status mapping ---
lead_status_field = "Lead-Status (Field -ID: **************)"
if lead_status_field in support_ticket:
    original_status = support_ticket[lead_status_field]
    mapped_status = lead_status_mapping.get(original_status)
    if mapped_status and str(mapped_status).strip() != "":
        logger.info(f"Mapping lead-status '{original_status}' to '{mapped_status}'")
        support_ticket[lead_status_field] = mapped_status
    else:
        logger.info(f"No mapping found for lead-status '{original_status}', keeping original.")

# Get the possible candidate categories based on the support ticket fields
possible_categories = get_possible_categories(support_ticket, mapping_df, filtering_columns)

def format_categories(categories):
    """
    Print categories and their definitions in a nicely formatted way.
    
    Args:
        categories (list): List of dictionaries containing 'Kategorie' and 'Definition' keys
    """
    lines = []
    for candidate in categories:
        lines.append(f"Category Name: {candidate['Kategorie']}")
        lines.append(f"{candidate['Definition']}")
        lines.append("-----")

    return "\n".join(lines)

print(format_categories(possible_categories))