import re
import logging
from collections import OrderedDict

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_canned_replies(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # Normalize line endings to Unix style
    content = content.replace('\r\n', '\n').replace('\r', '\n')

    # Split the content into individual replies using ------ as separator
    replies = content.split('------')
    total_replies = len(replies)
    logger.info(f"Found {total_replies} total replies in the input file")
    
    # Dictionary to store unique replies (keyed by (title, body))
    unique_replies = OrderedDict()
    
    # Updated regular expressions to handle line starts
    title_pattern = re.compile(r'^Title:\s*(.*)$', re.MULTILINE)
    body_pattern = re.compile(r'^Body:\s*(.*?)(?=\n^Title:|\Z)', re.MULTILINE | re.DOTALL)
    
    duplicates = 0
    for idx, reply in enumerate(replies, start=1):
        reply = reply.strip()
        if not reply:
            logger.debug(f"Reply {idx} is empty. Skipping.")
            continue
            
        title_match = title_pattern.search(reply)
        body_match = body_pattern.search(reply)
        
        if title_match and body_match:
            title = title_match.group(1).strip()
            body = body_match.group(1).strip()
            
            logger.debug(f"Processing Reply {idx}: Title='{title}'")
            
            # Create a unique key based on title and body
            unique_key = (title, body)
            
            if unique_key not in unique_replies:
                unique_replies[unique_key] = reply  # Store reply using the unique key
                logger.debug(f"Added Reply {idx} as unique.")
            else:
                duplicates += 1
                logger.info(f"Duplicate found. Title: '{title}' (Reply {idx})")
        else:
            logger.warning(f"Reply {idx} missing Title or Body. Skipping.")
    
    unique_count = len(unique_replies)
    logger.info(f"Found {duplicates} duplicate replies")
    logger.info(f"Keeping {unique_count} unique replies")
    
    # Write the deduplicated content back to a new file
    output_file = 'deduplicated_prompt.txt'
    with open(output_file, 'w', encoding='utf-8') as f:
        for reply in unique_replies.values():
            f.write(reply)
            f.write('\n------\n\n')
    
    # Verify the output format
    with open(output_file, 'r', encoding='utf-8') as f:
        output_content = f.read()
        if not output_content.strip().endswith('------'):
            logger.warning("Output file format might not match expected format")

if __name__ == '__main__':
    parse_canned_replies('prompt.txt')
