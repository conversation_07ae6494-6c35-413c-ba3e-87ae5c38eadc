#!/bin/bash

source .env

ticket_id=$1
if [ -z "$ticket_id" ]; then
    echo "Error: Please provide a ticket ID"
    echo "Usage: $0 <ticket_id>"
    exit 1
fi

# Get the API token from the environment variable
if [ -z "$EESEL_API_TOKEN" ]; then
    echo "Error: API token is not set. Please set the API_TOKEN environment variable."
    exit 1
fi

echo -e "\nTesting webhook with ticket ID: $ticket_id"
response=$(curl -s -X POST "https://dashboard.eesel.ai/api/webhooks/zendesk" \
    -H "Content-Type: application/json" \
    -H "x-eesel-api-token: $EESEL_API_TOKEN" \
    -H "x-eesel-connection-id: smava-eesel-ai-cid" \
    -H "x-eesel-self-serve: true" \
    -d "{
        \"id\": \"$ticket_id\",
        \"event_type\": \"ticket.draft\"
    }")

echo "Webhook Test Response:"
echo "$response" | jq '.' 2>/dev/null || echo "$response"
