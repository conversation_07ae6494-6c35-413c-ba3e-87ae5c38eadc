import os
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.cluster import KMeans
import json

def process_and_cluster_questions(files, model):
    aggregated_qa_pairs = []  # Aggregate QA pairs from all files

    # Process each file to aggregate QA pairs
    for file_path in files:
        with open(file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)

        for item in data:
            item = item["article"]
            if item and "result" in item and item["result"] is not None and "qa" in item["result"]:
                qa_list = item["result"]["qa"]
                if isinstance(qa_list, list):
                    for q in qa_list:
                        if isinstance(q, dict) and "q" in q and "a" in q:
                            aggregated_qa_pairs.append((q["q"], q["a"]))

    if not aggregated_qa_pairs:
        print("No QA pairs found. Exiting.")
        return {}

    questions, answers = zip(*aggregated_qa_pairs)
    embeddings = model.encode(questions)

    similarity_matrix = cosine_similarity(embeddings)
    threshold = 0.9
    duplicates = set()
    for i in range(len(similarity_matrix)):
        for j in range(i + 1, len(similarity_matrix)):
            if similarity_matrix[i][j] > threshold:
                duplicates.add(j)

    filtered_qa_pairs = [(q, a) for i, (q, a) in enumerate(aggregated_qa_pairs) if i not in duplicates]
    filtered_embeddings = [emb for i, emb in enumerate(embeddings) if i not in duplicates]

    num_clusters = 10
    kmeans = KMeans(n_clusters=num_clusters, random_state=42)
    cluster_labels = kmeans.fit_predict(filtered_embeddings)

    clustered_qa_pairs = {}
    for qa_pair_idx, cluster_idx in enumerate(cluster_labels):
        clustered_qa_pairs.setdefault(cluster_idx, []).append(filtered_qa_pairs[qa_pair_idx])

    return clustered_qa_pairs

# Initialize the SBERT model
model = SentenceTransformer('all-MiniLM-L6-v2')

# Directories
input_dir = './output/asij/'
output_dir = './final/asij/'
output_file_path = os.path.join(output_dir, 'final_aggregated.txt')

if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# Gather all file paths
file_paths = [os.path.join(input_dir, f) for f in os.listdir(input_dir) if f.startswith("output-") and f.endswith(".json")]

# Process all files and cluster QA pairs
clustered_questions = process_and_cluster_questions(file_paths, model)

# Write the clustered questions to the output file
with open(output_file_path, 'w', encoding='utf-8') as output_file:
    for cluster_idx, qa_pairs in clustered_questions.items():
        output_file.write(f"Cluster {cluster_idx}: {len(qa_pairs)} QA pairs\n")
        for question, answer in qa_pairs:
            output_file.write(f"Q: {question}\nA: {answer}\n\n")
        output_file.write("-----------\n")
