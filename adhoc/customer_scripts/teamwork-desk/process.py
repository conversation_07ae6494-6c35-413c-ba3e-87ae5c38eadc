import json
import re
import glob
import os  # Needed for directory operations
import html2text
import xm<PERSON>odict
from time import sleep
from anthropic import Anthropic
import openai

# Configuration for OpenAI API
openai.api_key = '-----'

# client = Anthropic(api_key=anthropic_key)

def html_to_markdown(html_content):
    """
    Converts HTML content to Markdown.
    """
    h = html2text.HTML2Text()
    h.ignore_links = True
    return h.handle(html_content)

def sanitize_text(text):
    """
    Strips out URLs and replaces them with a placeholder text.
    """
    return re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '**link redacted**', text)

SYSTEM_PROMPT = '''
Identify common issues and general knowledge that could be useful to multiple users.
YOU MUST ONLY use information provided to create your answer, YOU MUST NOT use any outside information.

Extract and structure your findings in the form of Question and Answer (Q&A) entries for a knowledge base, with reference quotes that contain the answer.
The "reference-quote" should contain the snippet of information used to formulate the "answer", not the "question".

If there is no viable answer, just respond with <result></result>

ALWAYS focus on information that can be generalized and applicable to a wider audience.
DO NOT include information specific to this exchange, such as the subject/title of the ticket, the created date, or any other metadata.
DO NOT create candidate articles based on details that apply only to this specific exchange.
DO NOT say anything other than the Q&A items.
ALWAYS provide sufficient detail and relevant keywords so it can easily be retrieved in a search system.
DO NOT refer to the email in any candidate articles.
Articles should be written as if they were to appear on a public knowledge base.
NEVER say things like "According to the information provided", "Based on the ticket", etc.

ALWAYS include as much detail in your answer as is available in the information provided.
DO NOT recommend articles specific to a particular customer. 
DO NOT include PII in articles.

DO NOT include articles that do not contain a helpful answer.

Respond with XML, for example:
<result>
    <id>{ticket_id}</id>
    <qa>
        <q>How do I reset my password?</q>
        <a>Click the 'reset your password' button</a>
        <reference-quote>Hi John, you can reset you password by clicking the "Reset your password" button</reference-quote>
    </qa>
</result>
'''

def xml_to_json(xml_string):
    """
    Parse the XML string into a Python dictionary.
    """
    return xmltodict.parse(xml_string)

def process_tickets(file_path):
    count = 0
    result = []

    # Load the JSON data from the file
    with open(file_path, 'r') as file:
        tickets = json.load(file)

    for ticket in tickets:
        # Process each field in the ticket
        for key, value in ticket.items():
            if isinstance(value, str):
                # Convert HTML to Markdown and sanitize the text
                ticket[key] = sanitize_text(html_to_markdown(value))


        new_ticket = {
            "id": ticket["id"],
            "subject": ticket["subject"],
            "threads": [sanitize_text(html_to_markdown(thread["body"])) for thread in ticket["threads"]]
        }

        formatted_ticket_json = json.dumps(new_ticket, indent=4)

        try:
            # print(formatted_ticket_json)
            count += 1
            # message = client.messages.create(
            #     max_tokens=250,
            #     system=SYSTEM_PROMPT,
            #     messages=[
            #         {"role": "user", "content": formatted_ticket_json},
            #         {"role": "assistant", "content": "<result>"}
            #     ],
            #     model="claude-3-haiku-20240307",
            # )

            # # for item in message.content:
            # #     print("Call completed.")
            #     result.append(xml_to_json("<result>\n" + item.text))
            response = openai.ChatCompletion.create(
                model="gpt-4-0125-preview",
                messages=[
                    {"role": "system", "content": SYSTEM_PROMPT},
                    {"role": "user", "content": formatted_ticket_json}
                ],
                temperature=0,
                max_tokens=500
            )

            print(response)

            print("Call completed.")
            item = response['choices'][0]['message']['content']
            result.append({
                "article": xml_to_json(item),
                "source": new_ticket
            })

            sleep(1)

            if count > 50:
                break
        except Exception as e:
            print(f"Exception: {e}")

    # Ensure the output directory exists
    os.makedirs('output', exist_ok=True)

    # Generate the output filename by replacing the directory names
    output_filename = file_path.replace("tickets", "output")
    os.makedirs(os.path.dirname(output_filename), exist_ok=True)

    # Write the data to a JSON file
    with open(output_filename, 'w') as file:
        json.dump(result, file, indent=4)

if __name__ == "__main__":
    print("Hello!")
    i = 0
    # Use glob to find all ticket files in the tickets directory
    for file_path in glob.glob('./tickets/asij/tickets-*.json'):
        i += 1
        if i < 10:
            continue

        print("Processing: " + file_path)
        process_tickets(file_path)

        if i > 20:
            break
