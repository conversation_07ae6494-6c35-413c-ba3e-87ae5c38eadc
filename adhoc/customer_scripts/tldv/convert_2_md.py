import json
from markdownify import markdownify as md

# Load the array of strings from the JSON file
with open('html_conversations.json', 'r') as file:
    data = json.load(file)

# Function to convert HTML to Markdown
def convert_html_to_markdown(text):
    return md(text)

# Convert each string in the array from HTML to Markdown
converted_data = [convert_html_to_markdown(conversation) for conversation in data]

# Save the converted array back to a new JSON file
with open('converted_conversations_markdown.json', 'w') as file:
    json.dump(converted_data, file, indent=2)
