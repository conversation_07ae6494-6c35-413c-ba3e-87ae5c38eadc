import csv
import json
import requests

# Function to read conversation IDs from a CSV file
def read_conversation_ids(csv_file_path):
    with open(csv_file_path, mode='r', newline='') as csvfile:
        reader = csv.reader(csvfile)
        conversation_ids = [row[0] for row in reader]
    return conversation_ids

# Function to fetch conversation details from Intercom API
def fetch_conversation_detail(conversation_id, access_token):
    url = f'https://api.intercom.io/conversations/{conversation_id}'
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Accept': 'application/json'
    }
    response = requests.get(url, headers=headers)
    if response.status_code == 200:
        return response.json()
    else:
        print(f'Failed to fetch conversation {conversation_id}: {response.status_code}')
        return None

# Function to fetch all conversations in batches of 20 and then fetch details individually
def fetch_conversations(conversation_ids, access_token, json_file_path):
    all_conversations = []
    total_conversations = len(conversation_ids)
    batch_size = 20

    for i in range(0, total_conversations, batch_size):
        batch_ids = conversation_ids[i:i+batch_size]
        
        for idx, conversation_id in enumerate(batch_ids):
            print(f'Fetching conversation {i + idx + 1}/{total_conversations}')
            content = fetch_conversation_detail(conversation_id, access_token)
            if content:
                all_conversations.append(content)
        
        # Save the progress after each batch
        save_conversations_to_json(all_conversations, json_file_path)
    
    return all_conversations

# Function to save conversations to a JSON file
def save_conversations_to_json(conversations, json_file_path):
    with open(json_file_path, 'w') as jsonfile:
        json.dump(conversations, jsonfile, indent=4)

# Main function to orchestrate the workflow
def main(csv_file_path, json_file_path, access_token):
    conversation_ids = read_conversation_ids(csv_file_path)
    fetch_conversations(conversation_ids, access_token, json_file_path)

if __name__ == '__main__':
    # Define your file paths and Intercom access token here
    csv_file_path = './inbox_data.csv'
    json_file_path = './conversations.json'
    access_token = 'xxxxx'
    
    main(csv_file_path, json_file_path, access_token)