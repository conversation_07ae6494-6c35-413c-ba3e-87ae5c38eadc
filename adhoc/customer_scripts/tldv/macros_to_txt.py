import json

def json_to_markdown(json_file, output_file):
    with open(json_file, 'r') as file:
        data = json.load(file)

    with open(output_file, 'w') as file:
        for reply in data['saved_replies']:
            # Extract title
            title = reply.get('name', 'No Title')
            file.write(f"# {title}\n\n")

            # Extract body
            body = []
            for block in reply.get('blocks', []):
                if block['type'] == 'paragraph' and block['text']:
                    body.append(block['text'])
                elif block['type'] == 'unorderedList':
                    for item in block['items']:
                        body.append(f"- {item}")
                elif block['type'] == 'orderedList':
                    for i, item in enumerate(block['items'], 1):
                        body.append(f"{i}. {item}")
                elif block['type'] == 'messengerCard':
                    card_title = block['canvas']['stored_data']['anchor_link']['title']
                    card_preview = block['canvas']['stored_data']['anchor_link']['preview']
                    article_id = block['canvas']['stored_data']['article_id']
                    base_url = "https://intercom.help/tldv/en/articles/"
                    full_url = f"{base_url}{article_id}"
                    body.append(f"**{card_title}**\n\n[{card_preview}]({full_url})")
                elif block['type'] == 'articleLink':
                    article_title = block['title']
                    article_url = block['url']
                    body.append(f"[{article_title}]({article_url})")

            file.write("\n".join(body))
            file.write("\n\n")

if __name__ == "__main__":
    json_file = 'saved_replies.json'  # Replace with your JSON file path
    output_file = 'output.md'  # Replace with your desired output file path
    json_to_markdown(json_file, output_file)
