import json
import argparse
from typing import Any, List
from langchain_openai import Chat<PERSON>penAI
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import PromptTemplate
from langchain.output_parsers import ResponseSchema, StructuredOutputParser
from langchain.globals import set_debug
from concurrent.futures import ProcessPoolExecutor
import logging  # {{ edit_1 }}
import sys
from langchain_community.callbacks import get_openai_callback  # {{ edit_1 }}

# Configure logging to output to stdout
logging.basicConfig(stream=sys.stdout, level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Set debug mode
set_debug(False)

# Initialize the language model
llm = ChatOpenAI(model="gpt-4o-mini", temperature=0, request_timeout=10000)

total_cost = 0

# Define the response schema
response_schemas = [
    ResponseSchema(
        name="answer",
        description="The answer to the provided question based on the data in the file.",
        type="string"
    ),
    ResponseSchema(
        name="answer_rationale",
        description="A step-by-step, detailed explanation of how the answer was derived, including quotes from the data."
    )
]

# Define the response schema
setup_response_schema = [
    ResponseSchema(
        name="step1_instructions",
        description="Describe how to process each conversation individually.",
        type="string"
    ),
    ResponseSchema(
        name="step1_instructions_rationale",
        description="A step-by-step, detailed explanation of the step1_instructions were decided."
    ),
    ResponseSchema(
        name="step2_instructions",
        description="Describe how to aggregate and analyze the results from Step 1 to answer the question.",
        type="string"
    ),
    ResponseSchema(
        name="step2_instructions_rationale",
        description="A step-by-step, detailed explanation of the step2_instructions were decided."
    )
]

# Create the structured output parser
response_parser = StructuredOutputParser.from_response_schemas(response_schemas)

setup_response_parser = StructuredOutputParser.from_response_schemas(setup_response_schema)

PLANNING_PROMPT = PromptTemplate(
    template="""
        You are provided with a set of transformed conversations in JSON format. Your task is to plan a two-step process to answer the following question based on the information found in these conversations:

        Question: {question}

        Step 1: Describe how to process each conversation individually.
        Step 2: Describe how to aggregate and analyze the results from Step 1 to answer the question.

        Sample conversations: {sample_conversations}

        {format_instructions}
    """,
    input_variables=["sample_conversations", "question"],
    partial_variables={"format_instructions": setup_response_parser.get_format_instructions()}
)

# Define the step processing prompt template
STEP_PROCESSING_PROMPT = PromptTemplate(
    template="""
        You are provided with a single transformed conversation in JSON format. Your task is to process this conversation according to the following instructions:

        Instructions: {step_instructions}

        Conversation: {conversation}

        {format_instructions}
    """,
    input_variables=["conversation", "step_instructions"],
    partial_variables={"format_instructions": response_parser.get_format_instructions()}
)

# Define the final analysis prompt template
FINAL_ANALYSIS_PROMPT = PromptTemplate(
    template="""
        You are provided with the results from processing individual conversations. Your task is to aggregate and analyze these results according to the following instructions:

        Instructions: {step_instructions}

        Step Results: {step_results}

        {format_instructions}
    """,
    input_variables=["step_results", "step_instructions"],
    partial_variables={"format_instructions": response_parser.get_format_instructions()}
)

# Define the process_conversation function at the module level
def process_conversation(conversation, step1_instructions):  # {{ edit_1 }}
    global total_cost  # Declare total_cost as global
    step1_chain = STEP_PROCESSING_PROMPT | llm | response_parser  # Recreate the chain here
    with get_openai_callback() as cb:  # {{ edit_5 }}
        result = step1_chain.invoke({
            "conversation": json.dumps(conversation, indent=4),
            "step_instructions": step1_instructions
        })
        total_cost += cb.total_cost
    return result

# Function to load data and analyze
def analyze_data(file_path: str, question: str, count: int):  # {{ edit_3 }}
    global total_cost  # Declare total_cost as global
    logging.info("Loading data from %s", file_path)  # {{ edit_2 }}
    with open(file_path, 'r') as file:
        transformed_conversations = json.load(file)

    # Limit sample conversations to the first 10 for planning
    sample_conversations = transformed_conversations[:10]  # {{ edit_4 }}
    # Limit total processed conversations to the specified count
    transformed_conversations = transformed_conversations[:count]  # {{ edit_5 }}
    logging.info("Loaded %d conversations", len(transformed_conversations))  # {{ edit_3 }}

    # Step 1: Planning
    logging.info("Generating planning prompt")  # {{ edit_4 }}
    planning_chain = PLANNING_PROMPT | llm | setup_response_parser
    planning_result = planning_chain.invoke({
        "sample_conversations": json.dumps(sample_conversations),
        "question": question
    })
    step1_instructions = planning_result['step1_instructions']
    step2_instructions = planning_result['step2_instructions']
    logging.info("Planning instructions generated") 
    logging.info("step1_instructions: %s", step1_instructions)
    logging.info("step2_instructions: %s", step2_instructions) 

    # Step 2: Process each conversation using RunnableParallel
    step1_results = []
    logging.info("Processing conversations")
    with ProcessPoolExecutor(max_workers=30) as executor:  # {{ edit_1 }}
        futures = {
            executor.submit(process_conversation, conversation, step1_instructions): conversation for conversation in transformed_conversations
        }
        for future in futures:
            try:
                result = future.result(timeout=10)  # {{ edit_2 }} 
                step1_results.append(result)
            except Exception as e:
                logging.error("Error processing conversation: %s", e)  # {{ edit_1 }}

    # Step 3: Final analysis
    logging.info("Generating final analysis prompt")  # {{ edit_7 }}

    final_analysis_chain = FINAL_ANALYSIS_PROMPT | llm | response_parser

    with get_openai_callback() as cb:  # {{ edit_5 }}
        final_response = final_analysis_chain.invoke({
        "step_results": json.dumps(step1_results, indent=4),
            "step_instructions": step2_instructions
        })
        total_cost += cb.total_cost
    logging.info("Final analysis completed")  # {{ edit_7 }}

    return final_response['answer']  # {{ edit_1 }}

# Main function to handle CLI arguments and run analysis
def main():
    parser = argparse.ArgumentParser(description="Analyze transformed conversations and answer a question.")
    parser.add_argument("file_path", type=str, help="Path to the transformed_conversations.json file")
    parser.add_argument("question", type=str, help="The question to be answered by the AI")
    parser.add_argument("--count", type=int, default=10, help="Number of conversations to analyze")  # {{ edit_1 }}

    args = parser.parse_args()

    result = analyze_data(args.file_path, args.question, args.count)  # {{ edit_2 }}
    print(f"Question: {args.question}\nAnswer: {result}\n")  # {{ edit_1 }}

    print(f"The total cost to compute to answer this question was {total_cost}")

if __name__ == "__main__":
    main()
