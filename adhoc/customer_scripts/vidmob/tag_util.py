import argparse
import os
import requests

# Get the Intercom PAT from the environment variable
INTERCOM_PAT = os.getenv('INTERCOM_PAT')

if not INTERCOM_PAT:
    raise ValueError("Please set the INTERCOM_PAT environment variable")

# Base URL for Intercom API
BASE_URL = "https://api.intercom.io"

# Headers for the API requests
HEADERS = {
    'Authorization': f'Bearer {INTERCOM_PAT}',
    'Intercom-Version': '2.11',
    'Content-Type': 'application/json',
    'Accept': 'application/json'
}

# Categories array
CATEGORIES = [
    "General Platform",
    "Creative Analytics",
    "Creative Scoring",
    "Creative Studio",
    "Creator Studio",
    "Integrations",
    "Organization",
    "Workspace",
    "Notifications",
    "Locked Out/2FA",
    "SSO",
    "Platform Access",
    "Data Export",
    "Feature Request",
    "Ad Accounts",
    "API",
    "Executive Dashboard",
    "In Flight Scorecards",
    "Ad Account Scorecards",
    "Pre Flight Scorecards",
    "Criteria Management",
    "Scoring Override",
    "Adherence Report",
    "Impressions Adherence Report",
    "Adoption Report",
    "Diversity Report",
    "Insights",
    "KPIs",
    "Element Report",
    "Media Impact Report",
    "Individual Creative View",
    "Creative Leaderboard",
    "Comparison Report",
    "Creative Manager",
    "Project Kickoff",
    "Completing Project",
    "Brief",
    "Draft Review",
    "Final Files",
    "Assets",
    "Project Creation",
    "Project Access",
    "Available for Work (Creator)",
    "Creator Payouts",
    "Creator Projects"
]

def get_admin(accessToken):
    url = "https://api.intercom.io/me"

    headers = {
        "accept": "application/json",
        "authorization": "Bearer " + accessToken    
    }

    admin = requests.get(url, headers=headers).json()
    return admin

def get_tag_mapping():
    try:
        response = requests.get(f"{BASE_URL}/tags", headers=HEADERS)
        response.raise_for_status()
        tags = response.json().get('data', [])
        
        tag_map = {}
        categories_lower = {category.lower(): category for category in CATEGORIES}
        
        for tag in tags:
            tag_name_lower = tag['name'].lower()
            if tag_name_lower in categories_lower:
                tag_map[categories_lower[tag_name_lower]] = tag['id']
        
        return tag_map
    except requests.exceptions.RequestException as e:
        print(f"An error occurred: {e}")
        return {}

def list_tags():
    tag_map = get_tag_mapping()
    
    print("Tag Map:")
    for name, tag_id in tag_map.items():
        print(f"Name: {name}, ID: {tag_id}")
    
    # Identify categories with no associated tag ID
    missing_tags = [category for category in CATEGORIES if category not in tag_map]
    
    if missing_tags:
        print("\nCategories with no associated tag ID:")
        for category in missing_tags:
            print(category)
    else:
        print("\nAll categories have associated tag IDs.")

def create_tag(name):
    try:
        payload = {'name': name}
        response = requests.post(f"{BASE_URL}/tags", headers=HEADERS, json=payload)
        response.raise_for_status()
        tag = response.json()
        print(f"Created tag: ID: {tag['id']}, Name: {tag['name']}")
    except requests.exceptions.RequestException as e:
        print(f"An error occurred: {e}")

def update_conversation_tag(conversation_id, tag_id):
    try:
        admin = get_admin(INTERCOM_PAT)
        if 'id' not in admin:
            print(f"Error: 'id' not found in admin response: {admin}")
            return
        admin_id = admin['id']
        payload = {'id': tag_id, 'admin_id': admin_id}
        response = requests.post(f"{BASE_URL}/conversations/{conversation_id}/tags", headers=HEADERS, json=payload)
        response.raise_for_status()
        print(f"Updated conversation {conversation_id} with tag {tag_id}")
    except requests.exceptions.RequestException as e:
        print(f"An error occurred: {e}")

def main():
    parser = argparse.ArgumentParser(description='Intercom CLI Tool')
    subparsers = parser.add_subparsers(dest='command')

    # List tags command
    parser_list = subparsers.add_parser('list_tags', help='List all tags')

    # Create tag command
    parser_create = subparsers.add_parser('create_tag', help='Create a new tag')
    parser_create.add_argument('name', type=str, help='Name of the new tag')

    # Update conversation tag command
    parser_update = subparsers.add_parser('update_conversation_tag', help='Update conversation with a tag')
    parser_update.add_argument('conversation_id', type=str, help='ID of the conversation')
    parser_update.add_argument('tag_id', type=str, help='ID of the tag')

    args = parser.parse_args()

    if args.command == 'list_tags':
        list_tags()
    elif args.command == 'create_tag':
        create_tag(args.name)
    elif args.command == 'update_conversation_tag':
        update_conversation_tag(args.conversation_id, args.tag_id)
    else:
        parser.print_help()

if __name__ == '__main__':
    main()
