import pandas as pd

def summarize_sales_and_calculate_churn(csv_file_path):
    """
    Summarizes sales and calculates churn from a CSV file containing sales data.

    Parameters:
    - csv_file_path: Path to the CSV file containing sales data.

    The function prints out a summary of sales and churn amounts by sale type and month-year.
    """

    # Load the CSV file into a DataFrame
    df = pd.read_csv(csv_file_path)

    # Convert saleDate column to datetime format for easier date manipulation
    df['saleDate'] = pd.to_datetime(df['saleDate'])
    # Sort the DataFrame by saleDate to ensure chronological order
    df.sort_values(by='saleDate', inplace=True)

    # Create a YearMonth period column for easier monthly grouping
    df['YearMonth'] = df['saleDate'].dt.to_period('M')
    # Create a MonthYear string column for readable output
    df['MonthYear'] = df['saleDate'].dt.strftime('%B %Y')

    # Group data by saleType and MonthYear, summing up vendorAmount for each group
    sales_summary_df = df.groupby(['saleType', 'MonthYear'])['vendorAmount'].sum().reset_index()
    # Sort the sales summary for readable chronological output
    sales_summary_df = sales_summary_df.sort_values(by=['MonthYear', 'saleType'])

    # Calculate churn by identifying customers who did not make payments in subsequent months
    unique_months = df['YearMonth'].unique()
    churn_data = []

    for i in range(len(unique_months) - 1):
        # Find unique cloudId customers for the current and next month
        current_month_customers = df[df['YearMonth'] == unique_months[i]]['cloudId'].unique()
        next_month_customers = df[df['YearMonth'] == unique_months[i + 1]]['cloudId'].unique()

        # Determine churned customers as those in the current month but not the next
        churned_customers = set(current_month_customers) - set(next_month_customers)

        # Initialize total churn amount for the month
        total_churn = 0
        for cloud_id in churned_customers:
            # Filter payments for each churned customer in the current month
            customer_payments = df[(df['cloudId'] == cloud_id) & (df['YearMonth'] == unique_months[i])]
            # Check if there are any payments to avoid empty DataFrame issues
            if not customer_payments.empty:
                # Get the last payment amount; use absolute value cos refunds are negative in atlassian dataset (refunds included as churn in these calcs)
                last_payment = abs(customer_payments['vendorAmount'].iloc[-1])
                # Sum up the absolute value of last payments for churn amount
                total_churn += last_payment

        # Append churn data for the month to the list
        churn_data.append({'saleType': 'Churned', 'MonthYear': unique_months[i].strftime('%B %Y'), 'vendorAmount': total_churn})

    # Convert churn data into a DataFrame
    churn_df = pd.DataFrame(churn_data)

    # Identify upgrades and calculate Expansion and Existing payments
    expansion_data = []
    for cloud_id in df['cloudId'].unique():
        # Filter data for the current cloudId
        cloud_id_data = df[df['cloudId'] == cloud_id].sort_values(by='YearMonth')
        for month in unique_months:
            # Find if there's an upgrade in the current month
            current_month_data = cloud_id_data[cloud_id_data['YearMonth'] == month]
            if 'Upgrade' in current_month_data['saleType'].values:
                # Calculate Existing payment as the last payment before the upgrade
                previous_month = month - 1
                previous_month_data = cloud_id_data[cloud_id_data['YearMonth'] == previous_month]
                if not previous_month_data.empty:
                    existing_payment = abs(previous_month_data['vendorAmount'].iloc[-1])
                    upgrade_payment = abs(current_month_data[current_month_data['saleType'] == 'Upgrade']['vendorAmount'].sum())
                    expansion_payment = upgrade_payment - existing_payment
                    
                    # Append Existing data
                    expansion_data.append({'saleType': 'Existing', 'MonthYear': previous_month.strftime('%B %Y'), 'vendorAmount': existing_payment})
                    # Append Expansion data
                    expansion_data.append({'saleType': 'Expansion', 'MonthYear': month.strftime('%B %Y'), 'vendorAmount': expansion_payment})
    
    # Convert Expansion and Existing data into a DataFrame
    expansion_df = pd.DataFrame(expansion_data)

    # Combine all data into one DataFrame
    combined_summary_df = pd.concat([sales_summary_df, churn_df, expansion_df], ignore_index=True).sort_values(by=['MonthYear', 'saleType'])

    # Pivot the DataFrame to get saleType as rows and MonthYear as columns, filling missing values with 0
    # Use pivot_table to handle duplicate entries by aggregating them (e.g., summing up the vendorAmount for duplicates)
    pivoted_df = combined_summary_df.pivot_table(index='saleType', columns='MonthYear', values='vendorAmount', aggfunc='sum').fillna(0)


    # Reset index to make saleType a column again for easier CSV-like output
    pivoted_df.reset_index(inplace=True)

    # Reorder columns to ensure chronological order, if necessary
    # This step is optional and depends on the DataFrame's automatic column ordering after pivoting

    # Print the pivoted DataFrame in a CSV-like format
    # Note: Churn includeds refunds
    print(pivoted_df.to_csv(index=False, sep='\t'))


# Example usage
csv_file_path = '~/Downloads/transactions-1221976-20240206T072023Z.csv'  # Replace with the actual path to your CSV
summarize_sales_and_calculate_churn(csv_file_path)
