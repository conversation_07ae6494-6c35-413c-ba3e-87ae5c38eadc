import requests
import json
import os

# Load API URL from .env file
def load_env(key):
    with open('.env') as f:
        for line in f:
            if line.startswith(key):
                return line.strip().split('=')[1]
    return None

API_URL = load_env('OPS_URL')
ATL_KEY = load_env('ATL_KEY')

def send_request(command, params):
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + ATL_KEY
    }
    body = json.dumps({'command': command, 'params': params})
    response = requests.post(API_URL, headers=headers, data=body)

    # Check if the response is valid JSON
    try:
        return response.json()
    except json.JSONDecodeError:
        print(f"Error: Received non-JSON response: {response.text}")
        return None  # or handle it as needed

def main():
    # Ask for namespace ID at the beginning
    namespace_id = input("Enter namespace ID (will be used for all operations): ")
    
    while True:
        print("\nOptions:")
        print("1. Search CQL")
        print("2. Get Storage")
        print("3. Set Storage")
        print("4. Delete Storage")
        print("5. List Spaces")
        print("6. Get Page Attachments")
        print("7. Force Index")
        print("8. Check Page Status")
        print("9. Update Page Index Status")
        print("10. Check Pages Exist")
        print("11. Reconcile")
        print("12. Exit")

        choice = input("Select an option: ")

        if choice == '1':
            cql_query = input("Enter CQL query: ")
            include_expands = input("Include expands? (yes/no): ").lower() == 'yes'
            response = send_request('searchCQL', {
                'cqlQuery': cql_query,
                'includeExpands': include_expands
            })
            print(json.dumps(response, indent=4))

        elif choice == '2':
            key = input("Enter storage key: ")
            response = send_request('getStorage', {'key': key})
            print(json.dumps(response, indent=4))

        elif choice == '3':
            key = input("Enter storage key: ")
            value = input("Enter storage value: ")
            confirm = input(f"Are you sure you want to set the storage key '{key}' with value '{value}'? (yes/no): ")
            if confirm.lower() == 'yes':
                response = send_request('setStorage', {'key': key, 'value': value})
                print(json.dumps(response, indent=4))
            else:
                print("Operation cancelled.")

        elif choice == '4':
            key = input("Enter storage key to delete: ")
            confirm = input(f"Are you sure you want to delete the storage key '{key}'? (yes/no): ")
            if confirm.lower() == 'yes':
                response = send_request('deleteStorage', {'key': key})
                print(json.dumps(response, indent=4))
            else:
                print("Operation cancelled.")

        elif choice == '5':
            response = send_request('listSpaces', {})
            print(json.dumps(response, indent=4))

        elif choice == '6':
            page_id = input("Enter page ID: ")
            response = send_request('getPageAttachments', {'pageId': page_id})
            print(json.dumps(response, indent=4))

        elif choice == '7':
            confirm = input(f"Are you sure you want to force index for namespace '{namespace_id}'? This may take a while. (yes/no): ")
            if confirm.lower() == 'yes':
                response = send_request('forceIndex', {'namespaceId': namespace_id})
                print(json.dumps(response, indent=4))
            else:
                print("Operation cancelled.")

        elif choice == '8':
            page_ids_input = input("Enter page IDs (comma-separated): ")
            page_ids = [pid.strip() for pid in page_ids_input.split(',')]
            
            response = send_request('checkPageStatus', {
                'namespaceId': namespace_id,
                'pageIds': page_ids
            })
            print(json.dumps(response, indent=4))

        elif choice == '9':
            page_id_input = input("Enter page ID: ")
            page_id = page_id_input.strip()
            action = input("Enter status (e.g., 'INDEX', 'DELETE'): ")
            
            response = send_request('updatePageIndexStatus', {
                'namespaceId': namespace_id,
                'pageId': page_id,
                'action': action
            })
            print(json.dumps(response, indent=4))

        elif choice == '10':
            pages_input = input("Enter page details in format 'id1,url1;id2,url2': ")
            # Parse the input into array of {id, url} objects
            pages = []
            for page_detail in pages_input.split(';'):
                if ',' in page_detail:
                    page_id, url = page_detail.strip().split(',')
                    pages.append({'id': page_id.strip(), 'url': url.strip()})
            
            response = send_request('checkPagesExist', {
                'namespaceId': namespace_id,
                'pages': pages
            })
            print(json.dumps(response, indent=4))

        elif choice == '11':
            confirm = input("Are you sure you want to trigger reconciliation? (yes/no): ")
            if confirm.lower() == 'yes':
                response = send_request('reconcile', {})
                print(json.dumps(response, indent=4))
            else:
                print("Operation cancelled.")

        elif choice == '12':
            break

        else:
            print("Invalid option. Please try again.")

if __name__ == "__main__":
    main()
