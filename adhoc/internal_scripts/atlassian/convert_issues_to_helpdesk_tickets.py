import csv
import json
from datetime import datetime
import sys

def convert_csv_to_json(csv_file_path):
    tickets = []
    
    with open(csv_file_path, 'r', encoding='utf-8') as file:
        # First read headers
        csv_reader = csv.reader(file)
        headers = next(csv_reader)
        
        # Find indices of all required columns
        comment_indices = [i for i, col in enumerate(headers) if col.startswith("Comment")]
        summary_index = headers.index('Summary')
        description_index = headers.index('Description')
        issue_key_index = headers.index('Issue key')
        creator_id_index = headers.index('Creator Id')
        created_index = headers.index('Created')
        status_index = headers.index('Status')
        priority_index = headers.index('Priority')

        # Process each row
        for row in csv_reader:
            comments = []
            
            # Get comments from all Comment columns
            for idx in comment_indices:
                if row[idx]:  # If column has data
                    parts = row[idx].split(';')
                    if len(parts) >= 3:
                        comments.append(parts[2].strip())
            
            # Create messages list
            messages = []
            if row[summary_index]:
                messages.append(row[summary_index])
            if row[description_index]:
                messages.append(row[description_index])
            messages.extend(comments)
            
            ticket = {
                "metadata": {
                    "ticket_id": row[issue_key_index],
                    "user_id": row[creator_id_index],
                    "created_at": format_date(row[created_index]),
                    "status": row[status_index].lower(),
                    "priority": row[priority_index].lower()
                },
                "messages": messages
            }
            
            tickets.append(ticket)
    
    return tickets

def format_date(date_str):
    """Convert date from CSV format to ISO format"""
    try:
        dt = datetime.strptime(date_str, "%d/%b/%y %I:%M %p")
        return dt.strftime("%Y-%m-%dT%H:%M:%SZ")
    except ValueError:
        return date_str
    
if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python convert_tickets.py <csv_file_path>")
    else:
        result = convert_csv_to_json(sys.argv[1])
        print(json.dumps(result, indent=4))