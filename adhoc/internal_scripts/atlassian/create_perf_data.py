import requests
import json
import argparse  # Added for command-line argument parsing
import base64  # Added for Base64 encoding
import random  # Added for generating a random string
import string  # Added for generating a random string
import concurrent.futures  # Added for parallel execution
import time  # Added for handling sleep on rate limits

# Set up your Confluence details
confluence_base_url = "https://eesel-dev-helpdesk.atlassian.net/wiki"  # Replace with your Confluence base URL
# Removed hardcoded values for space_key and personal_access_token
# space_key = "YOUR_SPACE_KEY"  # Replace with your space key
# personal_access_token = "YOUR_PERSONAL_ACCESS_TOKEN"  # Replace with your personal access token

# Headers for authentication and content type
headers = {
    "Authorization": "",  # Will be set later
    "Content-Type": "application/json"
}

# Function to create a single page
def create_confluence_page(title, content, parent_id=None):
    # Generate a random string of 6 characters
    random_string = ''.join(random.choices(string.ascii_letters + string.digits, k=6))
    title = f"{space_key}_{title}_{random_string}"  # Update title to include space key and random string
    url = f"{confluence_base_url}/rest/api/content"
    payload = {
        "type": "page",
        "title": title,
        "ancestors": [{"id": parent_id}] if parent_id else [],
        "space": {"key": space_key},
        "body": {
            "storage": {
                "value": content,
                "representation": "storage"
            }
        }
    }
    response = requests.post(url, headers=headers, data=json.dumps(payload))
    
    if response.status_code == 200 or response.status_code == 201:
        print(f"Page '{title}' created successfully.")
        return response.json().get('id')  # Return the ID of the created page
    else:
        print(f"Failed to create page '{title}'. Status Code: {response.status_code}")
        print("Response:", response.text)
        return None

# Main function to create N pages with nesting
def create_multiple_pages(num_pages, current_level=1, parent_id=None):
    if current_level > 3:  # Limit to 3 levels deep
        return
    
    def create_page(i):
        title = f"Level {current_level} Page {i}"  # Customize title for each page
        content = f"<p>This is the content of Level {current_level} page {i}.</p>"  # Customize content as needed
        return create_confluence_page(title, content, parent_id)  # Create page and get its ID

    with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:  # Limit to 3 threads
        futures = {executor.submit(create_page, i): i for i in range(1, num_pages + 1)}
        for future in concurrent.futures.as_completed(futures):
            new_parent_id = future.result()  # Get the result of the completed future
            if new_parent_id is None:
                continue  # Skip if page creation failed
            create_multiple_pages(num_pages, current_level + 1, new_parent_id)  # Create next level of pages

            # Handle rate limits
            if future.exception() is not None:
                response = future.exception()
                if response.status_code == 429:  # Rate limit exceeded
                    retry_after = int(response.headers.get("Retry-After", 1))  # Get retry time or default to 1 second
                    print(f"Rate limit exceeded. Retrying after {retry_after} seconds.")
                    time.sleep(retry_after)  # Wait before retrying
                    create_multiple_pages(num_pages, current_level, parent_id)  # Retry creating pages

# Set up command-line argument parsing
parser = argparse.ArgumentParser(description='Create Confluence pages.')
parser.add_argument('email', type=str, help='Your email address')
parser.add_argument('personal_access_token', type=str, help='Your personal access token')
parser.add_argument('space_key', type=str, help='Your Confluence space key')
parser.add_argument('num_pages', type=int, help='Number of pages to create at each level')

args = parser.parse_args()

# Assign command-line arguments to variables
email = args.email
personal_access_token = args.personal_access_token
space_key = args.space_key
num_pages = args.num_pages

# Update headers with the Base64 encoded email:token
credentials = f"{email}:{personal_access_token}"
headers["Authorization"] = f"Basic {base64.b64encode(credentials.encode()).decode()}"

# Create the pages
create_multiple_pages(num_pages)
