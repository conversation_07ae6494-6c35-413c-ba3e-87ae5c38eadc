from urllib.parse import quote_plus
import requests
from datetime import datetime
import json

def fetch_zendesk_tickets(subdomain, limit=100):
    base_url = f"https://{subdomain}.zendesk.com/api/v2"

    headers = {
        'Authorization': 'Basic xxxx',
        'Content-Type': 'application/json'
    }

    # build your query exactly as before
    search_query = 'type:ticket '
    search_query += 'group:23547801 group:4721156610589 group:360007012397'
    search_query += ' group:360000413457 group:21648943644829 group:"" '
    search_query += '-tags:"voice call" '
    search_query += '-ticket_form_id:360000104498 '
    search_query += '-custom_fields_360001505817:* '

    # **HERE's the fix**: URL–encode the entire query string
    encoded = quote_plus(search_query)

    next_page = (
        f"{base_url}/search.json?"
        f"query={encoded}&sort=-created_at&per_page=100"
    )

    all_tickets = []
    page_count = 1

    while next_page and len(all_tickets) < limit:
        print(f"Fetching page {page_count}…")
        resp = requests.get(next_page, headers=headers)
        resp.raise_for_status()

        data = resp.json()
        all_tickets.extend(data['results'])
        next_page = data.get('next_page')
        page_count += 1

    return all_tickets

def main():
    # Configuration
    SUBDOMAIN = "smava"
    FIELD_ID = "20587828089757"
    
    try:
        tickets = fetch_zendesk_tickets(SUBDOMAIN)
        total_tickets = len(tickets)
        
        print("\nDebug info for first 5 tickets:")
        for i, ticket in enumerate(tickets[:5]):
            print(f"\nTicket {i+1}:")
            print(f"Ticket keys: {ticket.keys()}")
            print("Custom fields:", ticket.get('custom_fields'))
            if 'custom_fields' in ticket:
                print(f"Number of custom fields: {len(ticket['custom_fields'])}")
            
        # Count tickets with the specified field populated
        tickets_with_field = sum(
            1 for ticket in tickets 
            if any(field['id'] == FIELD_ID and field['value'] is not None 
                  for field in ticket['custom_fields'])
        )
        
        # Calculate percentage
        percentage = (tickets_with_field / total_tickets * 100) if total_tickets > 0 else 0
        
        print(f"\nAnalysis Results:")
        print(f"Total tickets: {total_tickets}")
        print(f"Tickets with field {FIELD_ID}: {tickets_with_field}")
        print(f"Percentage with field: {percentage:.2f}%")
            
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    main() 