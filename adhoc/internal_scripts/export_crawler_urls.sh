#!/bin/bash

# Get current time in milliseconds since Unix epoch
current_time=$(date +%s%3N)

# Calculate the time 2 days ago in milliseconds
two_days_ago=$(($(date +%s%3N) - 2*24*60*60*1000))

aws logs filter-log-events \
    --profile eeselAiDeployer \
    --log-group-name "/aws/lambda/eeselCrawler" \
    --filter-pattern "navigating to" \
    --start-time $two_days_ago \
    --end-time $current_time \
    --output json | jq -r '.events[].message' | grep -oP 'https?://[^ ]+' | sort | uniq