import requests
from datetime import datetime

def fetch_zendesk_tickets(subdomain, access_token, limit=10):
    # Base URL for Zendesk API
    base_url = f"https://{subdomain}.zendesk.com/api/v2"
    
    # Headers for authentication
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    # Get recent tickets
    tickets_url = f"{base_url}/tickets.json?sort=-created_at&per_page={limit}&include=users"
    tickets_response = requests.get(tickets_url, headers=headers)
    
    if tickets_response.status_code != 200:
        raise Exception(f"Failed to fetch tickets: {tickets_response.status_code}")
    
    tickets_data = tickets_response.json()
    
    # Process each ticket and fetch its comments
    full_tickets = []
    for ticket in tickets_data['tickets']:
        # Fetch comments for this ticket
        comments_url = f"{base_url}/tickets/{ticket['id']}/comments.json"
        comments_response = requests.get(comments_url, headers=headers)
        
        if comments_response.status_code == 200:
            comments = comments_response.json()['comments']
            # Add comments to ticket data
            ticket['comments'] = comments
            
            # Convert timestamps to readable format
            ticket['created_at'] = datetime.fromisoformat(ticket['created_at'].replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M:%S')
            ticket['updated_at'] = datetime.fromisoformat(ticket['updated_at'].replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M:%S')
            
            full_tickets.append(ticket)
    
    return full_tickets

def main():
    # Configuration
    SUBDOMAIN = "your_subdomain"
    ACCESS_TOKEN = "your_access_token"
    
    try:
        tickets = fetch_zendesk_tickets(SUBDOMAIN, ACCESS_TOKEN)
        
        # Print ticket information
        for ticket in tickets:
            print("\n" + "="*50)
            print(f"Ticket ID: {ticket['id']}")
            print(f"Subject: {ticket['subject']}")
            print(f"Status: {ticket['status']}")
            print(f"Created: {ticket['created_at']}")
            print(f"Updated: {ticket['updated_at']}")
            print("\nComments:")
            
            for comment in ticket['comments']:
                print("-"*30)
                print(f"Author ID: {comment['author_id']}")
                print(f"Created: {datetime.fromisoformat(comment['created_at'].replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"Body: {comment['body']}")
                
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    main()