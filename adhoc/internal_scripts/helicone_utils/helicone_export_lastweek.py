from gql.transport.aiohttp import AIOHTTPTransport
from gql import gql, Client
from datetime import datetime, timedelta
import csv
import re
import json
import time
import asyncio


from datetime import datetime, timedelta

# Get the current date and time
current_time = datetime.now()

# Calculate the date 30 days ago
thirty_days_ago = current_time - timedelta(days=60)

# Convert the date to a timestamp (assuming your API uses UNIX timestamps in milliseconds)
thirty_days_ago_timestamp = int(thirty_days_ago.timestamp() * 1000)



def retry_on_exception(func, max_retries=3, delay=5, allowed_exceptions=(Exception,)):
    """
    Retries a function in case of an exception.

    :param func: The function to execute.
    :param max_retries: The maximum number of times to retry.
    :param delay: The number of seconds to wait between retries.
    :param allowed_exceptions: Exceptions on which the function should be retried.
    :return: The result of the function if successful.
    """
    for i in range(max_retries):
        try:
            return func()
        except allowed_exceptions as e:
            if i < max_retries - 1:  # i is 0 indexed
                print(f"Error: {e}. Retrying in {delay} seconds...")
                time.sleep(delay)
            else:
                print(f"Failed after {max_retries} attempts.")
                raise


def extract_last_question(request_body):
    text = json.dumps(request_body)
    matches = re.findall(r"QUESTION: (.*?)\\n", text)
    if matches:
        return matches[-1]
    else:
        return ""


url = "https://www.helicone.ai/api/graphql"

# Replace <KEY> with your personal access key
transport = AIOHTTPTransport(
    url=url, headers={"Authorization": "Bearer sk-lkx72fq-f55e4li-teopcka-gspmpea"}
)

client = Client(transport=transport, fetch_schema_from_transport=True)

MAX_LOOPS = 100
SIZE = 100

# Create a list to store the filtered messages
filtered_messages = []
# Write the filtered messages to a CSV file
csv_file = "filtered_messages.csv"
fieldnames = [
    "createdAt",
    "namespace",
    "request",
    "response",
    "model",
    "app",
    
]

with open(csv_file, mode="w", newline="", encoding="utf-8") as file:
    writer = csv.DictWriter(file, fieldnames=fieldnames)
    writer.writeheader()

    for i in range(MAX_LOOPS):
        query = gql(
            """
        query getData($limit: Int, $offset: Int) {
            heliconeRequest(
                limit: $limit
                offset: $offset
                filters: [   
                  {
                        property: {
                            name: "Model",
                            value: {
                                contains: "gpt-4"
                            }
                        }
                    }    
            ]
            ) {
                requestBody                
                properties{                    
                    name
                    value
                }                
                responseBody
                response
                createdAt
            }
        }
    """
        )

        def fetch_data():
            return client.execute(
                query, variable_values={"limit": SIZE, "offset": i * SIZE}
            )

        # Use retry function to fetch data
        result = retry_on_exception(
            fetch_data,
            max_retries=3,
            delay=5,
            allowed_exceptions=(asyncio.exceptions.TimeoutError,),
        )

        if len(result["heliconeRequest"]) == 0:
            print("No more results")
            break

        # Get question from prompt and add to filtered messages
        for item in result["heliconeRequest"]:
            created_at = datetime.fromtimestamp(int(item["createdAt"]) / 1000)
            
            if created_at >= thirty_days_ago:
                namespace = next(
                    (
                        prop["value"]
                        for prop in item["properties"]
                        if prop["name"] == "namespace"
                    ),
                    None,
                )

                app = next(
                    (prop["value"] for prop in item["properties"] if prop["name"] == "app"),
                    None,
                )

                # Extract model from responseBody
                responseBody = item["responseBody"]
                model = responseBody.get("model", "")

                message = {
                    "createdAt": item["createdAt"],
                    "responseBody": item["responseBody"],
                    "response": item["response"],
                    "model": model,
                    "namespace": namespace,
                    "requestBody": item["requestBody"],
                    "app": app,
                }
                filtered_messages.append(message)

                response = ""  # Initialize to default value
                lastQuestion = ""  # Initialize to default value

                if message.get("response"):
                    try:
                        response_dict = json.loads(message["response"])
                        response = response_dict.get("content", "").replace("\n", "")
                    except json.JSONDecodeError:
                        # Handle the case where the response is not valid JSON
                        response = ""

                if message.get("requestBody"):
                    # get the last QUESTION:
                    lastQuestion = extract_last_question(message["requestBody"])

                writer.writerow(
                    {
                        "createdAt": datetime.fromtimestamp(
                            int(message["createdAt"]) / 1000
                        ),
                        "namespace": message.get("namespace", ""),
                        "request": lastQuestion,
                        "response": response,
                        "model": message.get("model", ""),
                        "app": message.get("app", ""),
                        
                    }
                )

    print(f"Filtered messages have been saved to {csv_file}.")

  
