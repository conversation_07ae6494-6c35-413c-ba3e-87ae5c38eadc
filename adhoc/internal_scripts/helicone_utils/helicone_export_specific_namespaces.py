from gql.transport.aiohttp import AIOHTTPTransport
from gql import gql, Client
from datetime import datetime, timedelta
import csv
import re
import json
import time
import asyncio
import sys

"""
To run over a list of namespaces, use the following to read from a file

while IFS= read -r namespace; do
    python helicone_export_specific_namespaces.py "$namespace"
done < namespaces.txt
"""

if len(sys.argv) != 2:
    print("Usage: python helicone_export_specific_namespaces.py <namespace>")
    sys.exit(1)

namespace_to_use = sys.argv[1].strip()

def retry_on_exception(func, max_retries=30, delay=15, allowed_exceptions=(Exception,)):
    """
    Retries a function in case of an exception.

    :param func: The function to execute.
    :param max_retries: The maximum number of times to retry.
    :param delay: The number of seconds to wait between retries.
    :param allowed_exceptions: Exceptions on which the function should be retried.
    :return: The result of the function if successful.
    """
    for i in range(max_retries):
        try:
            return func()
        except allowed_exceptions as e:
            if i < max_retries - 1:  # i is 0 indexed
                print(f"Error: {e}. Retrying in {delay} seconds...")
                time.sleep(delay)
            else:
                print(f"Failed after {max_retries} attempts.")
                raise


def extract_last_question(request_body):
    text = json.dumps(request_body)
    matches = re.findall(r"QUESTION: (.*?)\\n", text)
    if matches:
        return matches[-1]
    else:
        return ""


url = "https://www.helicone.ai/api/graphql"

# Replace <KEY> with your personal access key
transport = AIOHTTPTransport(
    url=url, headers={"Authorization": "Bearer sk-lkx72fq-f55e4li-teopcka-gspmpea"}
)

client = Client(transport=transport, fetch_schema_from_transport=True)

MAX_LOOPS = 10000
SIZE = 500

# Create a list to store the filtered messages
filtered_messages = []
# Write the filtered messages to a CSV file
csv_file = "filtered_messages.csv"
fieldnames = [
    "createdAt",
    "namespace",
    "app",
]

with open(csv_file, mode="a", newline="", encoding="utf-8") as file:
    writer = csv.DictWriter(file, fieldnames=fieldnames)
    # writer.writeheader()

    print(namespace_to_use)

    for i in range(MAX_LOOPS):
        query = gql(
            """
        query getData($limit: Int, $offset: Int) {
            heliconeRequest(
                limit: $limit
                offset: $offset
                filters: [
                    {
                    property: {
                        name: "app"
                        value: { 
                        equals:  "playground"
                        }
                    }
                    }
                ]
            ) {
                createdAt
                properties{
                    name
                    value
                }
            }
        }
    """
        )

        print(query)

        def fetch_data():
            return client.execute(
                query, variable_values={"limit": SIZE, "offset": i * SIZE}
            )

        # Use retry function to fetch data
        result = retry_on_exception(
            fetch_data,
            max_retries=30,
            delay=15,
            allowed_exceptions=(asyncio.exceptions.TimeoutError,),
        )

        if len(result["heliconeRequest"]) == 0:
            print("No more results")
            break

        # Get question from prompt and add to filtered messages
        for item in result["heliconeRequest"]:
            namespace = next(
                (
                    prop["value"]
                    for prop in item["properties"]
                    if prop["name"] == "namespace"
                ),
                None,
            )

            app = next(
                (prop["value"] for prop in item["properties"] if prop["name"] == "app"),
                None,
            )

            message = {
                "createdAt": item["createdAt"],
                "namespace": namespace,
                "app": app,
            }
            filtered_messages.append(message)

            writer.writerow(
                {
                    "createdAt": datetime.fromtimestamp(
                        int(message["createdAt"]) / 1000
                    ),
                    "namespace": message.get("namespace", ""),
                    "app": message.get("app", ""),
                }
            )

    print(f"Filtered messages have been saved to {csv_file}.")
