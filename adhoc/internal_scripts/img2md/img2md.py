import pytesseract
from PIL import Image
import argparse
import os

def convert_image_to_markdown(image_path):
    # Load the image
    img = Image.open(image_path)
    # Use pytesseract to extract text
    text = pytesseract.image_to_string(img)
    # Format the text as Markdown
    markdown = f"```\n{text}\n```"
    return markdown

def main():
    parser = argparse.ArgumentParser(description='Convert images to Markdown.')
    parser.add_argument('directory', type=str, help='Path to the directory containing PNG files')
    args = parser.parse_args()
    
    # Iterate over all PNG files in the specified directory
    for filename in os.listdir(args.directory):
        if filename.endswith('.png'):
            image_path = os.path.join(args.directory, filename)
            markdown_output = convert_image_to_markdown(image_path)
            # Save the Markdown output to a .md file with the same name
            md_filename = filename.replace('.png', '.md')
            with open(os.path.join(args.directory, md_filename), 'w') as md_file:
                md_file.write(markdown_output)
            print(f"Markdown for {filename} saved as {md_filename}.")

if __name__ == "__main__":
    main()
