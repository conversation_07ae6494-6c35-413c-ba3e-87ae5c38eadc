#!/usr/bin/env python3

import os
import sys
import base64
import requests
import argparse
import json
from pathlib import Path

def encode_image(image_path):
    """Encodes the image in base64."""
    try:
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    except FileNotFoundError:
        print(f"Error: File '{image_path}' not found.")
        return None
    except Exception as e:
        print(f"Error reading file '{image_path}': {e}")
        return None

def call_openai_api(base64_image, api_key):
    """Calls the OpenAI API with the encoded image."""
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }

    payload = {
        "model": "gpt-4o-mini",  # Replace with the correct model name if different
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": """
                            In extreme detail, translate the information in the image into English, formatted as Markdown.
                        """
                    },
                    {
                        "type": "image_url",
                        "image_url": { 
                            "url": f"data:image/png;base64,{base64_image}" 
                        } 
                    }
                ]
            }
        ],
        "max_tokens": 1000,
        "temperature": 0.3
    }

    try:
        response = requests.post("https://api.openai.com/v1/chat/completions", headers=headers, json=payload)
        response.raise_for_status()
    except requests.exceptions.HTTPError as http_err:
        print(f"HTTP error occurred: {http_err}")
        print(f"Response: {response.text}")
        return None
    except Exception as err:
        print(f"Other error occurred: {err}")
        return None

    return response.json()

def extract_markdown_text(api_response):
    """Extracts the Markdown text from the API response."""
    try:
        return api_response['choices'][0]['message']['content'].strip()
    except (KeyError, IndexError) as e:
        print("Error parsing API response. Please ensure the API call was successful.")
        print(json.dumps(api_response, indent=2))
        return None

def process_image(image_path, api_key):
    """Processes a single image: encodes, calls API, and saves Markdown."""
    print(f"Processing '{image_path}'...")
    base64_image = encode_image(image_path)
    if not base64_image:
        print(f"Skipping '{image_path}' due to encoding error.\n")
        return

    api_response = call_openai_api(base64_image, api_key)
    if not api_response:
        print(f"Skipping '{image_path}' due to API error.\n")
        return

    markdown_text = extract_markdown_text(api_response)
    if markdown_text is None:
        print(f"Skipping '{image_path}' due to extraction error.\n")
        return

    # Define the Markdown file path
    image_path_obj = Path(image_path)
    markdown_path = image_path_obj.with_suffix('.md')

    try:
        with open(markdown_path, 'w', encoding='utf-8') as md_file:
            md_file.write(markdown_text)
        print(f"Saved Markdown to '{markdown_path}'.\n")
    except Exception as e:
        print(f"Error writing Markdown file '{markdown_path}': {e}\n")

def main():
    parser = argparse.ArgumentParser(description="Batch extract text from PNG images in a directory and save as Markdown using OpenAI's vision models.")
    parser.add_argument("directory", help="Path to the directory containing PNG image files.")
    parser.add_argument("-r", "--recursive", action="store_true", help="Recursively process PNG files in all subdirectories.")
    args = parser.parse_args()

    directory = args.directory

    if not os.path.isdir(directory):
        print(f"Error: Directory '{directory}' does not exist or is not a directory.")
        sys.exit(1)

    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("Error: OPENAI_API_KEY environment variable is not set.")
        sys.exit(1)

    # Gather all PNG files
    if args.recursive:
        png_files = list(Path(directory).rglob("*.png"))
    else:
        png_files = list(Path(directory).glob("*.png"))

    if not png_files:
        print(f"No PNG files found in directory '{directory}'.")
        sys.exit(0)

    print(f"Found {len(png_files)} PNG file(s) in '{directory}'.\n")

    for png_file in png_files:
        process_image(str(png_file), api_key)

    print("Processing completed.")

if __name__ == "__main__":
    main()
