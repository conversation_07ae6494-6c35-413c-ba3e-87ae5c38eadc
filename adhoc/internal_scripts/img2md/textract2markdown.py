import boto3
import sys
import os
from botocore.exceptions import NoCredentialsError, NoRegionError
from operator import itemgetter
import logging
import json
from PIL import Image, ImageDraw, ImageEnhance, ImageFilter
import matplotlib.pyplot as plt
import io

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def get_textract_client(profile_name='eeselAiDeployer', region_name='us-east-1'):
    try:
        session = boto3.Session(profile_name=profile_name)
        client = session.client('textract', region_name=region_name)
        logging.info(f"Textract client initialized with profile '{profile_name}' and region '{region_name}'.")
        return client
    except NoCredentialsError:
        logging.error("AWS credentials not found. Ensure they are set up properly.")
        sys.exit(1)
    except NoRegionError:
        logging.error("Region not specified. Ensure the region is configured.")
        sys.exit(1)
    except Exception as e:
        logging.error(f"An error occurred while initializing Textract client: {e}")
        sys.exit(1)

def preprocess_image_heading(image_path):
    """
    Preprocesses the image to optimize Textract for extracting headings.
    Steps:
    - Convert to grayscale
    - Increase contrast
    - Sharpen the image
    - Resize to emphasize larger text
    """
    try:
        image = Image.open(image_path)
        image = image.convert('L')  # Convert to grayscale
        enhancer = ImageEnhance.Contrast(image)
        image = enhancer.enhance(2)  # Increase contrast
        image = image.filter(ImageFilter.SHARPEN)  # Sharpen image
        # Resize to double the size to emphasize larger text
        new_size = (image.width * 2, image.height * 2)
        image = image.resize(new_size, Image.Resampling.LANCZOS)  # Use LANCZOS for resizing
        # Save to bytes
        img_byte_arr = io.BytesIO()
        image.save(img_byte_arr, format='PNG')
        preprocessed_bytes = img_byte_arr.getvalue()
        logging.info("Image preprocessing for headings completed.")
        return preprocessed_bytes
    except Exception as e:
        logging.error(f"Failed to preprocess image for headings: {e}")
        sys.exit(1)

def preprocess_image_body(image_path):
    """
    Preprocesses the image to optimize Textract for extracting body text.
    Steps:
    - Convert to grayscale
    - Apply contrast and sharpness optimizations
    """
    try:
        image = Image.open(image_path)
        image = image.convert('L')  # Convert to grayscale
        # Apply contrast enhancements for low contrast text
        enhancer = ImageEnhance.Contrast(image)
        image = enhancer.enhance(1.5)
        # Sharpen the image
        image = image.filter(ImageFilter.SHARPEN)
        # Save to bytes
        img_byte_arr = io.BytesIO()
        image.save(img_byte_arr, format='PNG')
        preprocessed_bytes = img_byte_arr.getvalue()
        logging.info("Image preprocessing for body text completed.")
        return preprocessed_bytes
    except Exception as e:
        logging.error(f"Failed to preprocess image for body text: {e}")
        sys.exit(1)

def read_image_bytes(image_path, preprocess_method='heading'):
    """
    Reads the image file, preprocesses it, and returns its bytes.
    :param image_path: Path to the image file.
    :param preprocess_method: 'heading' or 'body' indicating the preprocessing to apply.
    :return: Preprocessed image bytes.
    """
    if not os.path.isfile(image_path):
        logging.error(f"Image file '{image_path}' does not exist.")
        sys.exit(1)
    
    if preprocess_method == 'heading':
        image_bytes = preprocess_image_heading(image_path)
    elif preprocess_method == 'body':
        image_bytes = preprocess_image_body(image_path)
    else:
        logging.error("Unsupported preprocessing method. Choose 'heading' or 'body'.")
        sys.exit(1)
    
    logging.info(f"Read and preprocessed image for '{preprocess_method}' ({len(image_bytes)} bytes).")
    return image_bytes

def analyze_document(client, image_bytes):
    """
    Calls Textract's AnalyzeDocument API to extract structured data.
    """
    try:
        response = client.analyze_document(
            Document={'Bytes': image_bytes},
            FeatureTypes=["TABLES", "FORMS"]
        )
        logging.info("Document analysis (AnalyzeDocument) completed.")
        return response
    except Exception as e:
        logging.error(f"An error occurred during document analysis: {e}")
        sys.exit(1)

def detect_document_text(client, image_bytes):
    """
    Calls Textract's DetectDocumentText API to extract raw text.
    """
    try:
        response = client.detect_document_text(Document={'Bytes': image_bytes})
        logging.info("Document text detection (DetectDocumentText) completed.")
        return response
    except Exception as e:
        logging.error(f"An error occurred during text detection: {e}")
        sys.exit(1)

def save_textract_response(response, output_json_path):
    """
    Saves the full Textract response to a JSON file for inspection.
    """
    try:
        with open(output_json_path, 'w', encoding='utf-8') as json_file:
            json.dump(response, json_file, indent=2)
        logging.info(f"Textract response saved to '{output_json_path}'.")
    except Exception as e:
        logging.error(f"Failed to save Textract response: {e}")

def convert_textract_to_markdown(response):
    """
    Converts Textract response to Markdown format, preserving tables and full text.
    """
    blocks = response['Blocks']
    logging.info(f"Total blocks found: {len(blocks)}.")

    lines = []
    for block in blocks:
        if block['BlockType'] == 'LINE':
            lines.append(block['Text'])

    full_text = "\n".join(lines)
    logging.info(f"Extracted {len(lines)} lines of text.")
    return full_text

def save_markdown(markdown_text, output_path):
    """
    Saves the Markdown text to a file.
    """
    try:
        with open(output_path, 'w', encoding='utf-8') as md_file:
            md_file.write(markdown_text)
        logging.info(f"Markdown saved to '{output_path}'.")
    except Exception as e:
        logging.error(f"Failed to save Markdown file: {e}")
        sys.exit(1)

def main(image_path, output_markdown_path, response_json_path_heading, response_json_path_body, visualized_image_path_heading, visualized_image_path_body, profile_name='eeselAiDeployer', region_name='us-east-1'):
    client = get_textract_client(profile_name, region_name)
    
    # Pass 1: Extract Headings
    image_bytes_heading = read_image_bytes(image_path, preprocess_method='heading')
    response_heading = analyze_document(client, image_bytes_heading)
    save_textract_response(response_heading, response_json_path_heading)
    markdown_heading = convert_textract_to_markdown(response_heading)
    save_markdown(markdown_heading, output_markdown_path + '_headings.md')
    
    # Pass 2: Extract Body Text
    image_bytes_body = read_image_bytes(image_path, preprocess_method='body')
    response_body = detect_document_text(client, image_bytes_body)
    save_textract_response(response_body, response_json_path_body)
    markdown_body = convert_textract_to_markdown(response_body)
    save_markdown(markdown_body, output_markdown_path + '_body.md')
    
    # Combine Headings and Body into Final Markdown
    final_markdown = ""
    if os.path.isfile(output_markdown_path + '_headings.md'):
        with open(output_markdown_path + '_headings.md', 'r', encoding='utf-8') as f:
            final_markdown += f.read() + "\n\n"
    if os.path.isfile(output_markdown_path + '_body.md'):
        with open(output_markdown_path + '_body.md', 'r', encoding='utf-8') as f:
            final_markdown += f.read()
    if final_markdown:
        with open(output_markdown_path, 'w', encoding='utf-8') as f:
            f.write(final_markdown)
        logging.info(f"Final combined Markdown saved to '{output_markdown_path}'.")
    
    logging.info("Process completed successfully.")

if __name__ == "__main__":
    if len(sys.argv) != 7:
        print("Usage: python textract_multi_pass.py <image_path> <output_markdown_path> <response_json_path_heading> <response_json_path_body> <visualized_image_path_heading> <visualized_image_path_body>")
        sys.exit(1)
    image_path = sys.argv[1]
    output_markdown_path = sys.argv[2]
    response_json_path_heading = sys.argv[3]
    response_json_path_body = sys.argv[4]
    visualized_image_path_heading = sys.argv[5]
    visualized_image_path_body = sys.argv[6]
    main(image_path, output_markdown_path, response_json_path_heading, response_json_path_body, visualized_image_path_heading, visualized_image_path_body)
