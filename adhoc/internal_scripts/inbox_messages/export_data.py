import boto3
import json
import time
import logging
from botocore.exceptions import ClientError

def truncate_string(s, max_length=100):
    return s[:max_length]

def export_dynamodb_to_json():
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)

    logger.info("Starting DynamoDB export process")
    
    # Initialize a session using the 'eeselAiDeployer' profile
    logger.info("Initializing AWS session with eeselAiDeployer profile")
    session = boto3.Session(profile_name='eeselAiDeployer', region_name='eu-west-1')
    dynamodb = session.resource('dynamodb')
    table = dynamodb.Table('eesel_ai_messages')
    
    # Add debug logging for table info
    logger.info("Table Information:")
    logger.info(f"Table Name: {table.name}")
    try:
        table_description = table.meta.client.describe_table(TableName='eesel_ai_messages')
        logger.info("Global Secondary Indexes:")
        for gsi in table_description['Table'].get('GlobalSecondaryIndexes', []):
            logger.info(f"  Index Name: {gsi['IndexName']}")
            logger.info(f"  Key Schema: {gsi['KeySchema']}")
    except ClientError as e:
        logger.error(f"Error getting table description: {e}")

    # Parameters for the query with debug logging
    index_name = 'namespaceId-receivedAt-index'
    namespace_id = '528ac45d-3d87-48b3-aeae-d5e74ecf3ff2'
    
    logger.info(f"Querying with:")
    logger.info(f"  Index Name: {index_name}")
    logger.info(f"  Namespace ID: {namespace_id}")
    
    query_kwargs = {
        'IndexName': index_name,
        'KeyConditionExpression': 'namespaceId = :namespace_id',
        'ExpressionAttributeValues': {
            ':namespace_id': namespace_id
        }
    }
    
    logger.info(f"Query parameters: {json.dumps(query_kwargs, default=str, indent=2)}")
    
    done = False
    start_key = None
    items = []
    batch_count = 0

    logger.info("Beginning query of DynamoDB table")
    while not done:
        if start_key:
            logger.debug(f"Continuing query with start key: {start_key}")
            query_kwargs['ExclusiveStartKey'] = start_key
        
        max_retries = 5
        retry_count = 0
        while retry_count < max_retries:
            try:
                logger.info("Executing query...")
                response = table.query(**query_kwargs)
                batch_items = response.get('Items', [])
                
                # Truncate message fields in each item
                for item in batch_items:
                    if 'agentMessage' in item:
                        item['agentMessage'] = truncate_string(item['agentMessage'])
                    if 'userMessage' in item:
                        item['userMessage'] = truncate_string(item['userMessage'])
                
                items.extend(batch_items)
                batch_count += 1
                
                logger.info(f"Batch {batch_count}: Retrieved {len(batch_items)} items. Total items so far: {len(items)}")
                
                start_key = response.get('LastEvaluatedKey', None)
                done = start_key is None
                break  # Success, exit retry loop
                
            except ClientError as e:
                if e.response['Error']['Code'] == 'ProvisionedThroughputExceededException':
                    retry_count += 1
                    if retry_count < max_retries:
                        wait_time = (2 ** retry_count) * 0.1  # exponential backoff
                        logger.warning(f"Throughput exceeded, waiting {wait_time:.2f} seconds before retry {retry_count}/{max_retries}")
                        time.sleep(wait_time)
                    else:
                        logger.error(f"Max retries ({max_retries}) reached. Exiting.")
                        raise
                else:
                    logger.error(f"An error occurred: {e.response['Error']['Message']}")
                    raise
                
        time.sleep(0.5)  # Increased base delay between successful requests

    logger.info(f"Query completed. Writing {len(items)} items to export.json")
    
    # Write items to export.json
    try:
        with open('export.json', 'w') as f:
            json.dump(items, f, default=str, indent=4)
        logger.info("Successfully wrote data to export.json")
    except Exception as e:
        logger.error(f"Error writing to file: {str(e)}")

if __name__ == "__main__":
    export_dynamodb_to_json() 