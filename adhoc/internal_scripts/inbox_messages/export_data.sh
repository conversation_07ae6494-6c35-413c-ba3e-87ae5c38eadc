#!/bin/bash

TABLE_NAME="eesel_ai_messages"
OUTPUT_FILE="inbox_export.json"

# Initialize the output file with an opening bracket
echo "[" > $OUTPUT_FILE

# Function to implement exponential backoff
scan_with_retry() {
    local max_attempts=5
    local attempt=1
    local wait_time=1

    while [ $attempt -le $max_attempts ]; do
        if [ -n "$1" ]; then
            # With LastEvaluatedKey
            result=$(aws dynamodb query \
                --profile eeselAiDeployer \
                --table-name $TABLE_NAME \
                --index-name namespaceId-receivedAt-index \
                --key-condition-expression "namespaceId = :nsid" \
                --expression-attribute-values '{":nsid": {"S": "528ac45d-3d87-48b3-aeae-d5e74ecf3ff2"}}' \
                --exclusive-start-key "$1" \
                --max-items 100 \
                --consistent-read false 2>&1)
        else
            # Initial query
            result=$(aws dynamodb query \
                --profile eesel<PERSON>iDeployer \
                --table-name $TABLE_NAME \
                --index-name namespaceId-receivedAt-index \
                --key-condition-expression "namespaceId = :nsid" \
                --expression-attribute-values '{":nsid": {"S": "528ac45d-3d87-48b3-aeae-d5e74ecf3ff2"}}' \
                --max-items 100 \
                --consistent-read false 2>&1)
        fi

        if [[ $result == *"ProvisionedThroughputExceededException"* ]]; then
            echo "Rate limit hit, waiting $wait_time seconds..."
            sleep $wait_time
            wait_time=$((wait_time * 2))
            attempt=$((attempt + 1))
        else
            echo "$result"
            return 0
        fi
    done
    echo "Failed after $max_attempts attempts"
    return 1
}

# Initialize counter for logging
total_items=0

# Perform initial scan
last_key=""
first_item=true

while true; do
    echo "Fetching next batch of items..."
    if [ -z "$last_key" ]; then
        result=$(scan_with_retry)
    else
        result=$(scan_with_retry "$last_key")
    fi

    # Add debug logging
    echo "Debug: Raw result:"
    echo "$result"

    # Validate JSON before processing
    if ! echo "$result" | jq '.' >/dev/null 2>&1; then
        echo "Error: Invalid JSON received from DynamoDB"
        echo "Raw response: $result"
        exit 1
    fi

    # Extract items and append to file
    items=$(echo "$result" | jq -r '.Items | tostring')
    if [ "$items" != "null" ] && [ "$items" != "" ]; then
        batch_count=0
        echo "$result" | jq -c '.Items[]' | while IFS= read -r item; do
            if [ -n "$item" ]; then
                if [ "$first_item" = true ]; then
                    first_item=false
                else
                    echo "," >> $OUTPUT_FILE
                fi
                echo "$item" >> $OUTPUT_FILE
                batch_count=$((batch_count + 1))
            fi
        done
        total_items=$((total_items + batch_count))
        echo "Processed batch of $batch_count items. Total items so far: $total_items"
    else
        echo "Warning: No items found in this batch"
    fi

    # Check for more items
    last_key=$(echo "$result" | jq -r '.LastEvaluatedKey | tostring')
    
    if [ "$last_key" = "null" ]; then
        echo "No more items to fetch. Scan complete."
        break
    fi

    echo "Found LastEvaluatedKey, continuing to next batch..."
    # Optional: Add a small delay between scans
    sleep 0.5
done

# Close the JSON array
echo "]" >> $OUTPUT_FILE

echo "Export completed to $OUTPUT_FILE with $total_items total items"