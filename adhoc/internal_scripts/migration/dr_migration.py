import pinecone
import os
import itertools

# Initialize Pinecone clients with environment-specific API keys
source_api_key = os.environ.get('SOURCE_PINECONE_API_KEY')
dest_api_key = os.environ.get('DEST_PINECONE_API_KEY')

if not source_api_key or not dest_api_key:
    raise ValueError("SOURCE_PINECONE_API_KEY and/or DEST_PINECONE_API_KEY environment variables not set")

# Define source and destination parameters
source_index_name = os.environ.get('SOURCE_INDEX_NAME', 'source-index')
destination_index_name = os.environ.get('DEST_INDEX_NAME', 'destination-index')
source_environment = os.environ.get('SOURCE_ENVIRONMENT')
dest_environment = os.environ.get('DEST_ENVIRONMENT')
batch_size = 1000                         # Number of vectors to process per batch

# Define the specific namespaces to migrate
target_namespaces = [
    # '56faffa0-4164-4b44-9b7b-02a3cab1e1ed_eesel'
    'd64092ee-ec62-4e3e-95d4-b94bc0b8ff82_eesel',
    '78aacd34-c31e-45bb-bfe6-8969cd955d76_eesel', 
    '71187c26-6301-48b7-ab18-8e324b6017b3_eesel',
    '05c8e67b-d8d3-4909-b69e-351dfe3b45c6_eesel',
    'c8a7ca8a-0c2e-43aa-8ee6-120e82fa17d2_eesel',
    '5c89d900-6cf0-42ef-ae4a-22922ecac3b9_eesel',
    '36aa5096-20eb-4507-80e0-8a87b1d48cf7_eesel',
    'f693c005-bd91-433d-8700-a65008b623bf_eesel',
    '6c2d3e44-1704-4c1b-9017-fb0c6ae213da_eesel',
    'da2f1b61-14ab-450b-8ec0-b60fa55f0367_eesel',
    '56faffa0-4164-4b44-9b7b-02a3cab1e1ed_eesel',
    'd8df219b-ae45-4ec3-a834-a46c2ccb9a95_eesel',
    '50d1f7fc-d0a9-4d70-91c1-f89d4e740d77_eesel',
    'f088eead-83bb-4e15-9a16-cecd46e338c4_eesel',
    'f4f33790-e09d-40dd-b48a-7eff01b50c87_eesel',
    'f082cd88-b6a1-4ce8-9e25-e2c0ce2ad0dc_eesel'
]

# Create separate Pinecone instances for source and destination
source_pc = pinecone.Pinecone(
    api_key=source_api_key,
    environment=source_environment
)
dest_pc = pinecone.Pinecone(
    api_key=dest_api_key,
    environment=dest_environment
)

# Create Index objects
source_index = source_pc.Index(source_index_name)
destination_index = dest_pc.Index(destination_index_name)

# Get all namespaces from source index
stats = source_index.describe_index_stats()
available_namespaces = stats['namespaces'].keys()
print(f"Found {len(available_namespaces)} total namespaces")

# Validate target namespaces exist in source
invalid_namespaces = [ns for ns in target_namespaces if ns not in available_namespaces]
if invalid_namespaces:
    print(f"\nThe following namespaces will be skipped (not found in source index):")
    for ns in invalid_namespaces:
        print(f"- {ns}")
    # Filter out invalid namespaces from target list
    target_namespaces = [ns for ns in target_namespaces if ns in available_namespaces]

print("\nMigration Summary:")
print("-" * 80)
print(f"{'Namespace':<50} | {'Vector Count':>10}")
print("-" * 80)
for namespace in target_namespaces:
    vector_count = stats['namespaces'][namespace]['vector_count']
    print(f"{namespace:<50} | {vector_count:>10,}")
print("-" * 80)
total_vectors = sum(stats['namespaces'][ns]['vector_count'] for ns in target_namespaces)
print(f"{'Total':<50} | {total_vectors:>10,}")
print()

# Make dry run the default mode
dry_run = os.environ.get('EXECUTE_MIGRATION', 'false').lower() != 'true'
if dry_run:
    print("*** DRY RUN MODE - No actual migrations will be performed ***")
    print("Set EXECUTE_MIGRATION=true to perform actual migration")
    
    # Verify connectivity to both indexes
    print("\nVerifying connectivity to source index...")
    source_stats = source_index.describe_index_stats()
    print(f"Successfully connected to source index '{source_index_name}'")
    
    print("\nVerifying connectivity to destination index...")
    dest_stats = destination_index.describe_index_stats()
    print(f"Successfully connected to destination index '{destination_index_name}'")
    print("\nConnectivity verification completed successfully!\n")
else:
    print("*** EXECUTING ACTUAL MIGRATION ***\n")

def estimate_vector_size(vector_tuple):
    """Estimate the size of a vector tuple in bytes"""
    id_size = len(vector_tuple[0].encode('utf-8'))  # ID string size
    values_size = len(vector_tuple[1]) * 4  # Float32 array size
    metadata_size = len(str(vector_tuple[2]).encode('utf-8'))  # Metadata size
    return id_size + values_size + metadata_size

def calculate_optimal_chunk_size(vectors, max_message_size=2_194_304):
    """Calculate optimal chunk size based on average vector size"""
    initial_chunk_size = 250  # Default chunk size if no vectors to analyze
    
    if not vectors:
        return initial_chunk_size
    
    # Sample up to 10 vectors to get average size
    sample_size = min(10, len(vectors))
    sample_vectors = vectors[:sample_size]
    avg_vector_size = sum(estimate_vector_size(v) for v in sample_vectors) / sample_size
    
    # Add 20% buffer for JSON overhead and safety margin
    optimal_size = int((max_message_size * 0.8) / avg_vector_size)
    return max(1, min(optimal_size, 500))  # Cap at 500 vectors per chunk

def get_dummy_vector(dimension):
    """Create a dummy vector of zeros with the specified dimension"""
    return [0] * dimension

# Process each namespace
for namespace in target_namespaces:
    total_vectors = stats['namespaces'][namespace]['vector_count']
    print(f"\nMigrating namespace '{namespace}' from {source_index_name} to {destination_index_name}")
    print(f"Total vectors to migrate: {total_vectors}")

    if total_vectors > 0:
        # Verify connectivity and check destination namespace
        print("\nVerifying connectivity and checking destination namespace...")
        source_stats = source_index.describe_index_stats()
        dest_stats = destination_index.describe_index_stats()
        print(f"Successfully connected to both indexes")
        
        # Get index dimension for dummy vector
        dimension = source_index.describe_index_stats()['dimension']
        dummy_vector = get_dummy_vector(dimension)
        
        dest_namespace_stats = destination_index.describe_index_stats()
        existing_vectors = dest_namespace_stats['namespaces'].get(namespace, {'vector_count': 0})['vector_count']
        if existing_vectors > 0:
            print(f"Found {existing_vectors} existing vectors in destination namespace '{namespace}'")
            if not dry_run:
                print(f"Cleaning up destination namespace '{namespace}'...")
                # Use delete_all instead of list_vectors + delete
                destination_index.delete(
                    namespace=namespace,
                    delete_all=True
                )
                print(f"Cleanup completed for namespace '{namespace}'")
            else:
                print(f"[DRY RUN] Would delete {existing_vectors} vectors from destination namespace '{namespace}'")

        migrated_count = 0
        processed_ids = set()
        
        while migrated_count < total_vectors:
            # Query for next batch of vectors (keep at 10000 to list them all)
            query_response = source_index.query(
                namespace=namespace,
                vector=dummy_vector,
                top_k=10000,
                include_values=False,
                include_metadata=False
            )
            
            # Get IDs from query response and print count
            print(f"Found {len(query_response.matches)} vectors in query response")
            batch_ids = [match.id for match in query_response.matches 
                        if match.id not in processed_ids]
            
            if not batch_ids:
                break
                
            # Use fixed chunk size of 250 vectors
            chunk_size = 250
            print(f"Using fixed chunk size: {chunk_size} vectors")
            
            # Process vectors in chunks to avoid fetching all at once
            for i in range(0, len(batch_ids), chunk_size):
                chunk_ids = batch_ids[i:i + chunk_size]
                
                # Fetch vector data for this chunk of IDs
                fetch_response = source_index.fetch(
                    ids=chunk_ids,
                    namespace=namespace
                )
                
                # Prepare vectors for upsert
                upsert_vectors = [
                    (
                        id,
                        vector_data.values,
                        vector_data.metadata if hasattr(vector_data, 'metadata') else {}
                    )
                    for id, vector_data in fetch_response.vectors.items()
                ]

                if not dry_run:
                    try:
                        destination_index.upsert(vectors=upsert_vectors, namespace=namespace)
                        migrated_count += len(upsert_vectors)
                        print(f"Migrated {migrated_count} / {total_vectors} vectors")
                    except Exception as e:
                        print(f"Error during chunk upsert, falling back to single vector mode: {str(e)}")
                        # Fall back to single vector processing for this chunk
                        for vector in upsert_vectors:
                            try:
                                destination_index.upsert(vectors=[vector], namespace=namespace)
                                migrated_count += 1
                                print(f"Migrated {migrated_count} / {total_vectors} vectors (single vector mode)")
                            except Exception as e:
                                print(f"Failed to migrate vector {vector[0]}: {str(e)}")
                else:
                    print(f"[DRY RUN] Would migrate batch of {len(upsert_vectors)} vectors")
                
                # Track processed IDs
                processed_ids.update(chunk_ids)

        # Verify migration for this namespace
        destination_stats = destination_index.describe_index_stats()
        destination_count = destination_stats['namespaces'].get(namespace, 
                                                              {'vector_count': 0})['vector_count']
        print(f"Namespace '{namespace}' now contains {destination_count} vectors")
        if destination_count == total_vectors:
            print(f"Migration successful for namespace '{namespace}': Vector counts match")
        else:
            print(f"Migration incomplete for namespace '{namespace}': "
                  f"Expected {total_vectors}, got {destination_count}")
    else:
        print(f"No vectors to migrate in namespace '{namespace}'")

print("\nMigration process completed for all namespaces")

# Update the final integrity check section
print("\nPerforming final integrity check...")
source_stats = source_index.describe_index_stats()
dest_stats = destination_index.describe_index_stats()

integrity_issues = []
for namespace in target_namespaces:
    source_count = source_stats['namespaces'].get(namespace, {'vector_count': 0})['vector_count']
    dest_count = dest_stats['namespaces'].get(namespace, {'vector_count': 0})['vector_count']
    
    print(f"\nChecking namespace '{namespace}':")
    print(f"Source vectors: {source_count}")
    print(f"Destination vectors: {dest_count}")
    
    if source_count != dest_count:
        integrity_issues.append(f"Count mismatch in namespace '{namespace}': "
                              f"Source has {source_count}, Destination has {dest_count}")
    elif source_count > 0:  # Only check contents if there are vectors to compare
        # Sample a few random vectors using query instead of list_ids
        results = source_index.query(
            namespace=namespace,
            vector=[0] * source_index.describe_index_stats()['dimension'],  # dummy vector
            top_k=5,
            include_values=True,
            include_metadata=True
        )
        sample_ids = [match.id for match in results.matches]
        
        if sample_ids:
            source_vectors = source_index.fetch(ids=sample_ids, namespace=namespace)
            dest_vectors = destination_index.fetch(ids=sample_ids, namespace=namespace)
            
            for id in sample_ids:
                if id not in dest_vectors.vectors:
                    integrity_issues.append(f"Vector {id} missing in destination namespace '{namespace}'")
                elif source_vectors.vectors[id].values != dest_vectors.vectors[id].values:
                    integrity_issues.append(f"Vector {id} content mismatch in namespace '{namespace}'")

if integrity_issues:
    print("\n⚠️ Integrity check failed with the following issues:")
    for issue in integrity_issues:
        print(f"- {issue}")
else:
    print("\n✅ Integrity check passed! All namespaces were migrated successfully.")