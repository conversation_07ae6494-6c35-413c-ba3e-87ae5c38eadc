#!/usr/bin/env python3
import os
import sys
from datetime import datetime, timedelta, timezone
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor

import matplotlib.pyplot as plt
from github import Github, GithubException

# ─── CONFIG ────────────────────────────────────────────────────────────────────
GITHUB_TOKEN = os.getenv("GITHUB_TOKEN")
REPO_NAME    = "eeselapp/slack"   # ← your repo: "owner/repo"
LOOKBACK_DAYS= 30
OUTLIER_DAYS =  3
# Tune number of workers for parallel fetch of commit stats
MAX_WORKERS  = 10
# ───────────────────────────────────────────────────────────────────────────────

def business_seconds(start: datetime, end: datetime) -> float:
    """
    Sum up only the seconds that fall on weekdays between start and end.
    """
    total = 0.0
    current = start
    one_day = timedelta(days=1)
    while current < end:
        next_midnight = (current.replace(hour=0, minute=0, second=0, microsecond=0)
                         + one_day)
        segment_end = min(end, next_midnight)
        if current.weekday() < 5:
            total += (segment_end - current).total_seconds()
        current = segment_end
    return total


def plot_commits_by_hour(commits, max_workers=MAX_WORKERS):
    """
    Generates plots of commits and LOC by hour for each contributor.
    Pulls commit.stats in parallel to speed up.
    """
    AEST = timezone(timedelta(hours=10))
    commit_counts = defaultdict(lambda: defaultdict(int))  # author -> hour -> count
    loc_counts = defaultdict(lambda: defaultdict(int))     # author -> hour -> LOC

    def process_commit(c):
        # Determine author
        author = c.author.login if c.author else c.commit.author.name
        # Convert commit time to AEST hour
        dt_utc = c.commit.author.date.replace(tzinfo=timezone.utc)
        hour = dt_utc.astimezone(AEST).hour
        # Fetch stats (network call)
        stats = c.stats
        loc = stats.additions + stats.deletions
        return author, hour, loc

    # Parallel processing
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        for author, hour, loc in executor.map(process_commit, commits):
            commit_counts[author][hour] += 1
            loc_counts[author][hour] += loc

    hours = list(range(24))

    # Plot commits per hour by author
    plt.figure()
    for author, hours_dict in commit_counts.items():
        counts = [hours_dict.get(h, 0) for h in hours]
        plt.plot(hours, counts, label=author)
    plt.title('Commits by Hour of Day (AEST)')
    plt.xlabel('Hour of Day')
    plt.ylabel('Number of Commits')
    plt.xticks(hours)
    plt.legend()
    plt.tight_layout()
    plt.show()

    # Plot LOC changed per hour by author
    plt.figure()
    for author, hours_dict in loc_counts.items():
        locs = [hours_dict.get(h, 0) for h in hours]
        plt.plot(hours, locs, label=author)
    plt.title('Lines of Code Changed by Hour of Day (AEST)')
    plt.xlabel('Hour of Day')
    plt.ylabel('Lines of Code Changed')
    plt.xticks(hours)
    plt.legend()
    plt.tight_layout()
    plt.show()

    while True:
        pass


def main():
    if not GITHUB_TOKEN:
        print("❌  Please set $GITHUB_TOKEN (finely-grained token with repo/issues/metadata read).")
        sys.exit(1)
    gh = Github(GITHUB_TOKEN)

    # verify repo access
    try:
        repo = gh.get_repo(REPO_NAME)
    except GithubException.UnknownObjectException:
        print(f"❌  Repo '{REPO_NAME}' not found or access denied.")
        sys.exit(1)

    cutoff = (datetime.now(timezone.utc) - timedelta(days=LOOKBACK_DAYS)).date()
    query  = f"repo:{REPO_NAME} is:pr is:merged closed:>={cutoff}"

    try:
        issues = gh.search_issues(query, sort="created", order="desc")
    except GithubException as e:
        print("❌  Search failed:", e)
        sys.exit(1)

    results = []
    for issue in issues:
        opened    = issue.created_at
        merged    = issue.closed_at
        secs      = business_seconds(opened, merged)
        days      = secs / 86400.0
        comments  = issue.comments
        author_pr = issue.user.login

        results.append({
            "number":     issue.number,
            "title":      issue.title.strip(),
            "opened":     opened,
            "merged":     merged,
            "days":       days,
            "comments":   comments,
            "author":     author_pr,
            "url":        issue.html_url,
            "is_outlier": days >= OUTLIER_DAYS
        })

    # DETAIL TABLE
    hdr = f"{'PR':<6}{'Opened':<20}{'Merged':<20}{'Days':<6}{'Comments':<9}{'Author':<15}{'Outlier'}"
    print(hdr)
    print("-" * len(hdr))
    for r in results:
        flag = "✅" if r["is_outlier"] else ""
        print(
            f"#{r['number']:<5} "
            f"{r['opened'].strftime('%Y-%m-%d %H:%M'): <20} "
            f"{r['merged'].strftime('%Y-%m-%d %H:%M'): <20} "
            f"{r['days']:<6.2f} "
            f"{r['comments']:<9} "
            f"{r['author']:<15} "
            f"{flag}"
        )

    # OUTLIERS
    outs = [r for r in results if r['is_outlier']]
    if outs:
        print("\nOutliers (≥ 3 business days):")
        for o in outs:
            print(
                f" • PR #{o['number']} ({o['title']}) by {o['author']} – "
                f"{o['days']:.2f} days, {o['comments']} comments "
                f"({o['opened'].date()} → {o['merged'].date()})\n"  
                f"   {o['url']}"
            )
    else:
        print("\nNo outliers (≥ 3 business days) in the last 30 days.")

    # SUMMARY STATISTICS
    buckets = {"< 1d": 0, "< 2d": 0, "< 3d": 0, ">= 3d": 0}
    for r in results:
        d = r['days']
        if d < 1:
            buckets['< 1d'] += 1
        elif d < 2:
            buckets['< 2d'] += 1
        elif d < 3:
            buckets['< 3d'] += 1
        else:
            buckets['>= 3d'] += 1

    print("\nSummary of PR review times (business days):")
    for label, count in buckets.items():
        pct = (count / len(results) * 100) if results else 0
        print(f" {label:<5}: {count:>3} ({pct:>5.1f}%)")

    # SUMMARY BY AUTHOR
    by_author = defaultdict(list)
    for r in results:
        by_author[r['author']].append(r)

    print("\nSummary of PR review times by author:")
    for author, prs in by_author.items():
        author_buckets = {"< 1d": 0, "< 2d": 0, "< 3d": 0, ">= 3d": 0}
        for pr in prs:
            d = pr['days']
            if d < 1:
                author_buckets['< 1d'] += 1
            elif d < 2:
                author_buckets['< 2d'] += 1
            elif d < 3:
                author_buckets['< 3d'] += 1
            else:
                author_buckets['>= 3d'] += 1
        print(f"\n{author} (Total PRs: {len(prs)})")
        for label, count in author_buckets.items():
            pct = (count / len(prs) * 100) if prs else 0
            print(f" {label:<5}: {count:>3} ({pct:>5.1f}%)")

    # PLOT COMMITS BY HOUR
    since_dt = datetime.now(timezone.utc) - timedelta(days=LOOKBACK_DAYS)
    all_commits = list(repo.get_commits(since=since_dt))
    plot_commits_by_hour(all_commits)


if __name__ == "__main__":
    main()