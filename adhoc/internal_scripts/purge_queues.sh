#!/bin/bash

# Define the LocalStack container name
CONTAINER_NAME="localstack-main"

# Define the list of queue URLs
QUEUES=(
    "http://sqs.us-east-1.localhost.localstack.cloud:4566/000000000000/EeselAiCrawlerQueue"
    "http://sqs.us-east-1.host.docker.internal:4566/000000000000/eesel_ai_async_ai_response_queue"
    "http://sqs.us-east-1.localhost.localstack.cloud:4566/000000000000/EeselAiFileProcessorQueue"
    "http://sqs.us-east-1.localhost.localstack.cloud:4566/000000000000/EeselAiMessageEvalQueue"
    "http://sqs.us-east-1.localhost.localstack.cloud:4566/000000000000/EeselAiLearningQueue"
    "http://sqs.us-east-1.localhost.localstack.cloud:4566/000000000000/EeselAiClusteringQueue"
    "http://sqs.us-east-1.localhost.localstack.cloud:4566/000000000000/EeselAiTicketProcessingQueue"
    "http://sqs.us-east-1.localhost.localstack.cloud:4566/000000000000/EeselAiSyncLoaderQueue"
    "http://sqs.us-east-1.localhost.localstack.cloud:4566/000000000000/EeselAiSimulationQueue"
    "http://sqs.us-east-1.localhost.localstack.cloud:4566/000000000000/EeselAiIngestionQueue"
    "http://sqs.us-east-1.localhost.localstack.cloud:4566/000000000000/EeselAiHelpdeskMessageProcessingQueue"
)

# Purge all SQS queues
for QUEUE in "${QUEUES[@]}"; do
    echo "Purging queue: $QUEUE"
    docker exec -it "$CONTAINER_NAME" awslocal sqs purge-queue --queue-url "$QUEUE"
done

echo "All queues have been purged."

# Define the list of event source mappings to remove
EVENT_SOURCES=(
    "EeselAiFileProcessorQueue"
    "EeselAiIngestionQueue"
    "EeselAiCrawlerQueue"
)

# Remove event source mappings
for SOURCE in "${EVENT_SOURCES[@]}"; do
    echo "Removing event source mapping for $SOURCE"
    MAPPING_ID=$(docker exec -it "$CONTAINER_NAME" awslocal lambda list-event-source-mappings --query "EventSourceMappings[?EventSourceArn=='arn:aws:sqs:us-east-1:000000000000:$SOURCE'].UUID" --output text)
    if [ -n "$MAPPING_ID" ]; then
        docker exec -it "$CONTAINER_NAME" awslocal lambda delete-event-source-mapping --uuid "$MAPPING_ID"
    fi
done

echo "All event source mappings have been removed."

# Pause to allow LocalStack to sync
sleep 10

# Reset Lambda configurations
docker exec -it "$CONTAINER_NAME" awslocal lambda update-function-configuration --function-name eeselAiFileProcessor --environment "Variables={ENV=local,INGEST_QUEUE=http://sqs.us-east-1.localhost.localstack.cloud:4566/000000000000/EeselAiIngestionQueue,S3_ENDPOINT=http://localhost.localstack.cloud:4566}" --region us-east-1

docker exec -it "$CONTAINER_NAME" awslocal lambda update-function-configuration --function-name eeselAiCrawlerIngest --environment "Variables={SERVER_URL=http://host.docker.internal:8080}" --region us-east-1 --timeout 300

docker exec -it "$CONTAINER_NAME" awslocal lambda update-function-configuration --function-name eeselAiCrawler --environment "Variables={SERVER_URL=http://host.docker.internal:8080,CRAWLEE_STORAGE_DIR=/tmp/crawlee/storage,INGEST_QUEUE=http://sqs.us-east-1.localhost.localstack.cloud:4566/000000000000/EeselAiIngestionQueue,CRAWLER_QUEUE=http://sqs.us-east-1.localhost.localstack.cloud:4566/000000000000/EeselAiCrawlerQueue}" --memory-size 2048 --timeout 300 --region us-east-1

echo "Lambda functions have been reconfigured."
