#!/bin/bash

# This file is used to backup the contents of the db and the credentials folder.
# It should be run daily with a scheduled task in cron.

# crontab -e
# 0 0 * * * /path/to/backup_script.py >> /path/to/cron.log 2>&1

# Paths to the file and directory
db_file_path="/home/<USER>/eesel-oracle.db"
data_directory_path="/home/<USER>/app_v2/data"

# Destination paths in S3
today=$(date +%Y-%m-%d)
db_s3_key="database_backups/oracle-eesel_${today}.zip"
data_s3_key="other_backups/data_${today}.zip"

# Zip the file and directory
db_zip_path="/tmp/oracle-eesel_${today}.zip"
data_zip_path="/tmp/data_${today}.zip"
zip -r "$data_zip_path" "$data_directory_path"
zip "$db_zip_path" "$db_file_path"

# Upload the zip files to S3
bucket_name="your-s3-bucket-name"
aws s3 cp "$db_zip_path" "s3://${bucket_name}/${db_s3_key}" --profile backupUser
aws s3 cp "$data_zip_path" "s3://${bucket_name}/${data_s3_key}" --profile backupUser

# Remove the temporary zip files
rm "$db_zip_path"
rm "$data_zip_path"
