#!/bin/bash

output_file="urls.txt"    # Name of the output file
initial_url="https://support.appsflyer.com/api/v2/help_center/en-us/articles.json?page=1&per_page=30" # Start URL

url="$initial_url"

# Loop until the "next_page" is null
while [ "$url" != "null" ]; do
    # Use cURL to fetch the JSON and extract URLs with jq
    curl -s "$url" | jq -r '.articles[] .html_url' >> "$output_file"

    # Fetch the "next_page" URL for the next iteration
    url=$(curl -s "$url" | jq -r '.next_page')
done
