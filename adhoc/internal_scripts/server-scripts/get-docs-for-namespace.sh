#!/bin/bash

CSV_FLAG=false
if [[ -z "${PEM_PATH}" ]]; then
  PEM_PATH="~/Downloads/gpt-oracle-mvp.pem"
fi

PRIMARY_IP=$(<../vpn/app_private_ip.txt)

# Parse command line arguments
for arg in "$@"; do
    case $arg in
        --csv)
            CSV_FLAG=true
            ;;
        *)
            NAMESPACE="$arg" # assuming only one other argument, which is the namespace
            ;;
    esac
done

# Check for input
if [[ -z "$NAMESPACE" ]]; then
    echo "Usage: $0 <namespace> [--csv]"
    exit 1
fi

REMOTE_USER="ec2-user" 
REMOTE_SERVER=$PRIMARY_IP

if $CSV_FLAG; then
    LOCAL_OUTPUT_FILE="$NAMESPACE.csv"
    # Execute the remote query to get the number of documents for the given namespace
    # and save the output as a CSV on the local machine
    ssh -i $PEM_PATH $REMOTE_USER@$REMOTE_SERVER "sqlite3 -csv ~/eesel-oracle.db 'SELECT url FROM documents WHERE namespaceId = \"$NAMESPACE\";'" > $LOCAL_OUTPUT_FILE
    echo "Results saved to: $LOCAL_OUTPUT_FILE"
else
    # Just display the result
    ssh -i $PEM_PATH $REMOTE_USER@$REMOTE_SERVER "sqlite3 -column -header ~/eesel-oracle.db 'SELECT * FROM documents WHERE namespaceId = \"$NAMESPACE\";'"
fi