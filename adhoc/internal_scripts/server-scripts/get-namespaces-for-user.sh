#!/bin/bash

if [[ -z "${PEM_PATH}" ]]; then
  PEM_PATH="~/Downloads/gpt-oracle-mvp.pem"
fi

# Check for input
if [ "$#" -ne 1 ]; then
    echo "Usage: $0 <namespace>"
    exit 1
fi

USERID="$1"
REMOTE_USER="ec2-user" 
REMOTE_SERVER="54.84.165.202" 

# Execute the remote query
ssh -i $PEM_PATH $REMOTE_USER@$REMOTE_SERVER "sqlite3 -column -header ~/eesel-oracle.db <<< 'SELECT * FROM eesel_ai_namespaces WHERE userid = \"$USERID\";'"
