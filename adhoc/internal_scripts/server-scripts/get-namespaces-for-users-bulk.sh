#!/bin/bash

# Default PEM_PATH, can be overridden by an environment variable
if [[ -z "${PEM_PATH}" ]]; then
  PEM_PATH="~/Downloads/gpt-oracle-mvp.pem"
fi

# Check for input file path
if [ "$#" -ne 1 ]; then
    echo "Usage: $0 <user_id_file>"
    exit 1
fi

USERID_FILE="$1"
REMOTE_USER="ec2-user" 
REMOTE_SERVER="54.84.165.202" 
BATCH_SIZE=1000

# Function to process a batch of userIds
process_batch() {
    local batch_ids=("$@")
    local userid_str=""

    for userid in "${batch_ids[@]}"; do
        if [[ -z "$userid_str" ]]; then
            userid_str="\"$userid\""
        else
            userid_str="$userid_str, \"$userid\""
        fi
    done

    # Execute the bulk query for the batch and format output as CSV
    echo "Executing bulk query for userId batch"
    ssh -i $PEM_PATH $REMOTE_USER@$REMOTE_SERVER "sqlite3 -header -csv ~/eesel-oracle.db <<< 'SELECT eesel_ai_namespaces.id AS namespace_id, eesel_ai_namespaces.userid AS user_id FROM eesel_ai_namespaces WHERE eesel_ai_namespaces.userid IN ($userid_str);'"
}

# Read userIds and process in batches
batch=()
while IFS= read -r userid; do
    batch+=("$userid")
    if [[ ${#batch[@]} -eq $BATCH_SIZE ]]; then
        process_batch "${batch[@]}"
        batch=()
    fi
done < "$USERID_FILE"

# Process the last batch if it has any userIds left
if [[ ${#batch[@]} -gt 0 ]]; then
    process_batch "${batch[@]}"
fi