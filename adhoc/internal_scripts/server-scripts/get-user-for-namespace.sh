#!/bin/bash

if [[ -z "${PEM_PATH}" ]]; then
  PEM_PATH="~/Downloads/gpt-oracle-mvp.pem"
fi

# Check for input
if [ "$#" -ne 1 ]; then
    echo "Usage: $0 <namespace>"
    exit 1
fi

USERID="$1"
REMOTE_USER="ec2-user" 
REMOTE_SERVER=$(<../../../vpn/app_private_ip.txt)

# Add error checking for the server IP
if [[ -z "${REMOTE_SERVER}" ]]; then
    echo "Error: Could not read server IP from app_private_ip.txt"
    exit 1
fi

echo "Attempting to connect to ${REMOTE_SERVER} as ${REMOTE_USER}..."

# Execute the remote query with error handling
if ! ssh -i "${PEM_PATH}" ${REMOTE_USER}@${REMOTE_SERVER} "sqlite3 -column -header ~/eesel-oracle.db \"SELECT userid FROM eesel_ai_namespaces WHERE userid = '$USERID';\""; then
    echo "Error: SSH connection or SQLite query failed"
    exit 1
fi

# If no results were returned but the command succeeded, print a message
if [ $? -eq 0 ]; then
    echo "Query completed successfully, but no matching user found for namespace: ${USERID}"
fi

