#!/bin/bash

# Default PEM_PATH, can be overridden by an environment variable
if [[ -z "${PEM_PATH}" ]]; then
  PEM_PATH="~/Downloads/gpt-oracle-mvp.pem"
fi

# Check for input file path
if [ "$#" -ne 1 ]; then
    echo "Usage: $0 <user_id_file>"
    exit 1
fi

USERID_FILE="$1"
REMOTE_USER="ec2-user" 
REMOTE_SERVER="54.84.165.202" 
BATCH_SIZE=500 # Adjust this size based on the maximum limit your environment supports

echo "Processing file: $USERID_FILE"

# Count total lines in file
total_lines=$(wc -l < "$USERID_FILE")
echo "Total lines in file: $total_lines"

# Function to process a batch of userIds
process_batch() {
    local batch_ids=("$@")
    local userid_str=""

    # echo "Processing batch of size: ${#batch_ids[@]}"

    for userid in "${batch_ids[@]}"; do
        if [[ -z "$userid_str" ]]; then
            userid_str="\"$userid\""
        else
            userid_str="$userid_str, \"$userid\""
        fi
    done

    # Execute the bulk query for the batch and format output as CSV
    # echo "Executing bulk query for userId batch"
    ssh -i $PEM_PATH $REMOTE_USER@$REMOTE_SERVER "sqlite3 -header -csv ~/eesel-oracle.db <<< 'SELECT id AS namespace_id, userid AS user_id FROM eesel_ai_namespaces WHERE id IN ($userid_str);'" < /dev/null
}

# Read userIds and process in batches
batch=()
line_count=0
while IFS= read -r userid; do
    line_count=$((line_count + 1))
    # echo "Reading line $line_count: $userid"
    batch+=("$userid")
    if [[ ${#batch[@]} -eq $BATCH_SIZE ]]; then
        process_batch "${batch[@]}"
        batch=() # Clear the batch array
    fi
done < "$USERID_FILE"

# echo "Processed $line_count lines in total."

# Process the last batch if it has any userIds left
if [[ ${#batch[@]} -gt 0 ]]; then
    process_batch "${batch[@]}"
fi

echo "Processing complete."
