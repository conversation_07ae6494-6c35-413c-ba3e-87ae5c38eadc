#!/bin/bash

# Ensure urls.txt exists
if [ ! -f urls.txt ]; then
    echo "Error: urls.txt does not exist."
    exit 1
fi

# Check if JWT is provided as an argument
if [ "$#" -ne 2 ]; then
    echo "Usage: $0 <ingest_jwt> <user_id>"
    exit 1
fi

INGEST_JWT="$1"
USER_ID="$2"

# Iterate over each line in urls.txt
while IFS= read -r URL; do
    RUN_ID=$(uuidgen)

    # Create the task in JSON format
    TASK_JSON=$(cat <<EOF
{
  "run_id": "$RUN_ID",
  "url": "$URL",
  "max_pages": 1,
  "ingest_jwt": "$INGEST_JWT",
  "user_id": "$USER_ID"
}
EOF
)

    # Send the task to SQS
    echo "$TASK_JSON"
    # aws sqs send-message --queue-url "$SCRAPE_QUEUE_URL" --message-body "$TASK_JSON" --region us-east-1

done < urls.txt