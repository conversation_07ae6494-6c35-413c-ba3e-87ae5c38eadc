# python get-vectors-for-namespace.py --namespace 42bd6232-2975-498f-beec-d65eba5e155e --output-file vector_ids.txt --index-host  --api-key 

import requests
import os
import json
import hashlib
import sys

NAMESPACE = '*************************' # namespace
PINECONE_API_KEY = "**********************" # eu pinecone key
INDEX_HOST = "eesel-gpt-intercom-39ad883.svc.us-east-1-aws.pinecone.io"
CHECKPOINT_FILE = f"checkpoint_{NAMESPACE}.txt"
OUTPUT_FILE = f"simployer_vectors_{NAMESPACE}.json"
BATCH_SIZE = 30

def sha256_hash(text):
    return hashlib.sha256(text.encode()).hexdigest()

DUMMY_ARR = [0.0 for _ in range(1536)]

def query_vectors(document_ids):
    url = f"https://{INDEX_HOST}/query"
    headers = {
        "Api-Key": PINECONE_API_KEY,
        "Content-Type": "application/json"
    }
    data = {
        "vector": DUMMY_ARR,
        "filter": {"document_id": {"$in": document_ids}},
        "topK": 1000,
        "namespace": f"{NAMESPACE}_eesel",
        "includeValues": True,
        "includeMetadata": True
    }
    response = requests.post(url, headers=headers, data=json.dumps(data))

    return response.json().get('matches', [])

def get_checkpoint():
    if os.path.exists(CHECKPOINT_FILE):
        with open(CHECKPOINT_FILE, 'r') as file:
            return int(file.read().strip())
    return 0

def save_checkpoint(index):
    with open(CHECKPOINT_FILE, 'w') as file:
        file.write(str(index))

def read_urls(input_file):
    with open(input_file, 'r') as file:
        return [line.strip() for line in file]

def load_existing_vectors(output_file):
    if os.path.exists(output_file):
        with open(output_file, 'r') as file:
            return json.load(file)
    return []

def save_vectors(vectors, output_file):
    with open(output_file, 'w') as file:
        json.dump(vectors, file)

def process_urls(input_file):
    urls = read_urls(input_file)
    checkpoint = get_checkpoint()
    existing_vectors = load_existing_vectors(OUTPUT_FILE)

    for batch_start in range(checkpoint, len(urls), BATCH_SIZE):
        batch_end = min(batch_start + BATCH_SIZE, len(urls))
        url_batch = urls[batch_start:batch_end]
        document_ids = [sha256_hash(url) for url in url_batch]
        vectors = query_vectors(document_ids)

        if len(vectors) >= 1000:
            for document_id in document_ids:
                individual_vectors = query_vectors([document_id])
                existing_vectors.extend(individual_vectors)
        else:
            existing_vectors.extend(vectors)


        print(f"Processed URLs: {url_batch}")
    save_vectors(existing_vectors, OUTPUT_FILE)
    # save_checkpoint(batch_end)
    print(f"Total vectors: {len(existing_vectors)}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python script.py <input_file>")
        sys.exit(1)
    input_file = sys.argv[1]
    process_urls(input_file)