import requests
import os
import json

NAMESPACE = '*************************' # namespace
PINECONE_API_KEY = "**********************" # eu pinecone key
INDEX_HOST = "eesel-prod-eu-8tuhajc.svc.apu-57e2-42f6.pinecone.io"

INPUT_FILE = f"simployer_vectors_{NAMESPACE}.json"
CHECKPOINT_FILE = f"checkpoint_upsert_{NAMESPACE}.txt"

BATCH_SIZE = 75

def upsert_vectors(vectors, namespace):
    url = f"https://{INDEX_HOST}/vectors/upsert"
    headers = {
        "Api-Key": PINECONE_API_KEY,
        "content-type": "application/json"
    }
    data = {
        "vectors": vectors,
        "namespace": f"{namespace}_eesel"
    }
    response = requests.post(url, headers=headers, data=json.dumps(data))
    response.raise_for_status()
    return response.json()

def get_checkpoint():
    if os.path.exists(CHECKPOINT_FILE):
        with open(CHECKPOINT_FILE, 'r') as file:
            return int(file.read().strip())
    return 0

def save_checkpoint(index):
    with open(CHECKPOINT_FILE, 'w') as file:
        file.write(str(index))

def read_vectors(input_file):
    with open(input_file, 'r') as file:
        return json.load(file)

def process_vectors(input_file, new_namespace):
    vectors = read_vectors(input_file)
    checkpoint = get_checkpoint()

    for index in range(checkpoint, len(vectors), BATCH_SIZE):
        batch = vectors[index:index + BATCH_SIZE]
        upsert_vectors(batch, new_namespace)
        save_checkpoint(index + len(batch))
        print(f"Processed batch starting at index: {index}")

if __name__ == "__main__":
    process_vectors(INPUT_FILE, NAMESPACE)