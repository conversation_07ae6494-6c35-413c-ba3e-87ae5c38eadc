import argparse
import os
import pinecone

# ideally this is an automated process, not a manual script to run
# but having the script is handy nonetheless and can be ported into a cron job.

# essentially the logic could have to be:
#  - find all namespaces that match the following criteria
#    1. are not a paying customer
#    2. trial has expired for > 14 days -->> but we may want to paywall in this case otherwise
#       the user may lose access before they realise that there was a trial in the first place.

def delete_namespaces(namespaces, dry_run):
    pinecone.init(api_key=os.getenv("PINECONE_API_KEY"), environment=os.getenv("PINECONE_ENV"))

    for namespace in namespaces:
        index = pinecone.Index(os.getenv("PINECONE_INDEX"))
        if dry_run:
            print(f"Dry run: Would delete namespace '{namespace}'")
        else:
            confirmation = input(f"Are you sure you want to delete namespace '{namespace}'? [y/N]: ")
            if confirmation.lower() == 'y':
                index.delete(delete_all=True, namespace=namespace)
                print(f"Deleted namespace '{namespace}'")
            else:
                print(f"Skipped deletion of namespace '{namespace}'")

def main():
    parser = argparse.ArgumentParser(description="Delete namespaces from a Pinecone index.")
    parser.add_argument('--namespaces', nargs='+', help='List of namespaces to delete')
    parser.add_argument('--file', type=str, help='File containing namespaces to delete')
    parser.add_argument('--dry-run', action='store_true', help='Perform a dry run without deleting')
    args = parser.parse_args()

    if args.file:
        with open(args.file, 'r') as file:
            namespaces = [line.strip() for line in file.readlines()]
    elif args.namespaces:
        namespaces = args.namespaces
    else:
        parser.error('No namespaces provided. Use --namespaces or --file.')

    delete_namespaces(namespaces, args.dry_run)

if __name__ == "__main__":
    main()