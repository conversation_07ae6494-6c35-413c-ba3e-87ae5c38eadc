import os
import json
import pinecone

pinecone.init(api_key=os.getenv("PINECONE_API_KEY"), environment=os.getenv("PINECONE_ENV"))

index = pinecone.Index(os.getenv("PINECONE_INDEX"))

index_stats_response = index.describe_index_stats()
stats_dict = index_stats_response.to_dict()

sorted_namespaces = sorted(stats_dict['namespaces'].items(), key=lambda x: x[1]['vector_count'], reverse=True)
formatted_data = [{"namespace_id": ns_id, "vector_count": ns_info["vector_count"]} for ns_id, ns_info in sorted_namespaces]

path = "exported_namespaces.json"

with open(path, 'w') as file:
    json.dump(formatted_data, file, indent=4)