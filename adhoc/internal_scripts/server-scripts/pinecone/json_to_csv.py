import json
import csv

# File path for the JSON input
json_file = 'exported_namespaces.json'

# Read JSON data from a file
with open(json_file, 'r') as file:
    json_data = json.load(file)

# File path for the CSV output
csv_file = 'output.csv'
with open(csv_file, 'w', newline='') as file:
    writer = csv.DictWriter(file, fieldnames=['namespace_id', 'vector_count'])
    writer.writeheader()
    for item in json_data:
        writer.writerow(item)