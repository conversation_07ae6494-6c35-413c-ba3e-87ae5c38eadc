import json
import csv
import sys

def json_to_csv(input_file, output_file):
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not data:
            print("No data found in JSON file.")
            return

        # Extract keys for CSV headers
        keys = data[0].keys()

        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=keys)
            writer.writeheader()
            writer.writerows(data)
        
        print(f"CSV file '{output_file}' has been created successfully.")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    input_file = sys.argv[1]
    output_file = "output.csv"
    json_to_csv(input_file, output_file)

