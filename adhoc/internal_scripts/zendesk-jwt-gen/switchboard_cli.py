"""
Sunshine Conversations API CLI Tool
=================================

A command-line interface for managing Zendesk Sunshine Conversations integrations and switchboards.

Usage:
    ./switchboard_cli.py <operation> [options]

Operations:
    - list-integrations
    - update-integration
    - create-switchboard
    - list-switchboards
    - create-switchboard-integration
    - update-switchboard-integration
    - list-switchboard-integrations
    - delete-switchboard-integration
    - list-webhooks
    - update-webhook-url
    - create-integration-key

Environment Variables:
    SUNSHINE_SECRET_KEY - API secret key
    SUNSHINE_APP_ID - Application ID
    SUNSHINE_KEY_ID - Key identifier

For detailed usage, run: ./switchboard_cli.py --help
"""

#!/usr/bin/env python3
import argparse
import requests
import json
import sys
import os
import hmac
import base64
import time
from typing import Dict

# Load environment variables
SECRET_KEY = os.getenv('SUNSHINE_SECRET_KEY')
APP_ID = os.getenv('SUNSHINE_APP_ID')
KEY_ID = os.getenv('SUNSHINE_KEY_ID')

BASE_URL = "https://api.smooch.io/v2"

def generate_jwt() -> str:
    """Generate JWT token following the same logic as main.js"""
    header = {
        "alg": "HS256",
        "typ": "JWT",
        "kid": KEY_ID
    }

    payload = {
        "scope": "app",
        "integration_id": APP_ID,
        "iat": int(time.time())
    }

    # Encode header and payload
    encoded_header = base64.urlsafe_b64encode(json.dumps(header).encode()).rstrip(b'=').decode()
    encoded_payload = base64.urlsafe_b64encode(json.dumps(payload).encode()).rstrip(b'=').decode()

    # Create signature
    message = f"{encoded_header}.{encoded_payload}"
    signature = hmac.new(
        SECRET_KEY.encode(),
        message.encode(),
        'sha256'
    )
    encoded_signature = base64.urlsafe_b64encode(signature.digest()).rstrip(b'=').decode()

    return f"{encoded_header}.{encoded_payload}.{encoded_signature}"

def get_headers() -> Dict[str, str]:
    return {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {generate_jwt()}'
    }

def list_integrations():
    url = f"{BASE_URL}/apps/{APP_ID}/integrations"
    response = requests.get(url, headers=get_headers())
    return response.json()

def update_integration(integration_id: str, responder_id: str):
    url = f"{BASE_URL}/apps/{APP_ID}/integrations/{integration_id}"
    data = {"defaultResponderId": responder_id}
    response = requests.patch(url, headers=get_headers(), json=data)
    return response.json()

def create_switchboard():
    url = f"{BASE_URL}/apps/{APP_ID}/switchboards"
    response = requests.post(url, headers=get_headers())
    return response.json()

def list_switchboards():
    url = f"{BASE_URL}/apps/{APP_ID}/switchboards"
    response = requests.get(url, headers=get_headers())
    return response.json()

def create_switchboard_integration(switchboard_id: str, name: str, integration_id: str, next_integration_id: str):
    url = f"{BASE_URL}/apps/{APP_ID}/switchboards/{switchboard_id}/switchboardIntegrations"
    data = {
        "name": name,
        "integrationId": integration_id,
        "deliverStandbyEvents": True,
        "nextSwitchboardIntegrationId": next_integration_id,
        "messageHistoryCount": 5
    }
    response = requests.post(url, headers=get_headers(), json=data)
    return response.json()

def update_switchboard_integration(switchboard_id: str, switchboard_integration_id: str, next_integration_id: str, message_history_count: int = None):
    url = f"{BASE_URL}/apps/{APP_ID}/switchboards/{switchboard_id}/switchboardIntegrations/{switchboard_integration_id}"
    data = {
        "nextSwitchboardIntegrationId": next_integration_id
    }
    if message_history_count is not None:
        data["messageHistoryCount"] = message_history_count
    response = requests.patch(url, headers=get_headers(), json=data)
    return response.json()

def list_switchboard_integrations(switchboard_id: str):
    url = f"{BASE_URL}/apps/{APP_ID}/switchboards/{switchboard_id}/switchboardIntegrations"
    response = requests.get(url, headers=get_headers())
    return response.json()

def delete_switchboard_integration(switchboard_id: str, switchboard_integration_id: str):
    url = f"{BASE_URL}/apps/{APP_ID}/switchboards/{switchboard_id}/switchboardIntegrations/{switchboard_integration_id}"
    response = requests.delete(url, headers=get_headers())
    if response.status_code == 204:
        return {"success": True}
    else:
        try:
            error_info = response.json()
        except:
            error_info = {"message": response.text}
        return {
            "success": False,
            "status_code": response.status_code,
            "error": error_info
        }

def delete_integration(integration_id: str):
    url = f"{BASE_URL}/apps/{APP_ID}/integrations/{integration_id}"
    response = requests.delete(url, headers=get_headers())
    if response.status_code == 204:
        return {"success": True}
    else:
        try:
            error_info = response.json()
        except:
            error_info = {"message": response.text}
        return {
            "success": False,
            "status_code": response.status_code,
            "error": error_info
        }

def list_webhooks(integration_id: str):
    url = f"{BASE_URL}/apps/{APP_ID}/integrations/{integration_id}/webhooks"
    response = requests.get(url, headers=get_headers())
    return response.json()

def update_webhook_url(integration_id: str, webhook_id: str, target: str):
    url = f"{BASE_URL}/apps/{APP_ID}/integrations/{integration_id}/webhooks/{webhook_id}"
    data = {"target": target}
    response = requests.patch(url, headers=get_headers(), json=data)
    return response.json()

def create_integration_key(integration_id: str, display_name: str):
    """Create a new API key for a custom integration"""
    url = f"{BASE_URL}/apps/{APP_ID}/integrations/{integration_id}/keys"
    data = {
        "displayName": display_name
    }
    response = requests.post(url, headers=get_headers(), json=data)
    return response.json()

def main():
    parser = argparse.ArgumentParser(description='Sunshine Conversations API CLI')
    parser.add_argument('operation', choices=[
        'list-integrations',
        'update-integration',
        'create-switchboard',
        'list-switchboards',
        'create-switchboard-integration',
        'update-switchboard-integration',
        'list-switchboard-integrations',
        'delete-switchboard-integration',
        'delete-integration',
        'list-webhooks',
        'update-webhook-url',
        'create-integration-key'
    ])
    parser.add_argument('--integration-id', help='Integration ID for updates')
    parser.add_argument('--responder-id', help='Responder ID for integration updates')
    parser.add_argument('--switchboard-id', help='Switchboard ID')
    parser.add_argument('--name', help='Name for switchboard integration')
    parser.add_argument('--next-integration-id', help='Next integration ID for switchboard')
    parser.add_argument('--switchboard-integration-id', help='Switchboard integration ID for updates')
    parser.add_argument('--webhook-id', help='Webhook ID for updates')
    parser.add_argument('--target', help='New webhook target URL')
    parser.add_argument('--message-history-count', type=int, help='Number of messages to include in history')
    parser.add_argument('--display-name', help='Display name for the integration key')

    args = parser.parse_args()

    try:
        if args.operation == 'list-integrations':
            result = list_integrations()
        elif args.operation == 'update-integration':
            if not args.integration_id or not args.responder_id:
                parser.error("update-integration requires --integration-id and --responder-id")
            result = update_integration(args.integration_id, args.responder_id)
        elif args.operation == 'create-switchboard':
            result = create_switchboard()
        elif args.operation == 'list-switchboards':
            result = list_switchboards()
        elif args.operation == 'create-switchboard-integration':
            if not all([args.switchboard_id, args.name, args.integration_id, args.next_integration_id]):
                parser.error("create-switchboard-integration requires --switchboard-id, --name, --integration-id, and --next-integration-id")
            result = create_switchboard_integration(
                args.switchboard_id,
                args.name,
                args.integration_id,
                args.next_integration_id
            )
        elif args.operation == 'update-switchboard-integration':
            if not all([args.switchboard_id, args.switchboard_integration_id, args.next_integration_id]):
                parser.error("update-switchboard-integration requires --switchboard-id, --switchboard-integration-id, and --next-integration-id")
            result = update_switchboard_integration(
                args.switchboard_id,
                args.switchboard_integration_id,
                args.next_integration_id,
                args.message_history_count
            )
        elif args.operation == 'list-switchboard-integrations':
            if not args.switchboard_id:
                parser.error("list-switchboard-integrations requires --switchboard-id")
            result = list_switchboard_integrations(args.switchboard_id)
        elif args.operation == 'delete-switchboard-integration':
            if not all([args.switchboard_id, args.switchboard_integration_id]):
                parser.error("delete-switchboard-integration requires --switchboard-id and --switchboard-integration-id")
            result = delete_switchboard_integration(
                args.switchboard_id,
                args.switchboard_integration_id
            )
        elif args.operation == 'delete-integration':
            if not args.integration_id:
                parser.error("delete-integration requires --integration-id")
            result = delete_integration(args.integration_id)
        elif args.operation == 'list-webhooks':
            if not args.integration_id:
                parser.error("list-webhooks requires --integration-id")
            result = list_webhooks(args.integration_id)
        elif args.operation == 'update-webhook-url':
            if not all([args.integration_id, args.webhook_id, args.target]):
                parser.error("update-webhook-url requires --integration-id, --webhook-id, and --target")
            result = update_webhook_url(
                args.integration_id,
                args.webhook_id,
                args.target
            )
        elif args.operation == 'create-integration-key':
            if not args.integration_id or not args.display_name:
                parser.error("create-integration-key requires --integration-id and --display-name")
            result = create_integration_key(args.integration_id, args.display_name)

        print(json.dumps(result, indent=2))

    except requests.exceptions.RequestException as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
