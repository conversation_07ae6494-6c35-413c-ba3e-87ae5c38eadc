#!/bin/bash

# Check if required arguments are provided
if [ $# -lt 2 ]; then
    echo "Please provide both domain and ticket ID as arguments"
    echo "Usage: ./fetch1.sh <zendesk_domain> <ticket_id>"
    exit 1
fi

# Zendesk configuration
ZENDESK_DOMAIN="$1"
TICKET_ID="$2"

# Check if API token is set in environment
if [ -z "$ZENDESK_API_TOKEN" ]; then
    echo "Error: ZENDESK_API_TOKEN environment variable is not set"
    exit 1
fi

# Make the API request
echo "Making API request to: https://$ZENDESK_DOMAIN/api/v2/tickets/$TICKET_ID.json"
echo "Using authorization token: ${ZENDESK_API_TOKEN:0:10}..." # Only show first 10 chars for security

RESPONSE=$(curl -v \
    -H "Authorization: $ZENDESK_API_TOKEN" \
    -H "Content-Type: application/json" \
    "https://$ZENDESK_DOMAIN/api/v2/tickets/$TICKET_ID.json")

echo "Response received:"
echo "$RESPONSE" | jq '.' || echo "Failed to parse JSON response: $RESPONSE"
