import requests
import json
from datetime import datetime
from pathlib import Path

def fetch_zendesk_tickets(subdomain, access_token, limit=5):
    # Base URL for Zendesk API
    base_url = f"https://{subdomain}.zendesk.com/api/v2"
    
    # Headers for authentication
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    # Get recent tickets
    tickets_url = f"{base_url}/tickets.json?sort=-created_at&per_page={limit}&include=users"
    tickets_response = requests.get(tickets_url, headers=headers)
    
    if tickets_response.status_code != 200:
        raise Exception(f"Failed to fetch tickets: {tickets_response.status_code}")
    
    tickets_data = tickets_response.json()
    
    # Process each ticket and fetch its comments
    full_tickets = []
    for ticket in tickets_data['tickets']:
        # Fetch comments for this ticket
        comments_url = f"{base_url}/tickets/{ticket['id']}/comments.json"
        comments_response = requests.get(comments_url, headers=headers)
        
        if comments_response.status_code == 200:
            comments = comments_response.json()['comments']
            # Add comments to ticket data
            ticket['comments'] = comments
            
            # Convert timestamps to readable format
            ticket['created_at'] = datetime.fromisoformat(ticket['created_at'].replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M:%S')
            ticket['updated_at'] = datetime.fromisoformat(ticket['updated_at'].replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M:%S')
            
            full_tickets.append(ticket)
    
    return full_tickets

def fetch_zendesk_macros(subdomain, access_token):
    base_url = f"https://{subdomain}.zendesk.com/api/v2"
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    macros_url = f"{base_url}/macros.json"
    macros_response = requests.get(macros_url, headers=headers)
    
    if macros_response.status_code != 200:
        raise Exception(f"Failed to fetch macros: {macros_response.status_code}")
    
    return macros_response.json()['macros']

def main():
    # Read Zendesk accounts from json file
    script_dir = Path(__file__).parent
    zendesk_config_path = script_dir / 'zendesk.json'
    output_dir = script_dir / 'zendesk_outputs'
    
    # Create output directory if it doesn't exist
    output_dir.mkdir(exist_ok=True)
    
    try:
        with open(zendesk_config_path, 'r') as f:
            zendesk_accounts = json.load(f)
        
        # Process each Zendesk account
        for account in zendesk_accounts:
            subdomain = account['subdomain']
            access_token = account['access_token']
            
            print(f"\nProcessing {subdomain}...")
            
            try:
                # Fetch and write tickets
                tickets = fetch_zendesk_tickets(subdomain, access_token)
                tickets_output_file = output_dir / f"zendesk_tickets_{subdomain.lower()}.txt"
                
                with open(tickets_output_file, 'w', encoding='utf-8') as f:
                    for ticket in tickets:
                        f.write("\n" + "="*50 + "\n")
                        f.write(f"Ticket ID: {ticket['id']}\n")
                        f.write(f"Subject: {ticket['subject']}\n")
                        f.write(f"Status: {ticket['status']}\n")
                        f.write(f"Created: {ticket['created_at']}\n")
                        f.write(f"Updated: {ticket['updated_at']}\n")
                        f.write("\nComments:\n")
                        
                        for comment in ticket['comments']:
                            f.write("-"*30 + "\n")
                            f.write(f"Author ID: {comment['author_id']}\n")
                            f.write(f"Created: {datetime.fromisoformat(comment['created_at'].replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M:%S')}\n")
                            f.write(f"Body: {comment['body']}\n")
                
                print(f"Successfully wrote tickets for {subdomain} to {tickets_output_file}")
                
                # Fetch and write macros
                macros = fetch_zendesk_macros(subdomain, access_token)
                macros_output_file = output_dir / f"zendesk_macros_{subdomain.lower()}.txt"
                
                with open(macros_output_file, 'w', encoding='utf-8') as f:
                    for macro in macros:
                        f.write("\n" + "="*50 + "\n")
                        f.write(f"Macro ID: {macro['id']}\n")
                        f.write(f"Title: {macro['title']}\n")
                        f.write(f"Active: {macro['active']}\n")
                        f.write(f"Created: {datetime.fromisoformat(macro['created_at'].replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write(f"Updated: {datetime.fromisoformat(macro['updated_at'].replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M:%S')}\n")
                        
                        if 'actions' in macro:
                            f.write("\nActions:\n")
                            f.write(json.dumps(macro['actions'], indent=2) + "\n")
                
                print(f"Successfully wrote macros for {subdomain} to {macros_output_file}")
                
            except Exception as e:
                print(f"Error processing {subdomain}: {str(e)}")
                continue
                
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    main()