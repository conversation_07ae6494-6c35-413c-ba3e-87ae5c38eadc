from typing import List, Dict, Any
from langchain_openai import Chat<PERSON>penAI
from langchain_core.prompts import PromptTemplate
from langchain.output_parsers import ResponseSchema, StructuredOutputParser
from langchain_core.runnables import RunnableParallel, RunnablePassthrough
from langchain_community.callbacks import get_openai_callback
import os
import traceback
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

llm = ChatOpenAI(model="gpt-4o-mini", temperature=0, api_key=os.environ.get("OPENAI_API_KEY"))

article_response_schemas = [
    ResponseSchema(
        name="category_title",
        description="A high-level category title that describes what the cluster is about.",
        type="string"
    )
]
article_response_parser = StructuredOutputParser.from_response_schemas(article_response_schemas)

article_prompt = PromptTemplate(
    template="""
        Based on the following questions and answers, provide a high-level category title that describes what the cluster is about.

        Questions and Answers:
        {qa_pairs}

        {format_instructions}
    """,
    input_variables=["qa_pairs"],
    partial_variables={"format_instructions": article_response_parser.get_format_instructions()}
)

def process_cluster(cluster_idx, qa_pairs):
    try:
        logging.info(f"Processing cluster {cluster_idx} with {len(qa_pairs)} Q&A pairs.")
        qa_pairs_with_index = [
            f"Q: {qa['question']}\nA: {qa['answer']}\nIndex: {idx}" for idx, qa in enumerate(qa_pairs)
        ]
        with get_openai_callback() as cb:
            res = (article_prompt | llm | article_response_parser).invoke({
                "qa_pairs": "\n".join(qa_pairs_with_index)
            })
            category_title = res["category_title"]
            logging.info(f"Processed cluster {cluster_idx} successfully.")
            logging.info(f"Category title for cluster {cluster_idx}: {category_title}")
            return category_title, qa_pairs
    except KeyError as e:
        logging.error(f"KeyError encountered for cluster {cluster_idx}: {e}, skipping")
    except Exception as e:
        logging.error(f"Exception encountered for cluster {cluster_idx}: {e}, skipping")
        traceback.print_exc()
    return None, None

def evaluate_clusters(clusters: Dict[str, List[Dict[str, str]]]) -> Dict[str, Any]:
    categorized_clusters = {}
    with ThreadPoolExecutor(max_workers=10) as executor:
        futures = {executor.submit(process_cluster, idx, qa_pairs): idx for idx, qa_pairs in clusters.items()}
        for future in as_completed(futures):
            category_title, qa_pairs = future.result()
            if category_title and qa_pairs:
                categorized_clusters[category_title] = qa_pairs
    return categorized_clusters

import sys
import json

def write_markdown_output(categorized_clusters: Dict[str, List[Dict[str, str]]], output_filename: str):
    with open(output_filename, 'w') as md_file:
        for category_title, qa_pairs in categorized_clusters.items():
            md_file.write(f"## {category_title}\n")
            for qa in qa_pairs:
                md_file.write(f"**Q:** {qa['question']} **A:** {qa['answer']}\n\n")
            md_file.write("\n")

def main(filename: str):
    try:
        logging.info(f"Opening file {filename}")
        with open(filename, 'r') as file:
            data = json.load(file)
            clusters = data.get("clusters", {})
            if not clusters:
                logging.warning("No clusters found in the provided file.")
                return

            logging.info(f"Found {len(clusters)} clusters in the file.")
            categorized_clusters = evaluate_clusters(clusters)
            
            output = {
                "clusters": categorized_clusters,
                "num_clusters": len(categorized_clusters)
            }

            logging.info("Writing categorized clusters to stdout.")
            print(json.dumps(output, indent=4))

            output_json_filename = filename.replace('.json', '_categorized.json')
            with open(output_json_filename, 'w') as json_file:
                json.dump(output, json_file, indent=4)

            output_md_filename = filename.replace('.json', '_categorized.md')
            write_markdown_output(categorized_clusters, output_md_filename)
            logging.info(f"Markdown output written to {output_md_filename}")

            for category_title, qa_pairs in categorized_clusters.items():
                logging.info(f"Cluster {category_title}: count={len(qa_pairs)}")

    except Exception as e:
        logging.error(f"Error processing file {filename}: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python articlizer.py <input_file_path>")
        sys.exit(1)

    input_file_path = sys.argv[1]
    main(input_file_path)
