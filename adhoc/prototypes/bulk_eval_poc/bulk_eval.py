from dataclasses import dataclass
from typing import List, Optional
import json
from bulk_eval_poc.models import EeselHelpdeskReplayResult, EeselQualityAssessmentResult
from langchain_openai import ChatOpenAI
from langchain_core.output_parsers import Str<PERSON>utputParser
from langchain_core.prompts import PromptTemplate
from langchain.output_parsers import ResponseSchema, StructuredOutputParser
from langchain_core.runnables import RunnableParallel, RunnablePassthrough
from langchain.globals import set_debug

# flatten function
def flatten(*vars):
    flat = {}
    for var in vars:
        keys = [k for k in var]
        for key in keys:
            if isinstance(var[key], dict):
                flat.update(var[key])
            else:
                flat[key] = var[key]
    return flat

# Setup LLM and prompts
llm = ChatOpenAI()

deflection_response_schemas = [
    ResponseSchema(
        name="deflection_attempt",
        description="A boolean indicating whether or not AI made an attempt to help the user without escalating to a human. Possible values are `true` and `false`"
    ),
    ResponseSchema(
        name="deflection_attempt_rationale",
        description="a point-by-point explanation of your rationale for `deflection_attempt`."
    ),
    ResponseSchema(
        name="deflection_attempt_failed_reason",
        description="null if `deflection_attempt` was True, 'knowledge_gap' if the AI could not help due to missing information, otherwise 'other'"
    )
]
deflection_response_parser = StructuredOutputParser.from_response_schemas(deflection_response_schemas)

deflection_prompt = PromptTemplate(
    template="""
        Assess whether the AI reply was an attempt to directly assist with the user's enquiry without escalating to a human or missing knowledge.
        
        User enquiry = {original_message}

        AI reply = {ai_reply}

        {format_instructions}
    """,
    input_variables=["ai_reply", "original_message"],
    partial_variables={"format_instructions": deflection_response_parser.get_format_instructions()}
)

accuracy_response_schemas = [
    ResponseSchema(
        name="accuracy",
        description="A boolean indicating whether the `ai_reply` only contains information that is correct according to the Human Response or the Knowledge Sources.",
        type="boolean"
    ),
    ResponseSchema(
        name="accuracy_rationale",
        description="A point-by-point explanation of your rationale for `accurate`."
    ),
    ResponseSchema(
        name="knowledge_gap_indicated",
        description="A boolean indicating whether the `ai_reply` was unable to help due to a lack of relevant knowledge or content snippets or if it directly contradicted the human response in any way",
        type="boolean"
    ),
    ResponseSchema(
        name="knowledge_gap_rationale",
        description="A point-by-point explanation of your rationale for `knowledge_gap_indicated`"
    ),
    ResponseSchema(
        name="escalation_indicated",
        description="A boolean indicating whether the `ai_reply` specifically recommended transferring or talking to a human.",
        type="boolean"
    ),
    ResponseSchema(
        name="escalation_rationale",
        description="A point-by-point explanation of your rationale for `escalation_indicated`"
    ),
    ResponseSchema(
        name="training_opportunity_indicated",
        description="A boolean indicating whether or not the `ai_reply` contained directly contradicting information, or was missing information, compared to the Human Reply.",
        type="boolean"
    ),
    ResponseSchema(
        name="training_opportunity_rationale",
        description="A point-by-point explanation of your rationale for `training_opportunity_indicated`"
    ),
]
accuracy_response_parser = StructuredOutputParser.from_response_schemas(accuracy_response_schemas)

accuracy_prompt = PromptTemplate(
    template="""
        Evaluate whether the AI reply to a user enquiry was accurate by examining a genuine Human Reply to the same query and relevant Knowledge Sources. Be detail oriented.

        Not all Knowledge Sources will be directly relevant, these are simple to assist.

        DO NOT consider any escalation to humans as part of your evaluation, just ignore it.

        It should be considered accurate as long as there are no incorrect statements.
        
        User enquiry = {original_message}

        AI reply = {ai_reply}

        Genuine human reply = {human_reply}

        Knowledge sources = {knowledge_sources}

        {format_instructions}
    """,
    input_variables=["ai_reply", "original_message", "human_reply", "knowledge_sources"],
    partial_variables={"format_instructions": accuracy_response_parser.get_format_instructions()}
)

chain = (
    RunnableParallel({
        "deflection_results": deflection_prompt | llm | deflection_response_parser,
        "prev_inputs": RunnablePassthrough()
    })
    | flatten
    | RunnableParallel({
        "accuracy_results": accuracy_prompt | llm | accuracy_response_parser,
        "prev_inputs": RunnablePassthrough()
    })
    | flatten
)

def evaluate_responses(replay_results: List[EeselHelpdeskReplayResult]) -> List[EeselQualityAssessmentResult]:
    results = []
    for element in replay_results:
        try:
            res = chain.invoke({
                "original_message": element.original_message,
                "ai_reply": element.ai_reply,
                "human_reply": element.human_reply,
                "knowledge_sources": element.knowledge_sources
            })
            assessment_result = EeselQualityAssessmentResult(
                accuracy=res["accuracy"],
                accuracy_rationale=res["accuracy_rationale"],
                knowledge_gap_indicated=res["knowledge_gap_indicated"],
                knowledge_gap_rationale=res["knowledge_gap_rationale"],
                escalation_indicated=res["escalation_indicated"],
                escalation_rationale=res["escalation_rationale"],
                training_opportunity_indicated=res["training_opportunity_indicated"],
                training_opportunity_rationale=res["training_opportunity_rationale"]
            )
            results.append(assessment_result)
        except Exception as e:
            print(f"Exception encountered: {e}, skipping")
    return results
