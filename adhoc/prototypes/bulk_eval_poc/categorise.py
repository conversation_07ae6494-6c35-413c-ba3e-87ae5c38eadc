from dataclasses import dataclass
from typing import Any, List, Optional
import json
from bulk_eval_poc.models import EeselCategorisationResponse, EeselHelpdeskTicket
from langchain_openai import ChatOpenAI
from langchain_core.output_parsers import Str<PERSON>utputParser
from langchain_core.prompts import PromptTemplate
from langchain.output_parsers import ResponseSchema, StructuredOutputParser
from langchain_core.runnables import RunnableParallel, RunnablePassthrough
from langchain.globals import set_debug
from langchain_community.callbacks import get_openai_callback

OUTER_CATEGORIES = {
    "General Platform":  "The user is inquiring about a platform access and organizations & workspaces. This includes a variety of questions related to accessing the platform through invitations, 2FA issues, navigating organizations or workspaces, or utilizing different functionalities within the platform. This is also the catch all attribute if a question does not fit into the rest, but choose other category if there is a plausible alternative.",
    "Creative Analytics": "The user is asking a question about the Creative Analytics feature. This includes issues or questions related to the analysis of creative performance, viewing reports, or understanding analytics data.",
    "Creative Scoring": "The user is asking a question about the Creative Scoring feature. This includes issues or questions related to scoring criteria, scorecards, or any questions about the Creative Scoring.",
    "Creative Studio": "The user is asking a question about the Creative Studio feature. This includes issues or questions related to creating, editing, or managing creative assets within the studio or getting/issues with project access.",
    "Creator Studio": "The user is asking a question about the Creator Studio feature. This includes issues or questions related to creator open for new work, payment issues, closing out a project, or if the user is indicating that they are available or seeking work opportunities.",
    "Integrations": "The user is inquiring about connecting, managing or any issues with ad accounts or media processing."
}

INNER_CATEGORIES = {
    "General Platform": {
        "Organization":  "The user is asking about managing or interacting with organizations within the platform, which can include setting up, editing, or navigating organizational settings.",
        "Workspace": "The user is inquiring about the workspaces within the platform, focusing on creating, accessing, or managing these collaborative environments.",
        "Notifications": "The user is asking about the notifications they receive from the platform, including setting preferences, understanding notifications, or troubleshooting notification issues.",
        "Locked Out/2FA": "The user is inquiring about being locked out of their account or dealing with two-factor authentication (2FA) issues, including resetting access or troubleshooting 2FA.",
        "SSO": "The user is inquiring about Single Sign-On (SSO) setup and troubleshooting, ensuring seamless login integration with other systems.",
        "Platform Access": "The user is asking about gaining access to the platform, including account creation, permissions, and sending, receiving, or managing invitations to access the platform or specific workspaces within it. Inquiries about access for other things, such as arbitrary pieces of information, are not counted as Platform Access.",
        "Data Export": "The user is asking about how to download data or a report at various spots in the platform.",
        "Feature Requests": "The user submitted a question or suggestion about a platform feature that doesn’t currently exist or is requesting the team to submit a feature request on their behalf"
    },
    "Integrations": {
        "Ad Accounts": "The user is inquiring about connecting and managing advertising accounts within the platform, including setup, processing, troubleshooting or any issues relating to managing ad accounts in a workspace.",
        "API":  "The user is asking about the Application Programming Interface (API), including how to use it, integration guides, and troubleshooting API issues.",
        "Feature Requests": "The user submitted a question or suggestion about a platform feature that doesn’t currently exist or is requesting the team to submit a feature request on their behalf"
    },
    "Creative Scoring": {
        "Executive Dashboard": "The user is asking about the Executive Dashboard, including how to access, customize, and interpret the dashboard.",
        "In Flight Scorecards": "The user is asking about In Flight Scorecards, including how to create, view, and analyze these ongoing performance scorecards.",
        "Ad Account Scorecards": "The user is inquiring about Ad Account Scorecards, including how to set up, view, and interpret scorecards for advertising accounts.",
        "Pre Flight Scorecards": "The user is asking about Pre Flight Scorecards, including how to create, review, and use these scorecards for planning.",
        "Criteria Management": "The user is inquiring about managing criteria for scoring, including setting, updating, and reviewing scoring criteria.",
        "Scoring Override": "The user is asking about overriding scoring results, including how to perform, review, and manage scoring overrides.",
        "Adherence Report": "The user is asking about Adherence Reports, including how to generate, view, and understand these reports.",
        "Impressions Adherence Report": "The user is asking about Impressions Adherence Reports, including how to generate, view, and understand these reports.",
        "Adoption Report": "The user is asking about Adoption Reports, including how to generate, view, and understand these reports.",
        "Diversity Report": "The user is asking about Diversity Reports, including how to generate, view, and analyze reports on diversity metrics within their creative assets.",
        "Feature Requests": "The user submitted a question or suggestion about a platform feature that doesn’t currently exist or is requesting the team to submit a feature request on their behalf"
    },
    "Creative Analytics": {
        "Insights": "The user is inquiring about insights derived from analytics, including how to generate, view, and use these insights for decision-making.",
        "KPIs": "The user is asking about Key Performance Indicators (KPIs), including how to set, track, and analyze KPIs within the platform.",
        "Element Report": "The user is inquiring about Element Reports, including how to generate, view, and interpret these reports for individual elements of their creative assets.",
        "Media Impact Report": "The user is inquiring about Media Impact Reports, including how to generate, view, and interpret these reports to understand the impact of their media assets.",
        "Individual Creative View": "The user is asking about viewing and analyzing individual creative assets, including how to access, review, and understand performance data for each asset.",
        "Creative Leaderboard": "The user is asking about the Creative Leaderboard, including how to access, view, and use the leaderboard to compare the performance of different creatives.",
        "Comparison Report": "The user is asking about Comparison Reports, including how to generate, view, and analyze reports comparing different sets of data.",
        "Creative Manager": "The user is inquiring about the Creative Manager tool, including how to manage, organize, and analyze creative assets.",
        "Feature Requests": "The user submitted a question or suggestion about a platform feature that doesn’t currently exist or is requesting the team to submit a feature request on their behalf"
    },
    "Creative Studio": { 
        "Project Kickoff": "The user is asking about initiating new projects, including how to set up, plan, and start a project in the platform.",
        "Completing Project": "The user is inquiring about the steps to finalize and complete a project, including ensuring all tasks are finished and submitting the final product.",
        "Brief": "The user is asking about creating and managing project briefs, including how to write, edit, and share briefs with the team.",
        "Draft Review": "The user is asking about reviewing draft versions of projects, including providing feedback, tracking changes, and approving drafts.",
        "Final Files": "The user is asking about finalizing and handling final project files, including exporting, sharing, and archiving the final outputs.",
        "Assets": "The user is inquiring about managing assets within projects, including uploading, organizing, and utilizing various media and resource files.",
        "Project Creation": "The user is inquiring about the steps to create a new project, including initial setup, defining objectives, and configuring project settings.",
        "Project Access": "The user is asking about accessing projects, including navigating to projects, managing access permissions, inviting collaborators to a project, including sending invitesand resolving access issues.",
        "Feature Requests": "The user submitted a question or suggestion about a platform feature that doesn’t currently exist or is requesting the team to submit a feature request on their behalf"
    },
    "Creator Studio": {
        "Available for Work (Creator)": "The user is inquiring about setting their availability status, including updating availability, managing requests, and responding to opportunities.",
        "Creator Payouts": "The user is asking about managing payouts for creators, including setting up payment methods, tracking earnings, and resolving payment issues.",
        "Creator Projects": "The user is inquiring about managing projects specifically for creators, including accepting projects, tracking progress, and delivering final products.",
        "Feature Requests": "The user submitted a question or suggestion about a platform feature that doesn’t currently exist or is requesting the team to submit a feature request on their behalf"
    }
}

# Flatten function
def flatten(*vars):
    flat = {}
    for var in vars:
        keys = [k for k in var]
        for key in keys:
            if isinstance(var[key], dict):
                flat.update(var[key])
            else:
                flat[key] = var[key]
    return flat

def override_and_move_categories(input_dict):
    modified_dict = input_dict.copy()

    if 'category' in modified_dict:
        modified_dict['outer_category'] = modified_dict['category']
        modified_dict['outer_category_rationale'] = modified_dict['category_rationale']

    modified_dict['categories'] = INNER_CATEGORIES.get(modified_dict.get('outer_category'), None)

    return modified_dict


# Setup LLM and prompts
llm = ChatOpenAI(model="gpt-4o", temperature=0)

inner_category_response_schemas = [
    ResponseSchema(
        name="category2",
        description="The most relevant categories for the ticket based on the user enquiry and fields.",
        type="string"
    ),
    ResponseSchema(
        name="category2_rationale",
        description="A point-by-point explanation of your rationale for each selected category, including quotes.",
        type="string"
    )
]
inner_category_response_parser = StructuredOutputParser.from_response_schemas(inner_category_response_schemas)

inner_category_prompt = PromptTemplate(
    template="""
        Review the user enquiry and fields to identify one or more correct categories for the ticket, depending on whether multiple categories can apply (defined below). Provide a detailed rationale for your choice.
        You must only choose from the possible categories provided below and the values must match exactly.

        Ignore any parts of the message that looks like an email signature or email privacy warning.

        User enquiry = {original_message}

        Possible categories = {categories}

        {format_instructions}
    """,
    input_variables=["original_message", "categories"],
    partial_variables={"format_instructions": inner_category_response_parser.get_format_instructions()}
)

outer_category_response_schemas = [
    ResponseSchema(
        name="category",
        description="The most relevant category for the ticket based on the user enquiry.",
        type="string"
    ),
    ResponseSchema(
        name="category_rationale",
        description="A point-by-point explanation of your rationale for the selected category.",
        type="string"
    )
]
outer_category_response_parser = StructuredOutputParser.from_response_schemas(outer_category_response_schemas)

outer_category_prompt = PromptTemplate(
    template="""
        Review the user enquiry and fields to identify the most relevant category for the ticket, depending on whether multiple categories can apply (defined below). Provide a detailed rationale for your choice.
        You must only choose from the possible categories provided below and must match exactly.

        Ignore any parts of the message that looks like an email signature or email privacy warning.

        User enquiry = {original_message}

        Possible categories = {categories}

        When evaluating which category is most appropriate, you must also take into account the possible subcategories which can provide additional context on each possible category.
        If it clearly fits into a subcategory, you should consider it's parent category as a good candidate for the category.
        The subcategories are: {sub_categories}

        {format_instructions}
    """,
    input_variables=["original_message", "categories", "sub_categories"],
    partial_variables={"format_instructions": outer_category_response_parser.get_format_instructions()}
)

def second_pass(input):
    if input.get('pass_through', None) is not None:
        return RunnablePassthrough()

    return RunnableParallel({
        "inner_category_results": inner_category_prompt | llm | inner_category_response_parser,
        "prev_inputs": RunnablePassthrough()
    })

chain = (
    RunnableParallel({
        "category_results": outer_category_prompt | llm | outer_category_response_parser,
        "prev_inputs": RunnablePassthrough()
    })
    | flatten
    | override_and_move_categories
    | second_pass
    | flatten
)

import traceback

def remove_last_group_of_gt_lines(input_string):
    lines = input_string.split('\n')
    last_gt_index = None

    # Iterate from the end to find the last group of '>' lines
    for i in range(len(lines) - 1, -1, -1):
        if lines[i].strip().startswith('>'):
            last_gt_index = i
        elif last_gt_index is not None:
            # Once we find a non-'>' line after a '>' group, break
            break

    # If a group was found, remove it
    if last_gt_index is not None:
        while last_gt_index < len(lines) and lines[last_gt_index].strip().startswith('>'):
            lines.pop(last_gt_index)

    return '\n'.join(lines)

import pprint

def categorize_tickets(replay_results: List[EeselHelpdeskTicket]) -> List[EeselCategorisationResponse]:
    results = []
    total_cost = 0
    for element in replay_results:

        # pprint.pprint(element)

        if not element.messages or len(element.messages) == 0:
            continue

        try:
            with get_openai_callback() as cb:
                res = chain.invoke({
                    "original_message": remove_last_group_of_gt_lines(element.messages[0]),
                    "categories": OUTER_CATEGORIES,
                    "sub_categories": INNER_CATEGORIES
                })

                total_cost += cb.total_cost
                
                result = EeselCategorisationResponse(
                    level1_category=res["outer_category"],
                    level1_category_rationale=res["outer_category_rationale"],
                    level2_category=res.get("category2", None),
                    level2_category_rationale=res["category2_rationale"],
                    original_message=element.messages[0],
                    metadata=element.metadata,
                    cost=cb.total_cost
                )
                print("Processed item.")

                results.append(result)

        except Exception as e:
            print(f"Exception encountered: {e}, skipping")
            traceback.print_exc()
    return results

