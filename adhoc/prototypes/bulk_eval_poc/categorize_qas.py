from typing import List, Dict, Any
from langchain_openai import ChatOpenAI
from langchain_core.prompts import PromptTemplate
from langchain.output_parsers import ResponseSchema, StructuredOutputParser
from langchain_community.callbacks import get_openai_callback
import os
import traceback
import logging
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor, as_completed
import sys
import json

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Initialize the LLM
llm = ChatOpenAI(model="gpt-4o-mini", temperature=0, api_key=os.environ.get("OPENAI_API_KEY"))

# Define the response schema for category classification
category_response_schemas = [
    ResponseSchema(
        name="category",
        description="The category of the Q&A. One of: Cancellations & refunds, Purchasing a logo, Logo licenses, Other.",
        type="string"
    )
]
category_response_parser = StructuredOutputParser.from_response_schemas(category_response_schemas)

# Define the prompt template for categorizing each Q&A
category_prompt = PromptTemplate(
    template="""
        Classify the following question and answer into one of the categories below:

        Categories:
        - Cancellations & refunds
        - Purchasing a logo
        - Logo licenses
        - Other

        Q: {question}
        A: {answer}

        Category:
        {format_instructions}
    """,
    input_variables=["question", "answer"],
    partial_variables={"format_instructions": category_response_parser.get_format_instructions()}
)

def categorize_qa(qa: Dict[str, str]) -> Dict[str, Any]:
    try:
        with get_openai_callback() as cb:
            res = (category_prompt | llm | category_response_parser).invoke({
                "question": qa['question'],
                "answer": qa['answer']
            })
            category = res["category"]
            logging.info(f"Categorized Q&A: '{qa['question']}' as '{category}'")
            return {"category": category, "qa": qa}
    except KeyError as e:
        logging.error(f"KeyError encountered while categorizing Q&A: {e}, skipping")
    except Exception as e:
        logging.error(f"Exception encountered while categorizing Q&A: {e}, skipping")
        traceback.print_exc()
    return {"category": "Other", "qa": qa}

def evaluate_qas(clusters: Dict[str, List[Dict[str, str]]]) -> Dict[str, List[Dict[str, str]]]:
    categorized_qas = {
        "Cancellations & refunds": [],
        "Purchasing a logo": [],
        "Logo licenses": []
    }
    with ThreadPoolExecutor(max_workers=20) as executor:
        futures = []
        for cluster_idx, qa_list in clusters.items():
            for qa in qa_list:
                futures.append(executor.submit(categorize_qa, qa))
        
        for future in as_completed(futures):
            result = future.result()
            if result:
                category = result["category"]
                qa = result["qa"]
                if category in categorized_qas:
                    categorized_qas[category].append(qa)
    return categorized_qas

def write_markdown_output(categorized_qas: Dict[str, List[Dict[str, str]]], output_dir: str):
    category_to_filename = {
        "Cancellations & refunds": "cancellations_refunds.md",
        "Purchasing a logo": "purchasing_logo.md",
        "Logo licenses": "logo_licenses.md"
    }
    
    for category, qas in categorized_qas.items():
        filename = os.path.join(output_dir, category_to_filename.get(category, "other.md"))
        with open(filename, 'w') as md_file:
            for qa in qas:
                md_file.write(f"Q: {qa['question']}\n")
                md_file.write(f"A: {qa['answer']}\n\n")
        logging.info(f"Markdown output written to {filename}")

def main(filename: str, output_dir: str):
    try:
        logging.info(f"Opening file {filename}")
        with open(filename, 'r') as file:
            data = json.load(file)
            clusters = data.get("clusters", {})
            if not clusters:
                logging.warning("No clusters found in the provided file.")
                return

            logging.info(f"Found {len(clusters)} clusters in the file.")
            categorized_qas = evaluate_qas(clusters)
            
            write_markdown_output(categorized_qas, output_dir)
            
            logging.info("Categorization complete. Summary:")
            for category, qas in categorized_qas.items():
                logging.info(f"Category '{category}': {len(qas)} Q&A pairs")
    
    except Exception as e:
        logging.error(f"Error processing file {filename}: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("Usage: python categorize_qas.py <input_file_path> <output_directory>")
        sys.exit(1)

    input_file_path = sys.argv[1]
    output_directory = sys.argv[2]
    
    if not os.path.exists(output_directory):
        os.makedirs(output_directory)
    
    main(input_file_path, output_directory) 