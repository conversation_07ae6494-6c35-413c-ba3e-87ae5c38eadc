import json
import os
import argparse
from bulk_eval_poc.categorise import categorize_tickets
from bulk_eval_poc.bulk_eval import evaluate_responses
from bulk_eval_poc.replay import replay_helpdesk_tickets
from bulk_eval_poc.models import EeselHelpdeskReplayResult, EeselHelpdeskTicket, dump_dataclass_to_csv, load_csv_to_dataclass
from bulk_eval_poc.learn import learn_tickets

from bulk_eval_poc.utils.intercom.intercom_exporter import IntercomFetcher
from bulk_eval_poc.utils.intercom.intercom_filter import filter_conversations as intercom_filter_conversations
from bulk_eval_poc.utils.intercom.intercom_transformer import transform_conversations as intercom_transform_conversations

from bulk_eval_poc.utils.zendesk.zendesk_exporter import ZendeskFetcher
from bulk_eval_poc.utils.zendesk.zendesk_filter import filter_tickets as zendesk_filter_tickets
from bulk_eval_poc.utils.zendesk.zendesk_transformer import transform_zendesk_tickets

from bulk_eval_poc.utils.front.front_exporter import FrontAPIFetcher
from bulk_eval_poc.utils.front.front_transformer import transform_front_conversations

from bulk_eval_poc.utils.freshdesk.freshdesk_exporter import FreshdeskExporter

from typing import List

def save_to_file(data, file_path):
    with open(file_path, 'w') as file:
        json.dump(data, file, indent=4)

def load_from_file(file_path):
    if os.path.exists(file_path):
        with open(file_path, 'r') as file:
            return json.load(file)
    return None

def load_categories_from_file(file_path):
    try:
        with open(file_path, 'r') as file:
            return json.load(file)
    except Exception as e:
        print(f"Error loading categories from {file_path}: {e}")
        return None

def get_intercom_helpdesk_tickets(intercom_app_id, intercom_access_token, max_tickets_to_retrieve):
    checkpoint_file_raw_convos = f"./tmp/checkpoint_conversations_{intercom_app_id}.json"

    # Step 1: Fetch conversations or load from checkpoint
    conversations = load_from_file(checkpoint_file_raw_convos)
    if conversations is None:
        intercom_fetcher = IntercomFetcher(intercom_access_token)
        conversations = intercom_fetcher.fetch_conversations(limit=max_tickets_to_retrieve)
        save_to_file(conversations, checkpoint_file_raw_convos)
    
    # Step 2: Filter conversations
    print(str(len(conversations)))
    filtered_conversations = intercom_filter_conversations(conversations)
    print(str(len(filtered_conversations)))
    
    # Step 3: Transform conversations
    transformed_tickets = intercom_transform_conversations(filtered_conversations)
    print(str(len(transformed_tickets)))
    return transformed_tickets

def get_zendesk_helpdesk_tickets(zendesk_domain, zendesk_access_token, max_tickets_to_retrieve):
    checkpoint_file_raw_convos = f"./tmp/checkpoint_conversations_{zendesk_domain}.json"

    # Step 1: Fetch conversations or load from checkpoint
    conversations = load_from_file(checkpoint_file_raw_convos)
    if conversations is None:
        zendesk_fetcher = ZendeskFetcher(zendesk_domain, zendesk_access_token)
        conversations = zendesk_fetcher.fetch_tickets_with_comments(limit=max_tickets_to_retrieve)
        save_to_file(conversations, checkpoint_file_raw_convos)
    
    # Step 2: Filter conversations
    filtered_conversations = zendesk_filter_tickets(conversations)

    # Step 3: Transform conversations
    transformed_tickets = transform_zendesk_tickets(filtered_conversations, [
        "plexus: Lead Purpose",
        "plexus: AGB valide",
        "plexus: Lead Type",
    ])

    return transformed_tickets

def get_front_helpdesk_tickets(front_api_key, front_company_key, max_tickets_to_retrieve):
    checkpoint_file_raw_convos = f"./tmp/checkpoint_conversations_front_{front_company_key}.json"

    # Step 1: Fetch conversations or load from checkpoint
    conversations = load_from_file(checkpoint_file_raw_convos)
    if conversations is None:
        front_fetcher = FrontAPIFetcher(front_api_key)
        conversations = front_fetcher.fetch_filtered_interactions(limit=max_tickets_to_retrieve)
        save_to_file(conversations, checkpoint_file_raw_convos)
    
    # Step 3: Transform conversations
    transformed_tickets = transform_front_conversations(conversations)

    return transformed_tickets

def get_freshdesk_helpdesk_tickets(freshdesk_api_key, freshdesk_domain, max_tickets_to_retrieve):
    checkpoint_file_raw_convos = f"./tmp/checkpoint_conversations_freshdesk_{freshdesk_domain}.json"

    # Step 1: Fetch conversations or load from checkpoint
    conversations = load_from_file(checkpoint_file_raw_convos)
    if conversations is None:
        freshdesk_fetcher = FreshdeskExporter(freshdesk_api_key, freshdesk_domain)
        conversations = freshdesk_fetcher.get_recent_tickets(max_tickets_to_retrieve)
        save_to_file(conversations, checkpoint_file_raw_convos)
    
    return conversations

def load_tickets_from_json(file_path: str) -> List[EeselHelpdeskTicket]:
    """Load tickets from a JSON file and return a list of EeselHelpdeskTicket instances."""
    try:
        with open(file_path, 'r') as file:
            tickets_data = json.load(file)
            return [EeselHelpdeskTicket(**{k: v for k, v in ticket.items() if k != 'ticket_id'}) for ticket in tickets_data]
    except Exception as e:
        print(f"Error loading tickets from {file_path}: {e}")
        return []

def main():
    parser = argparse.ArgumentParser(description='Helpdesk Ticket Processing Tool')
    parser.add_argument('--product', choices=['intercom', 'zendesk', 'front', 'freshdesk'], required=True, help='The product to process tickets for.')
    parser.add_argument('--auth_key', required=True, help='The authentication key for the product.')
    parser.add_argument('--eesel_auth_key', help='The authentication key for eesel. Required if using replay.')

    parser.add_argument('--domain', help='The domain for Zendesk (required if product is zendesk).')
    parser.add_argument('--intercom_id', help='The intercom app id (required if product is intercom).')
    parser.add_argument('--max_items_to_extract', type=int, default=35, help='The maximum number of items to extract.')
    parser.add_argument('--max_items_to_learn', type=int, default=35, help='The maximum number of items to learn.')
    parser.add_argument('--unique_id', required=True, help='The customer ID.')
    parser.add_argument('--learn', action='store_true', help='Run in learn mode.')
    parser.add_argument('--replay', action='store_true', help='Run in replay mode.')
    parser.add_argument('--evaluate', action='store_true', help='Run in evaluate mode.')
    parser.add_argument('--categories_file', help='Path to the categories JSON file.')
    parser.add_argument('--categorise', action='store_true', help='Run in categorise mode')

    parser.add_argument('--freshdesk_domain', help='The domain for Freshdesk (required if product is freshdesk).')

    parser.add_argument('--tickets_file', help='Path to the JSON file containing helpdesk tickets.')

    args = parser.parse_args()

    get_output_filename = lambda x : f"./{args.unique_id}_{x}_output.csv"

    tickets = None

    if args.tickets_file:
        tickets = load_tickets_from_json(args.tickets_file)
    elif args.product == "intercom":
        tickets = get_intercom_helpdesk_tickets(args.intercom_id, args.auth_key, args.max_items_to_extract)
    elif args.product == "zendesk":
        if not args.domain:
            parser.error("Zendesk requires both an authentication token and a domain.")
        tickets = get_zendesk_helpdesk_tickets(args.domain, args.auth_key, args.max_items_to_extract)
    elif args.product == "front":
        tickets = get_front_helpdesk_tickets(args.auth_key, args.unique_id, args.max_items_to_extract)
    elif args.product == "freshdesk":
        if not args.freshdesk_domain or not args.auth_key:
            parser.error("Freshdesk requires both an API key and a domain.")
        tickets = get_freshdesk_helpdesk_tickets(args.auth_key, args.freshdesk_domain, args.max_items_to_extract)

    categories1 = {
        "General Inquiry": "Questions about general information or services",
        "Warranty or Refunds": "Issues related to technical difficulties or malfunctions",
        "Billing": "Questions or issues regarding billing and payments",
        "Account Management": "Requests related to account settings, password resets, etc.",
        "Feedback": "Customer feedback and suggestions",
        "Complaint": "Customer complaints and grievances",
        "Other": "Any other inquiries"
    }
    categories2 = {}

    if args.categories_file:
        loaded_categories = load_categories_from_file(args.categories_file)
        if loaded_categories:
            categories1 = loaded_categories[0]
            categories2 = loaded_categories[1] if len(loaded_categories) > 1 else {}

    if args.learn:
        kb_and_classifications = learn_tickets(tickets[:args.max_items_to_learn], categories1, categories2)
        dump_dataclass_to_csv(kb_and_classifications, get_output_filename("learn"))

    if args.replay:
        replay_results = replay_helpdesk_tickets(tickets[:args.max_items_to_learn], args.eesel_auth_key)
        dump_dataclass_to_csv(replay_results, get_output_filename("replay"))

    if args.evaluate:
        replay_filename = get_output_filename("replay")
        if not os.path.exists(replay_filename):
            raise FileNotFoundError("Replay file not found. Please run replay mode first.")
        
        replays = load_csv_to_dataclass(replay_filename, EeselHelpdeskReplayResult)
        eval_results = evaluate_responses(replays[:args.max_items_to_learn])
        dump_dataclass_to_csv(eval_results, get_output_filename("evaluations"))

    if args.categorise:
        categorisation = categorize_tickets(tickets[:args.max_items_to_learn])
        dump_dataclass_to_csv(categorisation, get_output_filename("categorisation"))


if __name__ == "__main__":
    main()
