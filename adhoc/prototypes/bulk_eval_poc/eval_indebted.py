from dataclasses import dataclass
from typing import Dict, List, Optional
import json

from bulk_eval_poc.models import EeselHelpdeskReplayResult, EeselHelpdeskTicket, EeselQualityAssessmentResult, IndebtedHelpdeskResult, dict_to_dataclass, dump_dataclass_to_csv
from langchain_openai import ChatOpenAI
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import PromptTemplate
from langchain.output_parsers import ResponseSchema, StructuredOutputParser
from langchain_core.runnables import RunnableParallel, RunnablePassthrough
from langchain.globals import set_debug

from langchain_community.callbacks import get_openai_callback

# set_debug(True)
# flatten function
def flatten(*vars):
    flat = {}
    for var in vars:
        keys = [k for k in var]
        for key in keys:
            if isinstance(var[key], dict):
                flat.update(var[key])
            else:
                flat[key] = var[key]
    return flat

# Setup LLM and prompts
llm = ChatOpenAI(model="gpt-4o", temperature=0)

accuracy_response_schemas = [
    ResponseSchema(
        name="knowledge_gap_indicated",
        description="A boolean indicating whether 'eesel AI' was unable to help due to a lack of relevant knowledge or content snippets or if it directly contradicted any other parts of the message exchange",
        type="boolean"
    ),
    ResponseSchema(
        name="knowledge_gap_rationale",
        description="A point-by-point explanation of your rationale for `knowledge_gap_indicated`. Include comprehensive details and example quotes from the ticket of what the specific gap is, so it can be reviewed and addressed."
    ),
    ResponseSchema(
        name="knowledge_snippet_qa",
        description="null if knowledge_gap_indicated is false, otherwise a string representing knowledge articles you can infer from subsequent human responses, in Q&A format, you are able to identify and extract: Q: {question}\nA: {answer}\n\nQ: {question}\nA: {answer}\n\n. These MUST ONLY contain any instructions or information that is explicitly mentioned by a human in the ticket"
    ),
    ResponseSchema(
        name="knowledge_snippet_qa_rationale",
        description="a point-by-point explanation of your rationale for `knowledge_snippet_qa`, including why it would be appropriate as a public or internal knowledge base article. You must provide incorporate quotes from the human support agent to validate the accuracy."
    ),
    ResponseSchema(
        name="action_required",
        description="True if it appears that the human agent had to take action in another system, like manually reseting a password, provisioning an account, etc, in order to solve the ticket. It is acceptable for the help seeker to take action, but not the human support agent.",
        type="boolean"
    ),
    ResponseSchema(
        name="action_required_rationale",
        description="a point-by-point explanation of your rationale for `action_required."
    ),
    ResponseSchema(
        name="deflection_opportunity",
        description="A boolean indicating whether the AI would have been able to, in theory, completely resolve (`complete_deflection`) the ticket the knowledge in `knowledge_snippet_qa` combined with any AI response would have resulted in `complete_deflection`. `action_required` must also be false for this to be true, since the AI cannot take an action.",
        type="boolean"
    ),
    ResponseSchema(
        name="deflection_opportunity_rationale",
        description="A point-by-point explanation of your rationale for `deflection_opportunity`"
    ),
    ResponseSchema(
        name="helpful",
        description="A boolean indicating whether or not 'eesel AI' provided highly relevant and useful information.",
        type="boolean"
    ),
    ResponseSchema(
        name="helpful_rationale",
        description="A point-by-point explanation of your rationale for `helpful_rationale`"
    ),
    ResponseSchema(
        name="complete_deflection",
        description="A boolean indicating whether or not 'eesel AI' reply completely solved the issue in the initial ticket, indicated by a lack of additional human inputs indicating that help was still needed on the original issue.",
        type="boolean"
    ),
    ResponseSchema(
        name="complete_deflection_rationale",
        description="A point-by-point explanation of your rationale for `complete_deflection`"
    )
]
accuracy_response_parser = StructuredOutputParser.from_response_schemas(accuracy_response_schemas)

accuracy_prompt = PromptTemplate(
    template="""
        Assess the quality of an AI reply to a user's enquiry was Helpful and Accurate. Be detailed and accurate and use subsequent human replies to improve the knowledge base used by the AI.

        If you find that knowledge was used that should be in a knowledge base, you should generate Q&A style snippets for adding to the knowledge base.
        
        ALWAYS focus on information that can be generalized and applicable to a wider audience.
        DO NOT include information specific to this exchange, such as the subject/title of the ticket, the created date, or any other metadata.
        DO NOT create candidate articles based on details that apply only to this specific exchange.
        DO NOT say anything other than the Q&A items.
        ALWAYS provide sufficient detail and relevant keywords so it can easily be retrieved in a search system.
        DO NOT refer to the email or any specifics in any candidate articles.
        Articles should be written as if they were to appear on a public knowledge base.
        NEVER say things like "According to the information provided", "Based on the ticket", etc.

        DO NOT extract knowledge from lines in the message starting with "Unknown".

        DO NOT extract knowledge that is extremely temporal, like current stock status.
        DO NOT extract knowledge that is extremely general, and not specific to the company, processes or products.

        DO NOT consider any escalation to humans as part of your evaluation, just ignore it.
        
        -----

        The first message is the initial message from the help seeker, it contains a summary of the request and relevant fields.

        Subsequent messages represent a back and forth.

        You the enquiry comments are chronological. Other replies are from humans.

        If there is no AI reply (messages starting with "eesel AI reply"), treat it as unhelpful.

        Initial user enquiry = {initial_ticket}

        Comments = {comments}

        {format_instructions}
    """,
    input_variables=["initial_ticket", "comments"],
    partial_variables={"format_instructions": accuracy_response_parser.get_format_instructions()}
)

chain = accuracy_prompt | llm | accuracy_response_parser

def evaluate_responses(messages_list: List[EeselHelpdeskTicket]) -> List[EeselQualityAssessmentResult]:
    results = []
    cost = 0
    failed_times = 0
    for ticket in messages_list:
        pprint.pprint(ticket.messages)

        is_resolved = any("Resolution: Done" in x for x in ticket.messages)

        if not is_resolved:
            print("not resolved")
            continue

        ticket.messages = ["AI response: " + message if "eesel AI response" in "Human reply: " + message else message for message in ticket.messages]
        contains_ai_reply = any("eesel AI response" in message for message in ticket.messages)

        try:
            with get_openai_callback() as cb:
                res = chain.invoke({
                    "initial_ticket": json.dumps(ticket.messages[0], indent=4),
                    "comments": json.dumps(ticket.messages[1:], indent=4)
                })

                if not contains_ai_reply:
                    res["helpful"] = False
                    res["helpful_rationale"] = "N/A"

                res["original_ticket"] = json.dumps(ticket.messages[0], indent=4)
                res["comment_thread"] = json.dumps(ticket.messages[1:], indent=4)

                results.append(res)

                cost += cb.total_cost
        except Exception as e:
            print(f"Exception encountered: {e}, skipping")

    return {"eval_results": results, "total_cost": cost}

def load_and_evaluate(json_file_path: str) -> List[EeselQualityAssessmentResult]:
    with open(json_file_path, 'r') as file:
        data = json.load(file)

    dataclasses = [dict_to_dataclass(EeselHelpdeskTicket, item) for item in data]
    
    return evaluate_responses(dataclasses[500:])

import pprint

results = load_and_evaluate("./tmp/indebted_transformed.json")

print(json.dumps(results, indent=4))

dcs = [dict_to_dataclass(IndebtedHelpdeskResult, t) for t in results["eval_results"]]
dump_dataclass_to_csv(dcs, "out3.csv")

# print(json.dumps(results, indent=4))
