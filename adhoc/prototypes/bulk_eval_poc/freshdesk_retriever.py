import requests
import json
import argparse
import sys
from typing import List
import logging  # Import logging module
import concurrent.futures  # Import concurrent.futures for parallel processing

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Assuming EeselHelpdeskTicket is defined elsewhere and imported here
# from eesel_helpdesk import EeselHelpdeskTicket

class EeselHelpdeskTicket:
    def __init__(self, metadata, messages):
        self.metadata = metadata
        self.messages = messages


    def to_dict(self):
        return {
            "metadata": self.metadata,
            "messages": self.messages,
        }

def parse_arguments():
    parser = argparse.ArgumentParser(description='Fetch Freshdesk tickets and transform them into EeselHelpdeskTicket objects.')
    parser.add_argument('--api_key', required=True, help='Your Freshdesk API key.')
    parser.add_argument('--domain', required=True, help='Your Freshdesk domain (e.g., yourcompany.freshdesk.com).')
    parser.add_argument('--file', required=True, help='File containing newline-separated ticket IDs.')
    parser.add_argument('--output', required=False, help='Output file to save the transformed tickets as JSON.', default='output_tickets.json')
    return parser.parse_args()

def read_ticket_ids(file_path: str) -> List[str]:
    try:
        logging.info(f"Reading ticket IDs from file: {file_path}")  # Log the file being read
        with open(file_path, 'r') as file:
            ticket_ids = [line.strip() for line in file if line.strip()]
        logging.info(f"Found {len(ticket_ids)} ticket IDs.")  # Log the number of ticket IDs found
        return ticket_ids
    except Exception as e:
        logging.error(f"Error reading ticket IDs from file: {e}")  # Log the error
        sys.exit(1)

def fetch_ticket_conversations(api_key: str, domain: str, ticket_id: str) -> List[dict]:
    url = f"https://{domain}/api/v2/tickets/{ticket_id}/conversations"
    headers = {
        "Content-Type": "application/json"
    }
    try:
        logging.info(f"Fetching conversations for Ticket ID: {ticket_id}")  # Log the ticket ID being processed
        response = requests.get(url, auth=(api_key, 'X'), headers=headers)
        response.raise_for_status()
        conversations = response.json()
        logging.info(f"Fetched {len(conversations)} conversations for Ticket ID: {ticket_id}")  # Log the number of conversations fetched
        return conversations
    except requests.exceptions.HTTPError as http_err:
        logging.error(f"HTTP error occurred while fetching ticket {ticket_id}: {http_err}")  # Log HTTP errors
    except Exception as err:
        logging.error(f"Error occurred while fetching ticket {ticket_id}: {err}")  # Log other errors
    return []

def fetch_ticket_details(api_key: str, domain: str, ticket_id: str) -> dict:
    url = f"https://{domain}/api/v2/tickets/{ticket_id}"
    headers = {
        "Content-Type": "application/json"
    }
    try:
        logging.info(f"Fetching details for Ticket ID: {ticket_id}")  # Log the ticket ID being processed
        response = requests.get(url, auth=(api_key, 'X'), headers=headers)
        response.raise_for_status()
        ticket_details = response.json()
        logging.info(f"Fetched details for Ticket ID: {ticket_id}")  # Log successful fetch
        logging.debug(f"Ticket details: {ticket_details}")  # Log the fetched details
        return ticket_details
    except requests.exceptions.HTTPError as http_err:
        logging.error(f"HTTP error occurred while fetching ticket {ticket_id}: {http_err}")  # Log HTTP errors
        logging.error(f"Response status code: {response.status_code}, Response content: {response.text}")  # Log response details
    except Exception as err:
        logging.error(f"Error occurred while fetching ticket {ticket_id}: {err}")  # Log other errors
    logging.warning(f"No details found for Ticket ID: {ticket_id}. Check if the ticket exists or if the API key has the necessary permissions.")  # Additional warning
    return {}

def transform_to_eesel_ticket(ticket_id: str, conversations: List[dict], ticket_details: dict) -> EeselHelpdeskTicket:
    messages = []
    subject = ticket_details.get('subject', 'No Subject')
    description = ticket_details.get('description_text', 'No Description')
    
    # Add subject and description at the start of the messages array
    messages.append(f"Subject: {subject}")
    messages.append(f"Description: {description}")
    
    for convo in conversations:
        if not convo.get('private', False):
            author = convo.get('author', {}).get('name', 'Unknown')
            body_text = convo.get('body_text', '').strip()
            if body_text:
                message_content = f"{author}: {body_text}"
                messages.append(message_content)
    
    # Update to include ticket_id in a metadata field
    return EeselHelpdeskTicket({
            "ticket_id": ticket_id
    }, messages)

def main():
    args = parse_arguments()
    ticket_ids = read_ticket_ids(args.file)
    transformed_tickets = []

    logging.info(f"Starting processing for {len(ticket_ids)} tickets.")

    # Use ThreadPoolExecutor for parallel processing
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        # Submit conversation fetching tasks and map ticket_id to their futures
        conversation_futures = {
            ticket_id: executor.submit(fetch_ticket_conversations, args.api_key, args.domain, ticket_id)
            for ticket_id in ticket_ids
        }

        # Submit details fetching tasks and map ticket_id to their futures
        details_futures = {
            ticket_id: executor.submit(fetch_ticket_details, args.api_key, args.domain, ticket_id)
            for ticket_id in ticket_ids
        }

        for ticket_id in ticket_ids:
            logging.info(f"Processing Ticket ID: {ticket_id}")

            # Retrieve the conversation results
            conv_future = conversation_futures.get(ticket_id)
            if conv_future is None:
                logging.warning(f"No conversation future found for Ticket ID: {ticket_id}")
                continue

            try:
                conversations = conv_future.result()
                logging.debug(f"Conversations for Ticket ID {ticket_id}: {conversations}")
            except Exception as e:
                logging.error(f"Error fetching conversations for Ticket ID {ticket_id}: {e}")
                continue

            # Retrieve the details future
            details_future = details_futures.get(ticket_id)
            if details_future is None:
                logging.warning(f"No details future found for Ticket ID: {ticket_id}")
                continue

            try:
                ticket_details = details_future.result()
                logging.debug(f"Details for Ticket ID {ticket_id}: {ticket_details}")
            except Exception as e:
                logging.error(f"Error fetching details for Ticket ID {ticket_id}: {e}")
                continue

            if conversations and ticket_details:
                eesel_ticket = transform_to_eesel_ticket(ticket_id, conversations, ticket_details)
                transformed_tickets.append(eesel_ticket.to_dict())
                logging.info(f"Transformed Ticket ID: {ticket_id} into EeselHelpdeskTicket.")
            else:
                logging.warning(f"No conversations or details found for Ticket ID: {ticket_id}")

    # Write the transformed tickets to the JSON file
    if transformed_tickets:
        output_path = args.output  # Use the output path from CLI arguments
        try:
            with open(output_path, 'w') as f:
                json.dump(transformed_tickets, f, indent=4)
            logging.info(f"Successfully wrote transformed tickets to {output_path}.")
        except Exception as e:
            logging.error(f"Failed to write transformed tickets to {output_path}: {e}")
    else:
        logging.warning("No transformed tickets to write to the output file.")

if __name__ == "__main__":
    main()
