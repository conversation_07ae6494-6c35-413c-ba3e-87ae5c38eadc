import json
from typing import Any, List
from bulk_eval_poc.models import EeselHelpdeskTicket, EeselTrainingResponse
from langchain_openai import ChatOpenAI
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import PromptTemplate
from langchain.output_parsers import ResponseSchema, StructuredOutputParser
from langchain_core.runnables import RunnableParallel, RunnablePassthrough
from langchain.globals import set_debug

from langchain_community.callbacks import get_openai_callback
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# flatten -- useful for RunnablePassthrough objects.
def flatten(*vars):
    flat = {}
    for var in vars:
        keys = [k for k in var]
        for key in keys:
            if isinstance(var[key], dict):
                flat.update(var[key])
            else:
                flat[key] = var[key]
    return flat

set_debug(False)

llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)

# -------------------------------------------------------------------------------
# deflection
# -------------------------------------------------------------------------------

def get_training_response_parser(categories1: List[Any], categories2: List[Any]):
    formatted_categories_1 = json.dumps(categories1,indent=4)
    formatted_categories_2 = json.dumps(categories2,indent=4)
    training_response_schemas = [
        ResponseSchema(
            name="knowledge_used",
            description="A boolean representing whether or not the support agent used information to help the customer that would be found in a knowledge base, and would not require any customer data lookup or action.",
            type="boolean"
        ),
        ResponseSchema(
            name="knowledge_used_rationale",
            description="a point-by-point explanation of your rationale for `knowledge_used`. Use quotes and explain why it's knowledge that would be in a knowledge base, as opposed to another real time system."
        ),
        ResponseSchema(
            name="knowledge_snippet_qa",
            description="null if knowledge_used is false, otherwise a string representing knowledge articles, in Q&A format, you are able to identify and extract: Q: {question}\nA: {answer}\n\nQ: {question}\nA: {answer}\n\n"
        ),
        ResponseSchema(
            name="knowledge_snippet_qa_rationale",
            description="a point-by-point explanation of your rationale for `knowledge_snippet_qa`, including why it would be appropriate as a public or internal knowledge base article."
        ),
        ResponseSchema(
            name="deflection_opportunity",
            description="A boolean indicating whether the AI would have been able to, in theory, completely resolve the ticket using the knowledge in `knowledge_snippet_qa` combined with any AI response. `action_required` must also be false for this to be true, since the AI cannot take an action.",
            type="boolean"
        ),
        ResponseSchema(
            name="deflection_opportunity_rationale",
            description="A point-by-point explanation of your rationale for `deflection_opportunity`"
        ),
        ResponseSchema(
            name="category",
            description=f"A comma separated list of all categories that apply to the customer support query. Only choose highly relevant categories. Don't include the fallback category if others apply. You must only the following possible values and the instructions for when they apply: \n{formatted_categories_1}.",
            type="string"
        ),
        ResponseSchema(
            name="category_rationale",
            description="a point-by-point explanation of your rationale for `category`, detailing why the query fits into each selected category."
        ),
        ResponseSchema(
            name="category_2",
            description=f"A comma separated list of all categories that apply to the customer support query. Only choose highly relevant categories. Don't include the fallback category if others apply. You must only the following possible values and the instructions for when they apply: \n{formatted_categories_2}.",
            type="string"
        ),
        ResponseSchema(
            name="category_2_rationale",
            description="a point-by-point explanation of your rationale for `category`, detailing why the query fits into each selected category."
        )
    ]

    training_response_parser = StructuredOutputParser.from_response_schemas(training_response_schemas)

    return training_response_parser


def get_training_prompt(categories1: dict, categories2: dict):
    return PromptTemplate(
        template="""
            Your job is to review support ticket interactions between a customer and a customer support rep and determine whether or not the customer support rep used knowledge to respond that should be made available in a knowledge base.

            If you find that knowledge was used that should be in a knowledge base, you should generate Q&A style snippets for adding to the knowledge base.
            
            ALWAYS focus on information that can be generalized and applicable to a wider audience.
            DO NOT include information specific to this exchange, such as the subject/title of the ticket, the created date, or any other metadata.
            DO NOT create candidate articles based on details that apply only to this specific exchange.
            DO NOT say anything other than the Q&A items.
            ALWAYS provide sufficient detail and relevant keywords so it can easily be retrieved in a search system.
            DO NOT refer to the email or any specifics in any candidate articles.
            Articles should be written as if they were to appear on a public knowledge base.
            NEVER say things like "According to the information provided", "Based on the ticket", etc.

            DO NOT extract knowledge from lines in the message starting with "Unknown".

            DO NOT extract knowledge that is extremely temporal, like current stock status.
            DO NOT extract knowledge that is extremely general, and not specific to the company, processes or products.

            Do not provide any knowledge or rationale if the thread looks to be an automated email exchange or unrelated to customer support.

            Support ticket = {original_thread}

            {format_instructions}
        """,
        input_variables=["original_thread"],
        partial_variables={"format_instructions": get_training_response_parser(categories1, categories2).get_format_instructions()}
)


# -------------------------------------------------------------------------------
# deflection
# -------------------------------------------------------------------------------
actions_taken_schemas = [
    ResponseSchema(
        name="action_required",
        description="True if it appears that the human agent had to take action in another system, like manually reseting a password, provisioning an account or update something outside the ticketing system itself, etc, in order to solve the ticket. Simply requesting additional information from the help seeker or material should not be considered an action, nor should looking up current stock, product or policy information that is generally available to support agents and not specifically related to the customer. It is acceptable for the help seeker to take action, but not the human support agent.",
        type="boolean"
    ),
    ResponseSchema(
        name="action_required_rationale",
        description="a point-by-point explanation of your rationale for `action_required."
    ),
    ResponseSchema(
        name="non_kb_knowledge_used",
        description="A boolean indicated whether any statements or facts provided by the support agent that are specific to the customer in question and would not be found in the support rep's internal or public knowledge base.",
        type="boolean"
    ),
    ResponseSchema(
        name="non_kb_knowledge_used_rationale",
        description="""
            A point-by-point explanation of your rationale for `non_kb_knowledge_used`.
        """
    ),
]

actions_taken_parser = StructuredOutputParser.from_response_schemas(actions_taken_schemas)

actions_taken_prompt = PromptTemplate(
    template="""
        Your job is to review an email thread between a customer ("Unknown") and a customer support agent (with a <email>@sixzeropickleball.com email).

        Consider the entire conversation thread between the customer and the support agent. Determine whether the support agent needed to take any actions or perform further investigation to resolve the customer issue or provide the requested information. Actions can include, but are not limited to, the following:

        - Looking up product availability.
        - Processing a refund.
        - Sending an invoice.
        - Looking something up in another tool.
        - Any actions involving the current status of an order or account.
        - Manual steps taken to resolve an issue or fulfill a request.

        When evaluating, ensure you consider all actions taken by the agent throughout the thread, not just the final response. If the agent performed any such actions at any point, mark actions_taken as true. If no such actions were necessary, mark actions_taken as false.

        Email thread = {original_thread}

        {format_instructions}
    """,
    input_variables=["original_thread"],
    partial_variables={"format_instructions": actions_taken_parser.get_format_instructions()}
)

def get_chain(categories1: dict, categories2: dict):
    return (
        RunnableParallel({
            "training_results": get_training_prompt(categories1, categories2) | llm | get_training_response_parser(categories1, categories2),
            "prev_inputs": RunnablePassthrough()
        })
        | flatten
        | RunnableParallel({
            "actions_taken_results": actions_taken_prompt | llm | actions_taken_parser,
            "prev_inputs": RunnablePassthrough()
        })
        | flatten
    )


def process_ticket(ticket, categories1, categories2):
    try:
        original_thread = "\n".join(ticket.messages)
        with get_openai_callback() as cb:
            res = get_chain(categories1, categories2).invoke({"original_thread": original_thread})
            training_response = EeselTrainingResponse(
                knowledge_used=res['knowledge_used'],
                knowledge_used_rationale=res.get('knowledge_used_rationale'),
                knowledge_snippet_qa=res.get('knowledge_snippet_qa'),
                knowledge_snippet_qa_rationale=res.get('knowledge_snippet_qa_rationale'),
                actions_taken=res['action_required'],
                actions_taken_rationale=res.get('action_required_rationale'),
                category=res['category'],
                category_rationale=res.get('category_rationale'),
                category_2=res['category_2'],
                category_2_rationale=res.get('category_2_rationale'),
                original_thread=original_thread,
                cost=cb.total_cost
            )
        return training_response, cb.total_cost
    except Exception as e:
        print(f"Error processing ticket: {e}")
        return None, 0

def learn_tickets(tickets: List[EeselHelpdeskTicket], categories1: dict, categories2: dict) -> List[EeselTrainingResponse]:
    results = []
    total_cost = 0
    total_tickets = len(tickets)

    logging.info(f"Starting to process {total_tickets} tickets.")

    with ThreadPoolExecutor(max_workers=15) as executor:
        futures = [executor.submit(process_ticket, ticket, categories1, categories2) for ticket in tickets]
        for i, future in enumerate(as_completed(futures), 1):
            training_response, cost = future.result()
            if training_response:
                results.append(training_response)
                logging.info(f"Processed ticket {i}/{total_tickets} with cost: {cost}")
                total_cost += cost

    logging.info(f"Total cost of learning: {total_cost}")
    return results
