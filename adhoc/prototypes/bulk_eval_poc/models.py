from dataclasses import dataclass, asdict, fields, is_dataclass
from typing import List, Optional, Any, Type, TypeVar
import csv

@dataclass
class EeselHelpdeskTicket:
    messages: List[str]
    metadata: dict

@dataclass
class EeselQualityAssessmentResult:
    accuracy: bool
    accuracy_rationale: Optional[str] 
    knowledge_gap_indicated: bool  
    knowledge_gap_rationale: Optional[str]  
    escalation_indicated: bool  
    escalation_rationale: Optional[str]  
    training_opportunity_indicated: bool  
    training_opportunity_rationale: Optional[str]  

@dataclass
class EeselTrainingResponse:
    knowledge_used: bool  
    knowledge_used_rationale: Optional[str]  
    knowledge_snippet_qa: Optional[str]  
    knowledge_snippet_qa_rationale: Optional[str]  
    actions_taken: bool  
    actions_taken_rationale: Optional[str]  
    category: str  
    category_rationale: Optional[str] 
    category_2: str  
    category_2_rationale: Optional[str] 
    cost: Optional[float]
    original_thread: Optional[str]

@dataclass
class EeselCategorisationResponse:
    level1_category: str
    level1_category_rationale: str
    level2_category: str
    level2_category_rationale: str
    original_message: str
    metadata: Optional[Any] 
    cost: Optional[float]

@dataclass
class EeselHelpdeskReplayResult:
    original_message: str
    ai_reply: str
    human_reply: Optional[str]
    knowledge_sources: str
    metadata: Optional[Any]

@dataclass
class IndebtedHelpdeskResult:
    knowledge_gap_indicated: bool
    knowledge_gap_rationale: str
    knowledge_snippet_qa: Any
    knowledge_snippet_qa_rationale: str
    helpful: bool
    helpful_rationale: str
    action_required: bool
    action_required_rationale: str
    deflection_opportunity: bool
    deflection_opportunity: str
    complete_deflection: bool
    complete_deflection_rationale: str


def dump_dataclass_to_csv(data: List[Any], filename: str):
    if not data:
        raise ValueError("Data list is empty")

    if not is_dataclass(data[0]):
        raise ValueError("Items in the data list must be dataclass instances")

    fieldnames = [f.name for f in fields(data[0])]

    dict_data = [asdict(item) for item in data]

    with open(filename, 'w', newline='') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        for item in dict_data:
            writer.writerow(item)

T = TypeVar('T')

def dict_to_dataclass(cls: Type[T], data: dict) -> T:
    fieldnames = {f.name for f in fields(cls)}
    return cls(**{k: v for k, v in data.items() if k in fieldnames})

def load_csv_to_dataclass(filename: str, cls: Type[T]) -> List[T]:
    if not is_dataclass(cls):
        raise ValueError("Provided class must be a dataclass")

    with open(filename, 'r', newline='') as csvfile:
        reader = csv.DictReader(csvfile)
        return [dict_to_dataclass(cls, row) for row in reader]

