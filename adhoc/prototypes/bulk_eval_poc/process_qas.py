import os
from typing import List
import json

print("Importing libraries...")
try:
    from sentence_transformers import SentenceTransformer
    print("Successfully imported SentenceTransformer.")
except ImportError as e:
    print(f"Error importing SentenceTransformer: {e}")
    sys.exit(1)

from sklearn.metrics.pairwise import cosine_similarity
from sklearn.cluster import KMeans
import sys
import csv

# Increase the CSV field size limit
csv.field_size_limit(sys.maxsize)

def load_qa_pairs(filename: str) -> List[tuple]:
    qa_pairs = []
    if filename.endswith('.csv'):
        with open(filename, 'r', newline='') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                if 'knowledge_snippet_qa' in row and row['knowledge_snippet_qa']:
                    qa_text = row['knowledge_snippet_qa']
                    qa_lines = qa_text.split('\n')
                    question, answer = None, []
                    for line in qa_lines:
                        if line.startswith('Q:'):
                            if question and answer:
                                qa_pairs.append((question, '\n'.join(answer).strip()))
                            question = line[2:].strip()
                            answer = []
                        elif line.startswith('A:'):
                            answer.append(line[2:].strip())
                        else:
                            if answer:
                                answer[-1] += f"\n{line.strip()}"
                    if question and answer:
                        qa_pairs.append((question, '\n'.join(answer).strip()))
    elif filename.endswith('.json'):
        with open(filename, 'r') as jsonfile:
            data = json.load(jsonfile)
            for item in data:
                question = item.get('question')
                answer = item.get('answer')
                if question and answer:
                    qa_pairs.append((question, answer))
    return qa_pairs

def process_and_cluster_questions(qa_pairs, model):
    aggregated_qa_pairs = qa_pairs  # Use the provided QA pairs

    if not aggregated_qa_pairs:
        print("No QA pairs found. Exiting.")
        return {}, 0, 0

    questions, answers = zip(*aggregated_qa_pairs)
    embeddings = model.encode(questions)

    similarity_matrix = cosine_similarity(embeddings)
    threshold = 0.9
    duplicates = set()
    for i in range(len(similarity_matrix)):
        for j in range(i + 1, len(similarity_matrix)):
            if similarity_matrix[i][j] > threshold:
                duplicates.add(j)

    filtered_qa_pairs = [(q, a) for i, (q, a) in enumerate(aggregated_qa_pairs) if i not in duplicates]
    filtered_embeddings = [emb for i, emb in enumerate(embeddings) if i not in duplicates]

    num_clusters = 10
    kmeans = KMeans(n_clusters=num_clusters, random_state=42)
    cluster_labels = kmeans.fit_predict(filtered_embeddings)

    clustered_qa_pairs = {}
    for qa_pair_idx, cluster_idx in enumerate(cluster_labels):
        clustered_qa_pairs.setdefault(cluster_idx, []).append(filtered_qa_pairs[qa_pair_idx])

    return clustered_qa_pairs, len(aggregated_qa_pairs), len(duplicates)

print("Loading the model...")

# Initialize the SBERT model
model = SentenceTransformer('all-MiniLM-L6-v2')

# Gather the input file path from CLI arguments
if len(sys.argv) < 2:
    print("Usage: python process_qas.py <input_file_path>")
    sys.exit(1)

input_file_path = sys.argv[1]

# Load QA pairs from the CSV file
qa_pairs = load_qa_pairs(input_file_path)

# Process all files and cluster QA pairs
clustered_questions, initial_count, duplicate_count = process_and_cluster_questions(qa_pairs, model)

# Print the clustered questions as JSON
output = {
    "clusters": {
        str(cluster_idx): [{"question": q, "answer": a} for q, a in qa_pairs]
        for cluster_idx, qa_pairs in clustered_questions.items()
    },
    "initial_count": initial_count,
    "duplicate_count": duplicate_count,
    "final_count": sum(len(pairs) for pairs in clustered_questions.values())
}

print(json.dumps(output, indent=4))

# Summary logging
# final_count = sum(len(pairs) for pairs in clustered_questions.values())
# print(f"Initial QA pairs: {initial_count}")
# print(f"Duplicates removed: {duplicate_count}")
# print(f"Final QA pairs: {final_count}")
