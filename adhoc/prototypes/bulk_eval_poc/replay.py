import json
import requests
from time import sleep
from typing import List, Dict, Any

from bulk_eval_poc.models import EeselHelpdeskTicket, EeselHelpdeskReplayResult

REPLAY_ENDPOINT = "https://oracle.eesel.app/helpdesk/v4/assistant/webhook"

# Function to send POST request to REST endpoint
def send_request(data: Dict[str, Any], api_token: str, rest_endpoint: str) -> Dict[str, Any]:
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {api_token}'
    }
    response = requests.post(rest_endpoint, json=data, headers=headers)
    return response.json()

# Utility function to process a helpdesk ticket and capture responses
def replay_helpdesk_ticket(ticket: EeselHelpdeskTicket, api_token: str, rest_endpoint: str) -> EeselHelpdeskReplayResult:
    # if not ticket.messages or len(ticket.messages) < 2:
    #     return []
    
    first_element = ticket.messages[0]
    # human_reply = ticket.messages[1]
    
    payload = {
        # "metadata": ticket.metadata,
        "next_message": first_element,
        "tools": ["generic_operation_search_all"],
    }
    
    try:
        print("Attempting replay.")
        helpdesk_response = send_request(payload, api_token, rest_endpoint)
        print(helpdesk_response)
        ai_response = helpdesk_response.get("response", "No response from helpdesk endpoint.")
        # knowledge_sources = helpdesk_response.get("sources", "No sources from helpdesk endpoint.")
        print("Got response")
        knowledge_sources = ""
        human_reply = ""

        result = EeselHelpdeskReplayResult(
            original_message=first_element,
            ai_reply=ai_response,
            human_reply=human_reply,
            knowledge_sources=knowledge_sources,
            metadata=ticket.metadata
        )
        
        print("Got replay result")

        return result

    except Exception as e:
        print(f"Exception: {e}")

    return None
def replay_helpdesk_tickets(tickets: List[EeselHelpdeskTicket], api_token: str) -> List[EeselHelpdeskReplayResult]:
    results = []
    for ticket in tickets:
        try:
            result = replay_helpdesk_ticket(ticket, api_token, REPLAY_ENDPOINT)
            results.append(result)
        except Exception as e:
            print("Something went wrong with replay" + str(e))
    return results
