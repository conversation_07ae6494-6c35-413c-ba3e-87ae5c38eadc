import pandas as pd
import json
import re

# Load the CSV file
csv_file = './tmp/indebted.csv'
df = pd.read_csv(csv_file, low_memory=False)

# Define the columns we are interested in
description_column = 'Description'
summary_column = 'Summary'
comments_columns = [col for col in df.columns if 'Comment' in col]
issue_key_column = 'Issue key'

# Function to clean unwanted data from comments
def clean_comment(comment):
    if pd.isna(comment):
        return ''
    
    # return comment
    # Convert to string if not already
    comment = str(comment)
    # Remove patterns like "28/May/24 7:40 AM;629828b62eafc9006eec9546;[~accountid:620d97b11d088700694f18a8]"
    cleaned_comment = re.sub(r'\d{1,2}/[A-Za-z]{3}/\d{2,4} \d{1,2}:\d{2} [APM]{2};[\w:;-]+;', '', comment)
    return cleaned_comment.strip()

# Function to collect all non-NA fields into a single string
def collect_non_na_fields(row):
    non_na_fields = []
    for col in row.index:
        if pd.notna(row[col]) and "Comment" not in col:
            non_na_fields.append(f"{col}: {row[col]}")
    return "\n\n".join(non_na_fields)

# Create a function to extract the relevant data
def extract_data(row): 
    all_non_na_fields = collect_non_na_fields(row)

    messages = [all_non_na_fields] + [clean_comment(row[col]) for col in comments_columns if pd.notna(row[col])]
    metadata = {'issue_key': row[issue_key_column]}
    return {
        'messages': messages,
        'metadata': metadata,
    }

# Apply the function to each row in the dataframe
data = df.apply(extract_data, axis=1).tolist()

# Save the result to a JSON file
# json_file = 'path_to_your_output_file.json'  # Replace with the path to your output JSON file
# with open(json_file, 'w') as f:
#     json.dump(data, f, indent=2)

# For verification, print the first 5 entries
print(json.dumps(data, indent=2))
