import requests
from typing import List, Dict

class FreshdeskExporter:
    def __init__(self, api_key: str, domain: str):
        """
        Initialize the FreshdeskExporter with API credentials.

        :param api_key: Your Freshdesk API key
        :param domain: Your Freshdesk domain (e.g., 'yourcompany')
        """
        self.api_key = api_key
        self.domain = domain
        self.base_url = f"https://{self.domain}.freshdesk.com/api/v2"

    def get_recent_tickets(self, n: int) -> List[Dict]:
        """
        Retrieve the N most recent customer support tickets.

        :param n: Number of recent tickets to retrieve
        :return: A list of ticket dictionaries
        """
        tickets = []
        per_page = min(n, 100)  # Freshdesk API allows a maximum of 100 per page
        page = 1

        while len(tickets) < n:
            params = {
                "per_page": per_page,
                "page": page,
                "order_by": "created_at",
                "order_type": "desc"
            }
            response = requests.get(
                f"{self.base_url}/tickets",
                auth=(self.api_key, "X"),
                params=params
            )

            if response.status_code != 200:
                raise Exception(f"Failed to fetch tickets: {response.status_code} - {response.text}")

            page_tickets = response.json()
            if not page_tickets:
                break  # No more tickets available

            tickets.extend(page_tickets)
            page += 1

        return tickets[:n]
