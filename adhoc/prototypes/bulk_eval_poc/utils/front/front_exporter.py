import requests
import json
import pprint
import time
import markdownify
import re

class FrontAPIFetcher:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = 'https://api2.frontapp.com'
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Accept': 'application/json'
        }

    def clean_email_body(self, email_body):
        cleaned_body = re.sub(r'(\n--.*|\nBest.*|\nKind regards.*|\nOn.*wrote:|\n>.*)', '', email_body, flags=re.DOTALL)
        cleaned_body = re.sub(r'\[.*?\]\(.*?\)', '<link>', cleaned_body)
        return cleaned_body.strip()

    def get_recent_conversations(self, limit=1000):
        url = f'{self.base_url}/conversations'
        params = {'limit': limit}
        all_conversations = []

        while url and len(all_conversations) < limit:
            print("Fetching")
            response = requests.get(url, headers=self.headers, params=params)
            
            if response.status_code != 200:
                raise Exception(f'Error fetching conversations: {response.status_code} - {response.text}')
            
            data = response.json()
            all_conversations.extend(data['_results'])

            url = data['_pagination'].get('next', None)
            params = {}

        print("Got all conversations")

        return all_conversations

    def get_messages(self, conversation_id):
        url = f'{self.base_url}/conversations/{conversation_id}/messages'
        response = requests.get(url, headers=self.headers)

        if response.status_code != 200:
            raise Exception(f'Error fetching messages for conversation {conversation_id}: {response.status_code} - {response.text}')

        return response.json()

    def filter_interactions(self, messages):
        interactions = []
        if not messages:
            return []
        for message in messages['_results']:
            interaction = {
                'author': message["author"]["email"] if message["author"] else "Unknown",
                'body': self.clean_email_body(markdownify.markdownify(message['body'])),
                'created_at': message['created_at']
            } 
            interactions.append(interaction)
        return interactions

    def fetch_filtered_interactions(self, limit=1000):
        conversations = self.get_recent_conversations(limit)
        all_interactions = []

        if conversations:
            for conversation in conversations:
                conversation_id = conversation['id']
                print("Getting messages")
                messages = self.get_messages(conversation_id)
                
                if messages:
                    interactions = self.filter_interactions(messages)
                    all_interactions.append(interactions)
                time.sleep(1) 

        return all_interactions
