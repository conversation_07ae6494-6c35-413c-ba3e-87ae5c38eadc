from typing import Any, Dict, List
import markdownify
from bulk_eval_poc.models import EeselHelpdeskTicket

def transform_front_conversations(data: List[List[Dict[str, Any]]]) -> List[EeselHelpdeskTicket]:
    transformed_data = []
    
    for conversation in data:
        content = []
        last_author_email = None
        last_author_name = None
        combined_body = ""

        for message in conversation:
            author_email = message['author']
            author_name = f"<Author: {author_email}>"
            body = message.get('body', '')

            if body and ('powered by' in body.lower() or 'sources:' in body.lower() or 'eesel.ai response' in body.lower() or 'eesel.ai assistant' in body.lower()):
                continue

            if author_email != last_author_email:
                if combined_body:
                    content.append(f"{last_author_name}: {combined_body}")
                last_author_email = author_email
                last_author_name = author_name
                combined_body = markdownify.markdownify(body, heading_style='ATX')
            else:
                combined_body += f"\n\n{markdownify.markdownify(body, heading_style='ATX')}"

        if combined_body:
            content.append(f"{last_author_name}: {combined_body}")

        if conversation:
            ticket_metadata = {
                "ticket_id": conversation[0]['created_at'],  # Using 'created_at' as a unique identifier for simplicity
                "created_at": conversation[0]['created_at'],
                "updated_at": conversation[-1]['created_at']
            }
            ticket = EeselHelpdeskTicket(messages=content, metadata=ticket_metadata)
            transformed_data.append(ticket)

    return transformed_data
