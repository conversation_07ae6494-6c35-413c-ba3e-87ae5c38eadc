import json
import requests

# Example usage
# if __name__ == '__main__':
#     access_token = '-------'  # Replace with your actual Intercom access token
#     intercom_fetcher = IntercomFetcher(access_token)
#     conversations = intercom_fetcher.fetch_conversations(limit=100)
#     print(json.dumps(conversations, indent=4))


class IntercomFetcher:
    def __init__(self, access_token):
        self.access_token = access_token

    def fetch_conversation_ids(self, limit=1000):
        print(f"Fetching up to the last {limit} conversation IDs...")
        url = 'https://api.intercom.io/conversations/search'
        headers = {
            'Authorization': f'Bearer {self.access_token}',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
        payload = {
            "query": {
                "operator": "AND",
                "value": [
                    {
                        "field": "source.delivered_as",
                        "operator": "!=",
                        "value": ""
                    },
                    {
                        "field": "statistics.last_admin_reply_at",
                        "operator": ">",
                        "value": 1
                    }
                ]
            },
            "pagination": {
                "per_page": 50,
                "starting_after": None
            }
        }

        conversation_ids = []
        while True:
            response = requests.post(url, headers=headers, json=payload)
            if response.status_code == 200:
                data = response.json()
                for conversation in data.get('conversations', []):
                    conversation_ids.append(conversation['id'])
                    if len(conversation_ids) >= limit:
                        break
                if len(conversation_ids) >= limit or not ('pages' in data and data['pages']['next']):
                    break
                payload["pagination"]["starting_after"] = data['pages']['next']['starting_after']
                print(f"Fetched {len(conversation_ids)} conversation IDs so far...")
            else:
                print(f'Failed to fetch conversations: {response.status_code}')
                break
        print(f"Total conversation IDs fetched: {len(conversation_ids)}")
        return conversation_ids[:limit]

    def fetch_conversation_detail(self, conversation_id):
        url = f'https://api.intercom.io/conversations/{conversation_id}'
        headers = {
            'Authorization': f'Bearer {self.access_token}',
            'Accept': 'application/json'
        }
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            return response.json()
        else:
            print(f'Failed to fetch conversation {conversation_id}: {response.status_code}')
            return None

    def fetch_conversations(self, limit=1000):
        conversation_ids = self.fetch_conversation_ids(limit)
        print("Fetching conversation details...")
        all_conversations = []
        total_conversations = len(conversation_ids)
        batch_size = 20

        for i in range(0, total_conversations, batch_size):
            batch_ids = conversation_ids[i:i + batch_size]

            for idx, conversation_id in enumerate(batch_ids):
                print(f'Fetching conversation {i + idx + 1}/{total_conversations}')
                content = self.fetch_conversation_detail(conversation_id)
                if content:
                    all_conversations.append(content)

        print(f"Total conversations fetched: {len(all_conversations)}")
        return all_conversations
