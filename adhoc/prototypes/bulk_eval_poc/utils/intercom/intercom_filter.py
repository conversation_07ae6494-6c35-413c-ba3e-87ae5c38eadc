import json

MAX_CONVO = 1000

def filter_conversations(data):
    """
    Filters conversations to include only those with more than one comment part.
    
    Parameters:
    data (list): List of conversation dictionaries.
    
    Returns:
    list: Filtered list of conversations.
    """
    filtered_data = []
    count = 0
    for conversation in data:
        if count >= MAX_CONVO:
            break
        if 'conversation_parts' in conversation and 'conversation_parts' in conversation['conversation_parts']:
            parts = conversation['conversation_parts']['conversation_parts']
            if len(parts) > 0:
                comment_count = sum(1 for part in parts if part['part_type'] == 'comment')
                if comment_count > 1:
                    filtered_data.append(conversation)
                    count += 1
    return filtered_data
