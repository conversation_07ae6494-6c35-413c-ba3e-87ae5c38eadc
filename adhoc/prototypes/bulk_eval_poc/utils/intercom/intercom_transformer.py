from typing import Any, Dict, List
import markdownify
from bulk_eval_poc.models import EeselHelpdeskTicket
import markdownify
import json

def transform_conversations(data: List[Dict[str, Any]]) -> List[EeselHelpdeskTicket]:
    transformed_data = []
    for conversation in data:
        content = []
        # Check if the initial message author type is 'user'
        if 'source' in conversation and 'body' in conversation['source'] and conversation['source']['author']['type'] == 'user':
            source_author_id = conversation['source']['author']['id']
            source_author_name = conversation['source']['author']['name']
            source_body = conversation['source']['body']
            
            # Initialize combined content with the source part
            combined_body = markdownify.markdownify(source_body, heading_style='ATX') if source_body else ""
            
            # Add conversation parts bodies to content and check if there's an admin response of type 'comment' or 'assignment'
            has_admin_response = False
            last_author_id = source_author_id
            last_author_name = source_author_name

            if 'conversation_parts' in conversation and 'conversation_parts' in conversation['conversation_parts']:
                parts = conversation['conversation_parts']['conversation_parts']
                for part in parts:
                    part_body = part.get('body', '')
                    if part_body and ('powered by' in part_body.lower() or 'sources:' in part_body.lower() or 'eesel.ai response' in part_body.lower() or 'eesel.ai assistant' in part_body.lower()):
                        continue
                    if part['author']['type'] == 'admin' and 'body' in part and part['part_type'] in ['comment', 'assignment']:
                        has_admin_response = True
                    if part['author']['type'] in ['admin', 'user'] and 'body' in part:
                        if part_body:  # Check if part_body is not None
                            author_id = part['author']['id']
                            author_name = part['author']['name']
                            if author_id == last_author_id:
                                combined_body += f"\n\n{markdownify.markdownify(part_body, heading_style='ATX')}"
                            else:
                                if combined_body:
                                    content.append(f"{last_author_name}: {combined_body}")
                                last_author_id = author_id
                                last_author_name = author_name
                                combined_body = markdownify.markdownify(part_body, heading_style='ATX')
                if combined_body:  # Append the last combined body
                    content.append(f"{last_author_name}: {combined_body}")
            
            # Only add content to transformed_data if there's an admin response of type 'comment' or 'assignment'
            if has_admin_response:
                ticket = EeselHelpdeskTicket(messages=content, metadata=conversation)
                transformed_data.append(ticket)
            else:
                print("No admin response of type 'comment' or 'assignment' found for conversation:")
                # print(json.dumps(conversation, indent=4))
        else:
            print("Initial message author type is not 'user' for conversation:")
            # print(json.dumps(conversation, indent=4))
                
    return transformed_data


# Example usage
if __name__ == "__main__":
    # Example data array
    example_data = [
        {
            "source": {
                "author": {"type": "user", "id": "user123", "name": "John Doe"},
                "body": "<p>Hello, I need help with...</p>"
            },
            "conversation_parts": {
                "conversation_parts": [
                    {
                        "author": {"type": "admin", "id": "admin456", "name": "Admin"},
                        "body": "<p>Sure, I can help you with that.</p>",
                        "part_type": "comment"
                    }
                ]
            }
        }
    ]

    transformed_tickets = transform_conversations(example_data)

    for ticket in transformed_tickets:
        print(ticket)
