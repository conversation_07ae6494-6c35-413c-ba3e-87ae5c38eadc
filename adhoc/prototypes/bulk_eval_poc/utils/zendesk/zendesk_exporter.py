import json
import pprint
import requests
from datetime import datetime, timedelta
import pprint

# ID 360001505817: 'Interne Kategorie',
# ID 9080259303325: 'interner Task',

# 360001505817 --> filter to these for now.

# custom_field_360001505817:*
# custom_field_9080259303325:*

VALID_RECIPIENTS = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>"
]

class ZendeskFetcher:
    def __init__(self, domain, access_token):
        self.access_token = access_token
        self.subdomain = domain
        self.custom_field_map = self.fetch_custom_field_definitions()

    def fetch_custom_field_definitions(self):
        print("Fetching custom field definitions...")
        url = f'https://{self.subdomain}.zendesk.com/api/v2/ticket_fields.json'
        print(url)
        headers = {
            'Authorization': f'{self.access_token}',
            'Accept': 'application/json'
        }

        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            fields = response.json().get('ticket_fields', [])
            custom_field_map = {field['id']: field['title'] for field in fields}
            print(f"Custom field definitions fetched: {len(custom_field_map)}")

            pprint.pprint(custom_field_map)
            return custom_field_map
        else:
            print(f'Failed to fetch custom field definitions: {response.status_code}')
            return {}

    def fetch_ticket_ids(self, limit=30000):
        # print(f"Fetching up to the last {limit} ticket IDs...")
        print(f"Fetching up to the last {limit} ticket IDs created in the last 6 months...")
        six_months_ago = (datetime.now() - timedelta(days=1 * 30)).strftime('%Y-%m-%d')
        #  OR 
        url = f'https://{self.subdomain}.zendesk.com/api/v2/search.json?query=type:ticket created>{six_months_ago} (custom_field_19840724383261:"interessent")'
        headers = {
            'Authorization': f'{self.access_token}',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
        params = {
            'per_page': 100
        }

        tickets = []
        while True:
            response = requests.get(url, headers=headers, params=params)
            if response.status_code == 200:
                data = response.json()

                for ticket in data.get('results', []):
                    custom_fields = ticket.get('custom_fields', [])
                    # Replace custom field IDs with names
                    named_fields = [{self.custom_field_map.get(field['id'], field['id']): field['value']} for field in custom_fields]
                    
                    if ticket['recipient'] in VALID_RECIPIENTS:
                        tickets.append({
                            "ticket_id": ticket['id'],
                            "recipient": ticket['recipient'],
                            "subject": ticket["subject"],
                            "fields": named_fields
                        })
                    if len(tickets) >= limit:
                        break

                if len(tickets) >= limit or not data['next_page']:
                    break
                url = data['next_page']
                print(f"Fetched {len(tickets)} ticket IDs so far...")
            else:
                print(f'Failed to fetch tickets: {response.status_code}')
                break
        print(f"Total ticket IDs fetched: {len(tickets)}")
        return tickets[:limit]

    def fetch_ticket_comments(self, ticket_id):
        url = f'https://{self.subdomain}.zendesk.com/api/v2/tickets/{ticket_id}/comments.json'
        headers = {
            'Authorization': f'{self.access_token}',
            'Accept': 'application/json'
        }
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            return response.json().get('comments', [])
        else:
            print(f'Failed to fetch comments for ticket {ticket_id}: {response.status_code}')
            return []

    def fetch_tickets_with_comments(self, limit=1000):
        ticket_ids = self.fetch_ticket_ids(limit)
        print("Fetching ticket details with comments...")
        all_tickets = []
        total_tickets = len(ticket_ids)
        batch_size = 20

        for i in range(0, total_tickets, batch_size):
            batch_ids = ticket_ids[i:i + batch_size]

            for idx, ticket in enumerate(batch_ids):
                print(f'Fetching ticket {i + idx + 1}/{total_tickets}')
                ticket_comments = self.fetch_ticket_comments(ticket["ticket_id"])
                if ticket:
                    all_tickets.append({
                        "ticket_id": ticket["ticket_id"],
                        "subject": ticket["subject"],
                        "comments": ticket_comments,
                        "recipient": ticket["recipient"],
                        "fields": ticket["fields"]
                    })

        print(f"Total tickets with comments fetched: {len(all_tickets)}")
        return all_tickets


# Example usage
if __name__ == '__main__':
    access_token = '-------'  # Replace with your actual Zendesk access token
    domain = 'your_zendesk_subdomain'  # Replace with your actual Zendesk domain
    zendesk_fetcher = ZendeskFetcher(domain, access_token)
    tickets = zendesk_fetcher.fetch_tickets_with_comments(limit=100)
    print(json.dumps(tickets, indent=4))
