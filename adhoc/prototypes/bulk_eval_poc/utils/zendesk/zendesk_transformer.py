from typing import Any, Dict, List
import markdownify
from bulk_eval_poc.models import EeselHelpdeskTicket
import pprint
def transform_zendesk_tickets(data: List[Dict[str, Any]], field_whitelist: List[str]) -> List[EeselHelpdeskTicket]:
    transformed_data = []

    for ticket in data:
        content = []
        has_admin_response = False
        combined_body = ""
        last_author_id = None
        last_author_name = None
        
        # Filter fields based on whitelist
        filtered_fields = [{k: v for k, v in field.items() if k in field_whitelist} for field in ticket['fields']]
        filtered_fields = [field for field in filtered_fields if field] 
                
        for comment in ticket['comments']:
            author_id = comment['author_id']
            author_name = f"<Author: {author_id}>"
            body = comment.get('body', '')

            if body and ('powered by' in body.lower() or 'sources:' in body.lower() or 'eesel.ai response' in body.lower() or 'eesel.ai assistant' in body.lower()):
                continue

            if comment['public'] and 'body' in comment:
                if comment['author_id'] != last_author_id:
                    if combined_body:
                        content.append(f"{last_author_name}: {combined_body}")
                    last_author_id = author_id
                    last_author_name = author_name
                    combined_body = markdownify.markdownify(body, heading_style='ATX')
                else:
                    combined_body += f"\n\n{markdownify.markdownify(body, heading_style='ATX')}"
                
                # if 'admin' in author_name.lower():
                #     has_admin_response = True
        
        if combined_body:
            content.append(f"{last_author_name}: {combined_body}")

        # if has_admin_response:
        ticket_metadata = {
            "ticket_id": ticket['ticket_id'],
            "fields": filtered_fields
        }
        pprint.pprint(ticket_metadata)

        if len(content) > 0:
            content[0] = "Email subject: " + ticket["subject"] + "\n\n" + content[0]
        
        ticket = EeselHelpdeskTicket(messages=content, metadata=ticket_metadata)
        transformed_data.append(ticket)
        # else:
        #     print(f"No admin response found for ticket {ticket['ticket_id']}")
    
    return transformed_data

# Example usage
if __name__ == '__main__':
    # Sample data structure based on the new result format
    sample_data = [
        {
            "ticket_id": 12345,
            "created_at": "2024-06-17T12:34:56Z",
            "updated_at": "2024-06-18T12:34:56Z",
            "fields": [
                {"field_name_1": "value_1"},
                {"field_name_2": "value_2"}
            ],
            "comments": [
                {
                    "author_id": 1,
                    "public": True,
                    "body": "This is a public comment.",
                    "created_at": "2024-06-17T12:34:56Z"
                },
                {
                    "author_id": 2,
                    "public": True,
                    "body": "This is another public comment.",
                    "created_at": "2024-06-18T12:34:56Z"
                }
            ]
        }
    ]
    
    field_whitelist = ["field_name_1", "field_name_2"]
    
    transformed_tickets = transform_zendesk_tickets(sample_data, field_whitelist)
    print(transformed_tickets)
