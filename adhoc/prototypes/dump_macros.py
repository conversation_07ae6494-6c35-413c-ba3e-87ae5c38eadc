import json
import argparse

# Set up argument parser
parser = argparse.ArgumentParser(description='Process a JSON file and output markdown.')
parser.add_argument('input_file', type=str, help='The input JSON file')
args = parser.parse_args()

# Load the JSON data
with open(args.input_file, 'r') as file:
    data = json.load(file)

# Write to stdout
for cluster, items in data['categorized_clusters'].items():
    # Write the cluster heading
    print(f"## {cluster}\n")
    
    # Write each question and answer pair
    for item in items:
        print(f"**{item['question']}**:")
        print(f"{item['answer']}\n")