import os
import requests
import json
from datetime import datetime, timedelta
import time
from typing import List, Dict
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed

class IntercomConversationDownloader:
    def __init__(self, access_token: str, rate_limit: float = 0.5):
        self.access_token = access_token
        self.rate_limit = rate_limit
        self.base_url = "https://api.intercom.io"
        self.logger = logging.getLogger("intercom_downloader")
        self.session = self._setup_session()

    def _setup_session(self) -> requests.Session:
        session = requests.Session()
        session.headers.update({
            'Authorization': f'Bearer {self.access_token}',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        })
        return session

    def _make_request(self, url: str, params: Dict = None) -> Dict:
        try:
            time.sleep(self.rate_limit)
            response = self.session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            self.logger.error(f"Error making request to {url}: {str(e)}")
            raise

    def extract_conversation_messages(self, conversation: Dict) -> List[Dict]:
        messages = []
        
        # Add initial message
        source = conversation['source']
        messages.append({
            'author': source['author'].get('name', 'Unknown'),
            'body': source['body']
        })
        
        # Add subsequent messages
        for part in conversation['conversation_parts'].get('conversation_parts', []):
            if part['body'] and part['part_type'] in ['comment', 'assignment']:
                messages.append({
                    'author': part['author'].get('name', 'Unknown'),
                    'body': part['body']
                })
        
        return messages

    def _get_conversation_batch(self, conversation_ids: List[str]) -> List[Dict]:
        """Process a batch of conversation IDs and return their details."""
        conversations = []
        for conv_id in conversation_ids:
            try:
                conv_details = self._make_request(f"{self.base_url}/conversations/{conv_id}")
                messages = self.extract_conversation_messages(conv_details)
                conversations.append({
                    'id': conv_details['id'],
                    'messages': messages
                })
                self.logger.info(f"Downloaded conversation {conv_id}")
            except Exception as e:
                self.logger.error(f"Error downloading conversation {conv_id}: {str(e)}")
        return conversations

    def get_conversations(self, start_date: datetime, max_workers: int = 5) -> List[Dict]:
        conversation_ids = []
        starting_after = None
        
        self.logger.info(f"Starting to collect conversation IDs from {start_date}")
        
        # First, collect all relevant conversation IDs
        while True:
            params = {
                'per_page': 50,
                'starting_after': starting_after
            }
            
            response = self._make_request(f"{self.base_url}/conversations", params)
            current_batch = len(response['conversations'])
            self.logger.info(f"Fetched batch of {current_batch} conversations")
            
            for conv in response['conversations']:
                conv_updated = datetime.fromtimestamp(conv['updated_at'])
                if conv_updated >= start_date:
                    conversation_ids.append(conv['id'])
                    if len(conversation_ids) >= 1000:  # Keep the same limit
                        break
                else:
                    break
            
            self.logger.info(f"Total conversation IDs collected: {len(conversation_ids)}")
            
            if len(conversation_ids) >= 1000 or not response.get('pages', {}).get('next'):
                break
                
            starting_after = response['pages']['next']['starting_after']

        # Process conversations in parallel
        all_conversations = []
        batch_size = 10  # Process 10 conversations per batch
        
        self.logger.info(f"Starting parallel processing of {len(conversation_ids)} conversations in batches of {batch_size}")
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Split conversation_ids into batches
            batches = [conversation_ids[i:i + batch_size] 
                      for i in range(0, len(conversation_ids), batch_size)]
            
            self.logger.info(f"Created {len(batches)} batches for processing")
            
            # Submit all batches to the thread pool
            future_to_batch = {
                executor.submit(self._get_conversation_batch, batch): batch 
                for batch in batches
            }
            
            completed_conversations = 0
            # Collect results as they complete
            for future in as_completed(future_to_batch):
                batch_results = future.result()
                completed_conversations += len(batch_results)
                all_conversations.extend(batch_results)
                self.logger.info(f"Progress: {completed_conversations}/{len(conversation_ids)} conversations processed")

        self.logger.info(f"Completed downloading {len(all_conversations)} conversations")
        return all_conversations

def main():
    # Setup logging with timestamp
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Configure these variables
    ACCESS_TOKEN = os.getenv('INTERCOM_KEY')
    OUTPUT_FILE = "data/intercom_conversations.json"
    
    # Calculate date 6 months ago
    six_months_ago = datetime.now() - timedelta(days=180)
    
    downloader = IntercomConversationDownloader(ACCESS_TOKEN)
    
    try:
        conversations = downloader.get_conversations(six_months_ago, max_workers=5)
        
        # Save to JSON file
        with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
            json.dump(conversations, f, indent=2, ensure_ascii=False)
            
        print(f"Successfully downloaded {len(conversations)} conversations to {OUTPUT_FILE}")
        
    except Exception as e:
        print(f"Error downloading conversations: {str(e)}")

if __name__ == "__main__":
    main()
