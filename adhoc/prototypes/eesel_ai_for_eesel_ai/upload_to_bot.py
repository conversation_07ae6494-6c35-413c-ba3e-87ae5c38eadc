import json
import requests
import logging
from typing import List, Dict
import time
import os
import html2text

class EeselUploader:
    def __init__(self, bearer_token: str, rate_limit: float = 0.5):
        self.bearer_token = bearer_token
        self.rate_limit = rate_limit
        self.ingest_url = "https://oracle.eesel.app/ingest-async"
        self.logger = logging.getLogger("eesel_uploader")
        self.session = self._setup_session()
        self.html_converter = html2text.HTML2Text()
        self.html_converter.body_width = 0  # Disable line wrapping

    def _setup_session(self) -> requests.Session:
        session = requests.Session()
        session.headers.update({
            'Authorization': f'Bearer {self.bearer_token}',
            'Content-Type': 'application/json'
        })
        return session

    def format_conversation_to_markdown(self, conversation: Dict) -> str:
        markdown = ""
        for message in conversation['messages']:
            author = message['author']
            # Convert HTML body to markdown
            body = self.html_converter.handle(message['body'])
            markdown += f"**{author}**: {body}\n\n"
        return markdown.strip()

    def upload_conversation(self, conversation: Dict) -> bool:
        try:
            markdown_content = self.format_conversation_to_markdown(conversation)
            
            payload = [{
                "pageTitle": f"Intercom Conversation {conversation['id']}",
                "pageBody": markdown_content,
                "source": f"https://eesel.ai/conversations/{conversation['id']}"
            }]

            time.sleep(self.rate_limit)
            response = self.session.post(self.ingest_url, json=payload)
            response.raise_for_status()
            
            self.logger.info(f"Successfully uploaded conversation {conversation['id']}")
            return True
            
        except requests.RequestException as e:
            self.logger.error(f"Error uploading conversation {conversation['id']}: {str(e)}")
            return False

    def upload_conversations_batch(self, conversations: List[Dict]) -> int:
        try:
            payload = []
            for conversation in conversations:
                markdown_content = self.format_conversation_to_markdown(conversation)
                self.logger.info(f"Processing conversation {conversation['id']}:")
                self.logger.info(f"Content length: {len(markdown_content)} characters")
                self.logger.info("First 200 characters of content:")
                self.logger.info(markdown_content[:200] + "...")
                
                payload.append({
                    "pageTitle": f"Intercom Conversation {conversation['id']}",
                    "pageBody": markdown_content,
                    "source": f"https://eesel.ai/conversations/{conversation['id']}"
                })

            time.sleep(self.rate_limit)
            response = self.session.post(self.ingest_url, json=payload)
            response.raise_for_status()
            
            self.logger.info(f"Successfully uploaded batch of {len(conversations)} conversations")
            return len(conversations)
            
        except requests.RequestException as e:
            self.logger.error(f"Error uploading batch of conversations: {str(e)}")
            return 0

def main():
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    # Get bearer token from environment variable
    BEARER_TOKEN = os.getenv('EESEL_JWT')
    if not BEARER_TOKEN:
        raise ValueError("EESEL_JWT environment variable is not set")
    
    INPUT_FILE = "data/intercom_conversations.json"
    BATCH_SIZE = 20
    MAX_CONVERSATIONS = 2000  # Limit to first 20 conversations
    
    uploader = EeselUploader(BEARER_TOKEN)
    
    try:
        # Load conversations from JSON file
        logger.info(f"Loading conversations from {INPUT_FILE}")
        with open(INPUT_FILE, 'r', encoding='utf-8') as f:
            all_conversations = json.load(f)
            conversations = all_conversations[:MAX_CONVERSATIONS]  # Take only first 20
        
        logger.info(f"Processing first {len(conversations)} conversations out of {len(all_conversations)} total")
        
        success_count = 0
        total_batches = (len(conversations) + BATCH_SIZE - 1) // BATCH_SIZE
        
        for i in range(0, len(conversations), BATCH_SIZE):
            batch = conversations[i:i + BATCH_SIZE]
            current_batch = (i // BATCH_SIZE) + 1
            logger.info(f"Processing batch {current_batch}/{total_batches}")
            success_count += uploader.upload_conversations_batch(batch)
                
        logger.info(f"Upload complete! Successfully uploaded {success_count} out of {len(conversations)} conversations")
        
    except Exception as e:
        logger.error(f"Error processing conversations: {str(e)}", exc_info=True)
        raise

if __name__ == "__main__":
    main()
