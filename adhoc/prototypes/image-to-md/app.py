from flask import Flask, render_template, request, redirect, url_for
from werkzeug.utils import secure_filename
from markdown import markdown
from PIL import Image
import pytesseract
import io

app = Flask(__name__)

markdown_files = []

@app.route('/', methods=['GET', 'POST'])
def upload_file():
    if request.method == 'POST':
        if 'files' not in request.files:
            return 'No file part'
        
        files = request.files.getlist('files')
        
        if not files or files[0].filename == '':
            return 'No selected file'
        
        for file in files:
            if file and file.filename.endswith('.png'):
                filename = secure_filename(file.filename)
                image = Image.open(file.stream)
                md_content = pytesseract.image_to_string(image)

                markdown_files.append({
                    'fileName': filename.replace('.png', '.md'),
                    'content': md_content
                })

        return redirect(url_for('view_selected_markdown'))

    return render_template('upload.html')

@app.route('/md/view', methods=['GET'])
def view_selected_markdown():
    selected_file = request.args.get('file')

    if selected_file:
        selected_md = next((item for item in markdown_files if item['fileName'] == selected_file), None)

        if selected_md:
            html_content = markdown(selected_md['content'])
        else:
            html_content = "File not found."
    else:
        html_content = "<p>Please select a Markdown file to view its content.</p>"

    md_files = [item['fileName'] for item in markdown_files]

    return render_template('markdown_viewer.html', content=html_content, filename=selected_file, md_files=md_files)


if __name__ == '__main__':
    app.run(debug=True)
