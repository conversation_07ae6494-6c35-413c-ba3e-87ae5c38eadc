# models.py
import os
from werkzeug.utils import secure_filename
from PIL import Image
import pytesseract

def convert_image_to_markdown(image_path):
    """
    Convert a PNG image to Markdown format by extracting text using Tesseract.
    """
    img = Image.open(image_path)
    text = pytesseract.image_to_string(img)
    return text

def process_image_and_save_md(upload_folder, filename):
    """
    Process the uploaded image, save the Markdown file, and delete the original image.
    """
    image_path = os.path.join(upload_folder, filename)

    # Convert the image to Markdown
    markdown_output = convert_image_to_markdown(image_path)

    return markdown_output
