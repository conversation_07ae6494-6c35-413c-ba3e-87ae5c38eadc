<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown Viewer</title>
    <!-- Include Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/css/bootstrap.min.css" rel="stylesheet">

    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .container {
            margin-top: 20px;
        }
        .markdown-content {
            background-color: #f8f8f8;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #ccc;
            overflow-x: auto;
            margin-top: 20px;
            position: relative;
        }
        .copy-btn {
            position: absolute;
            top: 20px;
            right: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Page header -->
        <div class="d-flex justify-content-between align-items-center">
            <h1 class="display-6">Markdown Viewer</h1>
            <a href="/" class="btn btn-outline-primary">Back to Upload</a>
        </div>

        <hr>

        <!-- Markdown File Selector -->
        <div class="row">
            <div class="col-md-6">
                <h2 class="h5">View Other Markdown Files</h2>
                <form method="GET" action="{{ url_for('view_selected_markdown') }}">
                    <div class="input-group">
                        <select name="file" class="form-select">
                            <option value="">Select a Markdown file</option>
                            {% for md_file in md_files %}
                                <option value="{{ md_file }}" {% if md_file == filename %}selected{% endif %}>
                                    {{ md_file }}
                                </option>
                            {% endfor %}
                        </select>
                        <button class="btn btn-outline-secondary" type="submit">Go</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Markdown Content Display -->
        <div class="markdown-content">
            <h2 class="h5">Markdown Content</h2>
            <button class="btn btn-outline-secondary copy-btn" id="copyBtn">Copy</button>
            <div id="markdownContent">
                <!-- Render the Markdown content as HTML -->
                {{ content|safe }}
            </div>
        </div>
    </div>

    <!-- Include Bootstrap JS and Popper.js (for dropdowns and other Bootstrap JS functionality) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Script to handle copy functionality -->
    <script>
        document.getElementById('copyBtn').addEventListener('click', function() {
            const markdownContent = document.getElementById('markdownContent').innerText;
            navigator.clipboard.writeText(markdownContent).then(function() {
                alert('Markdown content copied to clipboard!');
            }, function(err) {
                alert('Failed to copy text: ', err);
            });
        });
    </script>
</body>
</html>
