import csv
import markdownify

def csv_to_plaintext(input_csv, output_file):
    # Initialize an empty string for the output content
    output_content = ""
    
    # Open the CSV file and read its contents
    with open(input_csv, newline='', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        
        # Iterate over each row in the CSV
        for row in reader:
            # Convert HTML in 'Macro' to Markdown
            macro_md = markdownify.markdownify(row['Macro'], heading_style="ATX").rstrip()
            
            # Build the output content for this row
            content = f"{row['Improved Description']}\n{macro_md}\n------\n\n"
            output_content += content * 3  # Repeat the block three times
    
    # Write the output content to a file
    with open(output_file, 'w', encoding='utf-8') as file:
        file.write(output_content)

# Example usage:
csv_to_plaintext('./output/macro_evaluation_results.csv', 'output.txt')
