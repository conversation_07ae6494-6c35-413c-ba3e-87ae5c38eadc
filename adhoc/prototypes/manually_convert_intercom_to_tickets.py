import json
from typing import List, Dict, Any
from bulk_eval_poc.utils.intercom.intercom_transformer import transform_conversations, EeselHelpdeskTicket

def load_and_transform_conversations():
    # Load the JSON file
    with open("tmp/checkpoint_conversations_k1l9cw89.json", "r") as f:
        data = json.load(f)

    # Ensure data is a list
    if not isinstance(data, list):
        raise ValueError("Expected a list of conversations in the JSON file")

    # Filter out unnecessary metadata and extract tag names
    for conversation in data:
        if 'metadata' in conversation and 'tags' in conversation['metadata']:
            conversation['metadata'] = {
                'tags': [tag['name'] for tag in conversation['metadata']['tags']]
            }
        else:
            conversation['metadata'] = {'tags': []}
        
        # Extract only messages and tags
        conversation['messages'] = [msg for msg in conversation.get('messages', [])]
        conversation['tags'] = conversation['metadata'].get('tags', [])

    # Transform conversations
    transformed_data = transform_conversations(data)

    # Save the transformed data to a new JSON file
    output_file = "tmp/transformed_conversations.json"
    with open(output_file, "w") as f:
        json.dump([{
            'messages': ticket.messages,
            'tags': ticket.metadata['tags']
        } for ticket in transformed_data], f, indent=2)

    print(f"Transformed data saved to {output_file}")

if __name__ == "__main__":
    load_and_transform_conversations()
