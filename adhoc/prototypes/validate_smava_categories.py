import ast
import pandas as pd
import requests

# Function to get custom fields from Zendesk
def get_custom_fields(subdomain, api_token):
    url = f"https://{subdomain}.zendesk.com/api/v2/ticket_fields.json"
    response = requests.get(url, headers={
        "Authorization": api_token
    })
    response.raise_for_status()
    return response.json()['ticket_fields']

# Function to get ticket details by ticket ID
def get_ticket_details(subdomain, ticket_id, api_token):
    url = f"https://{subdomain}.zendesk.com/api/v2/tickets/{ticket_id}.json"
    response = requests.get(url, headers={
        "Authorization": api_token
    })
    response.raise_for_status()
    return response.json()['ticket']

# Load the CSV file
csv_file = "./smava_categorisation_output.csv"
df = pd.read_csv(csv_file)

# Zendesk credentials
subdomain = "smava"
api_token = "Basic xxxx"

# Get custom fields from Zendesk
custom_fields = get_custom_fields(subdomain, api_token)

# Find the IDs for the relevant custom fields
field_map = {}
for field in custom_fields:
    if field['title'] == "Interne Kategorie":
        field_map['Interne Kategorie'] = field['id']
    elif field['title'] == "interner Task":
        field_map['interner Task'] = field['id']

# Verify each ticket
results = []
for index, row in df.iterrows():
    try:
        metadata = ast.literal_eval(row['metadata'])
        ticket_id = metadata['ticket_id']
        level1_category = row['level1_category']
        level2_category = row['level2_category']

        level1_category_rationale = row['level1_category_rationale']
        level2_category_rationale = row['level2_category_rationale']

        ticket_details = get_ticket_details(subdomain, ticket_id, api_token)
        custom_field_values = {field['id']: field['value'] for field in ticket_details['custom_fields']}

        interner_kategorie = custom_field_values.get(field_map['Interne Kategorie'], None)
        interner_task = custom_field_values.get(field_map['interner Task'], None)

        l1_match = str(level1_category).lower() == str(interner_kategorie).lower()
        l2_match = str(level2_category).lower() == str(interner_task).lower()
        results.append({
            "ticket_id": ticket_id,
            "eesel - level1_category": level1_category,
            "eesel level2_category": level2_category,
            "smava - interner_kategorie": interner_kategorie,
            "smava - interner_task": interner_task,
            "eesel - l1_rationale": level1_category_rationale,
            "eesel - l2_rationale": level2_category_rationale,
            "original_message": row['original_message'],
            "metadata": row['metadata'],
            "l1_match": l1_match,
            "l2_match": l2_match
        })
    except Exception as e:
        print(str(e))

# Convert results to DataFrame and save to CSV
results_df = pd.DataFrame(results)
results_df.to_csv("verification_results.csv", index=False)