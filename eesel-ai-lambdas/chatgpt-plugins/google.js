export async function handler(event) {
    const path = event.rawPath;
    const headers = event.headers || {};
    const body = event.body || {};
    
    if (path === '/.well-known/ai-plugin.json') {
        const response = await fetch('https://oracle.eesel.app/.well-known/google/ai-plugin.json');
        const data = await response.text();
        return {
            statusCode: response.status,
            body: data,
        };
    
    } else if (path === '/.well-known/openapi.yaml') {
        const response = await fetch('https://oracle.eesel.app/.well-known/google/openapi.yaml');
        const data = await response.text();
        return {
            statusCode: response.status,
            body: data,
        };
    } else if (path === '/.well-known/logo.png') {
        const response = await fetch('https://oracle.eesel.app/.well-known/google/logo.png');
        const data = await response.arrayBuffer();
        let headers = {};
        for (let [key, value] of response.headers) {
            headers[key] = value;
        }
        return {
            statusCode: response.status,
            headers: headers,
            body: Buffer.from(data).toString('base64'),
            isBase64Encoded: true,
        };
    
    } else if (path === '/openai-plugin/chatgpt-query') {
    try {
         const h = { 
             "Authorization": event.headers['authorization'],
             "Content-Type": "application/json"
         };
         console.log(h, JSON.stringify(body), typeof body)
        const response = await fetch('https://oracle.eesel.app/openai-plugin/chatgpt-query', {
            method: 'POST',
            headers: h,
            body: body,
        });

        // If the response status indicates an error, throw an error
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.text();
        return {
            statusCode: response.status,
            body: data,
        };
    } catch (error) {
    console.error('Error during fetch:', error.toString());
    console.error('Stack Trace:', error.stack);

    return {
        statusCode: 500,
        body: `Internal Server Error: ${error.message}`,
    };
}

} else {
        return {
            statusCode: 404,
            body: 'Invalid path (' + path + ")",
        };
    }
};
