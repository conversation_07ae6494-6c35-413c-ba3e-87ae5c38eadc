{"name": "eesel-ai-crawler-lambda", "version": "0.0.1", "description": "eesel ai crawler", "dependencies": {"@crawlee/core": "^3.11.5", "@crawlee/playwright": "^3.11.5", "@sparticuz/chromium": "^129.0.0", "@types/turndown": "^5.0.1", "crypto": "^1.0.1", "joplin-turndown-plugin-gfm": "^1.0.12", "playwright": "^1.48.0", "turndown": "^7.1.2"}, "devDependencies": {"@apify/tsconfig": "^0.1.0", "@types/node": "^18.0.0", "ts-node": "^10.8.0", "typescript": "^5.0.0"}, "scripts": {"start": "npm run start:dev", "start:prod": "node dist/main.js", "start:dev": "ts-node-esm -T src/main.ts", "build": "npx tsc && cp src/*.js dist/", "package": "zip -r deployment.zip dist/*.js node_modules/", "upload": "aws s3 cp deployment.zip s3://code.eesel/crawler/playwright.zip --profile eeselAiDeployer", "update-lambda": "aws lambda update-function-code --function-name eeselCrawler --s3-bucket code.eesel --s3-key crawler/playwright.zip --profile eeselAiDeployer", "deploy": "source .env.prod && npm run build && npm run package && npm run upload && npm run update-lambda", "upload:local": "aws s3 cp deployment.zip s3://code.eesel/crawler/playwright.zip --profile localstack", "create-lambda:local": "aws lambda create-function --function-name eeselAiCrawler --runtime nodejs18.x --handler dist/index.handler --role arn:aws:iam::000000000000:role/lambda-role --code S3Bucket=\"code.eesel\",S3Key=\"crawler/playwright.zip\" --region us-east-1 --profile localstack", "update-lambda:local": "aws lambda update-function-code --function-name eeselAiCrawler --s3-bucket code.eesel --s3-key crawler/playwright.zip --profile localstack", "deploy:local": "npm run build && npm run package && npm run upload:local && npm run update-lambda:local"}, "author": "It's not you it's me", "license": "ISC"}