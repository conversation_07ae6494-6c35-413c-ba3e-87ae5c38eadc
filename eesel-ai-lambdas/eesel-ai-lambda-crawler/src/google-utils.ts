const isGoogleUrl = (url: string) => {
    const isGoogleDomain = url.includes("docs.google.com");
    const isHtmlView = url.includes('htmlpresent') || url.includes('htmlview') || url.includes('mobilebasic')
    return isGoogleDomain && isHtmlView;
}

const extractHTMLForSheets = () => {
    let resultBody = "";

    const sheets = document.querySelectorAll('div[id="sheets-viewport"] > div');
    sheets.forEach((sheet: any, index) => {
      const titles: any = document.querySelectorAll('ul[id="sheet-menu"] > li');
      const tableTitle = titles[index]?.innerText || "";
      const tableHTML = sheet.querySelector("table")?.outerHTML || "";

      resultBody += `\nTable Title: ${tableTitle}\n\nTable Content:\n\n${tableHTML}\n`;
    });

    return resultBody;
}

function getGoogleRedirectUrl(currentUrl: string) {
    const url = new URL(currentUrl);
    const currentDomain = url.hostname;
    const currentPathname = url.pathname;
  
    if (currentDomain.includes("docs.google.com")) {
      if (currentPathname.includes("document") && !currentPathname.includes("mobilebasic")) {
        return `${currentUrl.replace(
          "edit",
          "mobilebasic"
        )}`;
      } else if (currentPathname.includes("presentation") && !currentPathname.includes("htmlpresent")) {
        return `${currentUrl.replace(
          "edit",
          "htmlpresent"
        )}`;
      } else if (currentPathname.includes("spreadsheets") && !currentPathname.includes("htmlview")) {
        return `${currentUrl.replace(
          "edit",
          "htmlview"
        )}`;
      }
    }
  
    return currentUrl
  }

export default {
    extractHTMLForSheets,
    isGoogleUrl,
    getGoogleRedirectUrl
}
