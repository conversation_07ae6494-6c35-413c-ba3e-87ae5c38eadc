const {
  <PERSON>wright<PERSON><PERSON><PERSON>,
  createPlaywrightRouter,
} = require('@crawlee/playwright');
const {
  RequestQueue,
  Configuration,
} = require('@crawlee/core');
const chromium = require('@sparticuz/chromium');
const playwright = require('playwright');
const { randomUUID } = require('crypto');
const convertHtmlToMarkdown = require('./md-utils').default;

const {
  getMarkdownStringFromNotionDatabases,
  getMarkdownStringsFromNotionTables,
  isNotionDomain,
  openNotionToggles,
} = require('./notion-utils').default;

const { extractHTMLForSheets, isGoogleUrl, getGoogleRedirectUrl } =
  require('./google-utils').default;

  const { isZendesk, getZendeskArticleHtml } =
  require('./zendesk-utils').default;

const config = Configuration.getGlobalConfig();
config.set('availableMemoryRatio', 0.9);

const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const {
  DynamoDBDocumentClient,
  PutCommand,
  QueryCommand,
} = require('@aws-sdk/lib-dynamodb');
const { SQSClient, SendMessageCommand } = require('@aws-sdk/client-sqs');

const client = new DynamoDBClient({});
const dynamo = DynamoDBDocumentClient.from(client);
const sqsClient = new SQSClient({});

// Utility function to insert URL into DynamoDB
const insertUrlIntoDynamoDB = async (url, status, run_id) => {
  const timestamp = Math.floor(Date.now() / 1000); // current time in seconds
  const ttl = timestamp + 86400; // 24 hours in seconds
  const params = {
    TableName: 'EeselAICrawlerLog',
    Item: {
      url,
      status,
      run_id,
      ttl,
      uuid: randomUUID(),
    },
  };
  await dynamo.send(new PutCommand(params));
};

const getProcessedUrlsForRun = async (run_id, filterQueued) => {
  const params = {
    TableName: 'EeselAICrawlerLog',
    IndexName: 'RunIdIndex', // Assuming you have a GSI on run_id
    KeyConditionExpression: 'run_id = :run_id',
    ExpressionAttributeValues: {
      ':run_id': run_id,
    },
  };

  const result = await dynamo.send(new QueryCommand(params));
  const filteredItems = filterQueued
    ? result.Items.filter((item) => item.status == 'completed')
    : result.Items;
  const uniqueUrls = [...new Set(filteredItems.map((item) => item.url))];

  return uniqueUrls; // Returning an array of URLs
};

function replaceLongWords(input) {
  return input.split(' ').map(word => {
      if (word.length > 500) {
          return '...';
      }
      return word;
  }).join(' ');
}

const enqueueNextLink = async (messageBody) => {
  console.log('Adding to queue', messageBody);
  const params = {
    QueueUrl: process.env.CRAWLER_QUEUE,
    MessageBody: JSON.stringify(messageBody),
  };

  try {
    const result = await sqsClient.send(new SendMessageCommand(params));
    return result;
  } catch (error) {
    console.error('Error sending message to SQS for enqueuing:', error);
    throw error;
  }
};

const enqueueIngestion = async (messageBody) => {
  console.log('Adding to queue', messageBody);
  const params = {
    QueueUrl: process.env.INGEST_QUEUE,
    MessageBody: JSON.stringify(messageBody),
  };

  try {
    const result = await sqsClient.send(new SendMessageCommand(params));
    return result;
  } catch (error) {
    console.error('Error sending message to SQS for ingestion:', error);
    throw error;
  }
};
const checkUrlAgainstPaths = (url, paths="") => {
  const paths_list= paths.split(',');
  const isIncluded = paths_list.some(path => url.includes(path));
  return isIncluded;
}

const shouldSkipPage = async (page) => {
  const isMediaWiki = (await page
    .locator('css=div[id=content].mw-body')
    .count()) > 0;

  if (isMediaWiki) {
    currentUrl = await page.url();

    if (currentUrl.includes("Special:")) {
      return true;
    }

    if (currentUrl.includes("action=")) {
      return true;
    }

    if (currentUrl.includes("Category:")) {
      return true;
    }

    if (currentUrl.includes("File:")) {
      return true;
    }

    if (currentUrl.includes("Template:")) {
      return true;
    }

    if (currentUrl.includes("User:")) {
      return true;
    }

    if (currentUrl.includes("Talk:")) {
      return true;
    }

    const noTextMessage = "There is currently no text in this page";
    const isNoTextMessagePresent = (await page.locator(`:text("${noTextMessage}")`).count()) > 0;

    if (isNoTextMessagePresent) {
      return true;
    }
  }

  return false;
}

const createRouter = (cache, callback) => {
  const router = createPlaywrightRouter();

  router.addDefaultHandler(async ({ enqueueLinks, log, request, page }) => {
    if (request.loadedUrl && cache[request.loadedUrl]) {
      console.log('Skipping');
      return;
    }

    await insertUrlIntoDynamoDB(
      request.loadedUrl,
      'completed',
      request.userData.run_id
    );

    const title = await page.title();

    await page.waitForTimeout(5000);
    log.info(`${title}`, { url: request.loadedUrl });
    log.info(`Enqueueing new URLs`);

    const links = await page.$$('a[href]');
    const hrefs = await Promise.all(
      links.map(async (link) => await link.getAttribute('href'))
    );

    const skip = await shouldSkipPage(page)
    const latestProcessedUrls =
    (await getProcessedUrlsForRun(request.userData.run_id)) || [];

    if (!skip) {
      const { html, extraMarkdown } = await getRelevantHTMLSection(
        page,
        request.loadedUrl
      );
      let markdown = await convertHtmlToMarkdown(request.loadedUrl, html);

      markdown += '\n\n' + extraMarkdown;

      // sub out any google stuff that we changed.
      const originalUrl = request.loadedUrl
        .toString()
        .replace('mobilebasic', 'edit')
        .replace('htmlpresent', 'edit')
        .replace('htmlview', 'edit');

      let shouldIngest = true;
      if (request.userData.valid_pattern && request.userData.valid_pattern.length > 0) {
        const r = new RegExp(request.userData.valid_pattern);
        shouldIngest = r.test(originalUrl)
      }
      // Handle 'contains'
      let passesContains = true;
      if (request.userData.contains) {
        passesContains = checkUrlAgainstPaths(originalUrl, request.userData.contains);
      }

      // Handle 'does_not_contain'
      let passesDoesNotContain = true;
      if (request.userData.does_not_contain) {
        passesDoesNotContain = !checkUrlAgainstPaths(originalUrl, request.userData.does_not_contain);
      }
      if (shouldIngest && passesContains && passesDoesNotContain ) {
        await enqueueIngestion({
          ingest_jwt: request.userData.ingest_jwt,
          title,
          url: originalUrl,
          user_id: request.userData.user_id,
          job_id: request.userData.job_id,
          body: markdown.substr(0, 2 * 200 * 1024), // take max 200kb
        });
      }
    }

    if (request.userData.max_pages > 1) {
      for (const href of hrefs) {
        const urlObject = new URL(href, request.loadedUrl); // This resolves relative URLs

        console.log(urlObject.toString(), request.userData.original_hostname);
        if (urlObject.hostname.includes(request.userData.original_hostname)) {
          console.log('Adding to queue', urlObject.hostname);
          const u = `${urlObject.origin}${urlObject.pathname}${urlObject.search}`;

          if (
            !latestProcessedUrls.includes(u) &&
            latestProcessedUrls.length <= request.userData.max_pages
          ) {
            await enqueueNextLink({
              url: u,
              run_id: request.userData.run_id,
              original_hostname: request.userData.original_hostname,
              max_pages: request.userData.max_pages,
              ingest_jwt: request.userData.ingest_jwt,
              user_id: request.userData.user_id,
              valid_pattern: request.userData.valid_pattern,
              does_not_contain: request.userData.does_not_contain,
              contains: request.userData.contains,
              job_id: request.userData.job_id,
            });

            latestProcessedUrls.push(u);
            await insertUrlIntoDynamoDB(u, 'queued', request.userData.run_id);
          }
        }
      }
    }
  });

  return router;
};

let setup = false;
let queue = null;
let crawler = null;

const init = async () => {
  if (!setup) {
    queue = await RequestQueue.open('queue-' + Math.random());

    crawler = new PlaywrightCrawler({
      requestHandler: createRouter({}, (data) => {
        console.log('Callback');
      }),
      launchContext: {
        launchOptions: {
          args: chromium.args,
          defaultViewport: chromium.defaultViewport,
          executablePath: await chromium.executablePath(),
          headless: true,
          ignoreHTTPSErrors: true,
        },
      },
      requestQueue: queue,
      maxConcurrency: 1,
      minConcurrency: 1,
      // maxRequestsPerCrawl: 1,
      maxRequestRetries: 0, // disable retry
      keepAlive: true,
    });

    crawler.run([]);

    setup = true;
  }
};

async function getRelevantHTMLSection(page, url) {
  let html = '';
  let extraMarkdown = '';

  const numSalesforceSelectors = await page
    .locator('css=div[id=auraErrorMask]')
    .count();

  const isMediaWiki = (await page
    .locator('css=div[id=content].mw-body')
    .count()) > 0;

  const isReadme = (await page.locator('meta[name="readme-version"]').count() > 0) && (await page.locator("#content-head").count() > 0);

  if (numSalesforceSelectors > 0 && (url || '').includes('help/s/article')) {
    console.log('Salesforce page');
    html = await page.locator('css=article.content').innerHTML();
  } else if (isMediaWiki) {
    console.log("Media Wiki page");

    const currentUrl = await page.url();
    const urlObj = new URL(currentUrl);
    urlObj.searchParams.set('printable', 'yes');
    const printableUrl = urlObj.toString();

    await page.goto(printableUrl, { timeout: 10000 });
    await page.waitForTimeout(500);
    // 4. Get the entire page's HTML
    html = await page.locator('body').innerHTML();

  } else if (isNotionDomain(url)) {
    await page.evaluate(openNotionToggles);

    html = await page.locator('div.notion-page-content').innerHTML();

    await page.waitForTimeout(2500);

    extraMarkdown += await page.evaluate(getMarkdownStringFromNotionDatabases);
    extraMarkdown += await page.evaluate(getMarkdownStringsFromNotionTables);

    console.log(extraMarkdown);
  } else if (isGoogleUrl(url)) {
    if (url.includes('/spreadsheets')) {
      html = await page.evaluate(extractHTMLForSheets);
    } else {
      html = await page.innerHTML('body');
    }
  } else if (url.includes('atlassian.net')) {
    html = await page.locator('[data-testid="page-content-only"]').innerHTML();
  } else if (isReadme) {
    const contentHead = await page.locator('#content-head').innerHTML();
    const contentBody = await page.locator('#content-container > .content-body .markdown-body:first-child').innerHTML();

    html = replaceLongWords(contentHead) + "\n\n" + replaceLongWords(contentBody)
  } else if (await isZendesk(page, url)) {
    html = await getZendeskArticleHtml(page);
  } else {
    console.log('Default page');
    html = await page.innerHTML('body');

    try {
        await page.waitForSelector('article', { timeout: 500 })
        const articleElement = await page.$('article');
        if (articleElement) {
            const articleContent = await articleElement.innerHTML();
            if (articleContent) {
                html = articleContent;
            }
        }
    } catch (error) {
        if (error.name === 'TimeoutError') {
            // Handle the timeout error if needed, e.g., log a message or just ignore
            console.log('No article element found within the specified timeout.');
        } else {
            // Handle other potential errors
            console.error('An unexpected error occurred:', error);
        }
    }
  }

  return { html, extraMarkdown };
}

const checkQueueStatus = async (queue, delay) => {
  return new Promise(async (resolve) => {
    const isFinished = await queue.isFinished();
    if (isFinished) {
      console.log('Queue processing finished!');
      resolve();
      // Handle the finished state, maybe stop the lambda, or process results
    } else {
      console.log('Queue not finished yet. Checking again in', delay, 'ms.');
      setTimeout(async () => {
        await checkQueueStatus(queue, delay);
        resolve();
      }, delay);
    }
  });
};

const handler = async (event) => {
  await init();

  const processedUrlsMap = new Map(); // To store processed URLs for each run_id

  const shuffledRecords = event.Records.sort(() => 0.5 - Math.random());

  for (let record of shuffledRecords) {

    // dont process if more than 10 times
    const receiveCount = parseInt(record.attributes.ApproximateReceiveCount);
    if (receiveCount > 10) {
      console.log(`Message received more than 5 times, skipping: ${record.messageId}`);
      continue;
    }

    const { url, run_id, original_hostname, max_pages, ingest_jwt, user_id, valid_pattern, does_not_contain=null, contains=null, job_id } =
      JSON.parse(record.body);

    if (!url || !run_id) {
      console.log(record.body, typeof record.body);
      continue;
    }

    // Fetch processed URLs for the run_id if not already fetched
    if (!processedUrlsMap.has(run_id)) {
      const processedUrls = await getProcessedUrlsForRun(run_id, true);
      processedUrlsMap.set(run_id, new Set(processedUrls || []));
    }

    const processedUrlsSet = processedUrlsMap.get(run_id);

    if (processedUrlsSet.size >= (max_pages || 20)) {
      console.log(
        `Already processed 100 URLs for run_id: ${run_id}. Skipping...`
      );
      continue;
    }

    // Check if URL exists in our Set
    if (processedUrlsSet.has(url)) {
      console.log('URL already exists:', url);
      continue;
    }

    try {
      const urlObject = new URL(url);
      if (
        original_hostname &&
        !urlObject.hostname.includes(original_hostname)
      ) {
        console.log('Skipping due to hostname mismatch');
        // skip if different hostname
        continue;

      } else if (urlObject.hostname.includes("intercom.com")) {
        // skip intercom.com hostnames
        continue;
      } else {
        // swap out google URLs for the HTML views.
        const modifiedUrl = getGoogleRedirectUrl(url);

        // Add the URL to the processing queue
        console.log('Inserted URL into req queue.');
        await queue.addRequest({
          url: modifiedUrl,
          uniqueKey: `${run_id}:${modifiedUrl}`,
          userData: {
            run_id,
            original_hostname: original_hostname || urlObject.hostname,
            max_pages,
            ingest_jwt,
            user_id,
            valid_pattern,
            does_not_contain,
            contains,
            job_id
          },
        });

        // Add the URL to our Set to keep it updated
        processedUrlsSet.add(modifiedUrl);
      }
    } catch (ex) {
      console.error(ex);
      console.error(record.body)
    }
  }

  // wait for the queue to drain
  await checkQueueStatus(queue, 500);

  return {
    statusCode: 200,
    body: JSON.stringify({ message: 'Processing completed' }),
  };
};



module.exports = { handler };
