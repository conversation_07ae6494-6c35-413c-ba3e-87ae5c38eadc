import TurndownService from "turndown";

function cleanMarkdown(markdown: string) {
  // Split the Markdown string into an array of lines
  const lines = markdown.split("\n");

  // Filter out empty lines
  const nonEmptyLines = lines.filter((line) => line.trim() !== "");

  // Join the non-empty lines back into a single string
  const result = nonEmptyLines.join("\n");

  return result;
}
// Function to convert HTML to Markdown
export default async function convertHtmlToMarkdown(url: string, html: string) {
  // Wait for the turndown library to be loaded
  const turndownGfm = await import("joplin-turndown-plugin-gfm");
  const turndownService = new TurndownService();
  turndownService.use(turndownGfm.gfm);

  // Add custom rules for specific conversions (if needed)
  turndownService.addRule("customRule", {
    filter: [
      "script",
      "style",
      "link",
      "meta",
      "img",
      "noscript",
      "head",
      "nav",
      "footer",
    ],
    replacement: function () {
      return "";
    },
  });

  // Convert the HTML to Markdown
  // @ts-ignore - it's whinging about turndown being overloaded.
  let markdown = cleanMarkdown(turndownService.turndown(html));

  const baseURL = new URL(url).origin;
  if (baseURL) {
    // Convert relative URLs to absolute URLs in markdown
    const regex = /\]\((?!http)(\/[^)]+)\)/g; // match strings like (/relative-url) but not (http://...) or (https://...)
    markdown = markdown.replace(regex, `](${baseURL}$1)`); // prepend base URL to matched strings
  }

  // remove any unnecessary dashes
  markdown = markdown.replace(/-{3,}/g, "---");

  return markdown;
}
