/**
 * 
 * ==========================================================
 * All of these helper functions should be in page.evaluate()
 * ==========================================================
 */

export const isNotionDomain = (url: string) => url.includes("www.notion.so") || url.includes(".notion.site");

/**
 * Code to open all notion expanders, to be run in page.evaluate() for Playwright.
 */
export const openNotionToggles = () => {
    const toggleList = document.querySelectorAll(
        ".notion-toggle-block > div > .pseudoSelection > div"
    );

    toggleList.forEach((toggle: any) => {
        const sibling = toggle.parentElement?.nextElementSibling;
        const isCollapsed = sibling && sibling.childElementCount === 1;

        if (isCollapsed) {
            toggle.click();
        }
    });
}

function getMarkdownStringFromNotionDatabases() {
    // note that for tables with rows 100+, you'll need to actually scroll down to render
    // all the rows in the DOM
    // I tried automating scrolling but it's not trivial, so I'm just going to leave that for now
  
    let tables = document.querySelectorAll("div.notion-table-view");
  
    if (!tables.length) return "";
  
    const results: string[] = [];
  
    tables.forEach((table) => {
      let markdownTable = "";
      const headerCells = table.querySelectorAll(
        ".notion-table-view-header-cell"
      );
      const rows = table.querySelectorAll(
        ".notion-selectable.notion-page-block.notion-collection-item"
      );
  
      markdownTable += "| ";
      headerCells.forEach((headerCell: any) => {
        markdownTable += headerCell?.innerText?.trim() + " | ";
      });
      markdownTable += "\n| ";
      headerCells.forEach(() => {
        markdownTable += "--- | ";
      });
  
      rows.forEach((row) => {
        markdownTable += "\n| ";
  
        headerCells.forEach((headerCell: any, index) => {
          // if row's children's first element is a label then ignore that
          const cell: any = Array.from(row.children).filter(
            (element) => element.tagName === "DIV"
          )[index];
  
          if (!cell) {
            return;
          }
          markdownTable +=
            (cell.innerText || "").trim().replace(/[\r\n]+/g, " ") + " | ";
        });
      });
  
      results.push(markdownTable);
    });
  
    return results.join("\n\n");
  }
 
  function getMarkdownStringsFromNotionTables() {
    // Find all divs with the class '.notion-table-block'
    const tableDivs = document.querySelectorAll(".notion-table-content");
  
    if (!tableDivs.length) {
      return "";
    }
  
    let allMarkdownTables = "";
  
    tableDivs.forEach((tableDiv) => {
      // Find the table within the current div
      const table = tableDiv.querySelector("table");
  
      if (!table) {
        return;
      }
  
      // Initialize the Markdown output for the current table
      let markdown = "";
  
      // Iterate through the table header and add it to the Markdown output
      const headerRow = table.querySelector("thead tr");
      if (headerRow) {
        headerRow.querySelectorAll("th").forEach((headerCell: any) => {
          markdown += `| ${headerCell.textContent.trim()} `;
        });
        markdown += "|\n";
  
        // Add the separator line
        headerRow.querySelectorAll("th").forEach(() => {
          markdown += "| --- ";
        });
        markdown += "|\n";
      }
  
      // Iterate through the table body rows and add them to the Markdown output
      const rows = table.querySelectorAll("tbody tr");
      rows.forEach((row) => {
        row.querySelectorAll("td").forEach((cell: any) => {
          markdown += `| ${cell.textContent.trim()} `;
        });
        markdown += "|\n";
      });
  
      allMarkdownTables += markdown + "\n";
    });
  
    return allMarkdownTables;
  }


  export default {
    getMarkdownStringsFromNotionTables,
    getMarkdownStringFromNotionDatabases,
    isNotionDomain,
    openNotionToggles
  }