const isZendesk = async (page: any, url: string) => {
    const head = await page.locator("head").innerHTML();
    return head.includes("zdassets") && url.includes("/hc/");
}

const getZendeskArticleHtml = async (page: any): Promise<string> => {
    const body = await page.locator(".article__body").innerHTML({ timeout: 2000 });
    return body;
}

export default {
    isZendesk,
    getZendeskArticleHtml
}