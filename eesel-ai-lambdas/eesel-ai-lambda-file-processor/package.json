{"name": "eesel-ai-crawler-lambda", "version": "0.0.1", "description": "eesel ai crawler", "dependencies": {"@opendocsg/pdf2md": "^0.1.26", "csv-to-markdown-table": "^1.3.1", "csvtojson": "^2.0.10", "node-fetch": "^2.7.0"}, "devDependencies": {"@types/aws-lambda": "^8.10.119", "@types/node": "^18.17.15", "aws-sdk": "^2.1454.0", "ts-node": "^10.8.0", "typescript": "^5.0.0"}, "scripts": {"build": "npx tsc", "package": "zip -r deployment.zip dist/*.js node_modules/", "upload": "aws s3 cp deployment.zip s3://code.eesel/crawler/file-processor.zip --profile eeselAiDeployer", "update-config": "ATL_KEY=$(printenv ATL_KEY) && aws lambda update-function-configuration --function-name eeselAiFileProcessor --environment \"Variables={ENV=prod,ATL_KEY=$ATL_KEY,INGEST_QUEUE=https://sqs.us-east-1.amazonaws.com/059782158527/EeselAiIngestionQueue}\" --profile eeselAiDeployer", "update-lambda": "aws lambda update-function-code --function-name eeselAiFileProcessor --s3-bucket code.eesel --s3-key crawler/file-processor.zip --profile eeselAiDeployer", "deploy": "if [ -z \"$ATL_KEY\" ]; then echo 'Error: ATL_KEY is not set' && exit 1; fi &&npm run build && npm run package && npm run upload && npm run update-config && npm run update-lambda", "upload:local": "aws s3 cp deployment.zip s3://code.eesel/crawler/file-processor.zip --profile localstack", "create-lambda:local": "aws lambda create-function --function-name eeselAiFileProcessor --runtime nodejs18.x --handler dist/index.handler --role arn:aws:iam::000000000000:role/lambda-role --code S3Bucket=\"code.eesel\",S3Key=\"crawler/file-processor.zip\" --region us-east-1 --profile localstack", "update-lambda:local": "aws lambda update-function-code --function-name eeselAiFileProcessor --s3-bucket code.eesel --s3-key crawler/file-processor.zip --region us-east-1 --profile localstack", "deploy:local": "npm run build && npm run package && npm run upload:local && npm run update-lambda:local"}, "author": "It's not you it's me", "license": "ISC"}