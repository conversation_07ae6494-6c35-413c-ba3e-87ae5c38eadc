import { S3 } from 'aws-sdk';
import * as pdf2md from '@opendocsg/pdf2md';
import { <PERSON><PERSON>, SQSEvent } from 'aws-lambda';
const { SQSClient, SendMessageCommand } = require("@aws-sdk/client-sqs");

const csv = require("csvto<PERSON>son");
const UPDATE_PAGES_URL = `${process.env.SERVER_URL}/update-total-pages`;

const MAX_BYTES_FOR_MARKDOWN = 1 * 200 * 1024; // 200KB
const CHUNK_SIZE_FOR_LARGE_FILES = 1 * 100 * 1024; // 200KB

const MAX_BYTES_FOR_DIRECT_INGEST = 20 * 1024 * 1024; // 20MB

type IngestFileEvent = {
  input_file_key: string,
  output_file_key: string,
  file_name: string,
  file_uuid: string,
  ingest_jwt: string,
  namespace_id: string,
  user_id: string
  sequence_id: number,
  job_id?: string,

  // external URL
  is_external_url?: boolean;
  external_url?: string;
  url_to_store?: string;
}

type IngestMarkdownEvent = {
  ingest_jwt: string,
  job_id?: string,
  title: string,
  url: string,
  user_id: string,
  body: string,
  sequence_id: number
}

type FileType = 'pdf' | 'csv' | 'txt' | 'unknown';
type Markdown = string;

const S3_FILESTORE_BUCKET_NAME = "eesel-ai-filestore";

const s3 = new S3(process.env.ENV !== 'prod' ? {
  endpoint: process.env.S3_ENDPOINT,
  s3ForcePathStyle: true,
} : undefined);
const sqsClient = new SQSClient({});

const enqueueIngestion = async (messageBody: IngestMarkdownEvent) => {
  console.log("Adding to queue", messageBody)
  const params = {
    QueueUrl: process.env.INGEST_QUEUE,
    MessageBody: JSON.stringify(messageBody),
  };

  try {
    const result = await sqsClient.send(new SendMessageCommand(params));
    return result;
  } catch (error) {
    console.error("Error sending message to SQS for ingestion:", error);
    throw error;
  }
};

function getTypeFromName(name: string): FileType {
  if (name.endsWith(".csv")) {
    return "csv"
  }
  if (name.endsWith(".pdf")) {
    return "pdf"
  }
  if (name.endsWith(".txt")) {
    return "txt"
  }
  return "unknown";
}

async function getMarkdownForFile(data: Buffer, type: FileType): Promise<Markdown> {
  if (type == "pdf") {
    return await pdf2md.default(data as Buffer);
  }
  if (type == "txt") {
    return data.toString("utf-8")
  }
  if (type == "csv") {
    const json = await csv({ noheader: false, output: "json" })
      .fromString(data.toString("utf-8"))
    return JSON.stringify(json, null, "\t")
  }
  return "";
}

async function updateTotalPages(job_id: string, total_pages: number, token: string) {
  try {
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
  }
  const response = await fetch(UPDATE_PAGES_URL, {
    method: 'POST',
    headers: headers,
      body: JSON.stringify({ job_id, total_pages })
    });
  } catch (ex) {
    console.error("Error updating total pages", ex);
  }
}

const processUploadedFile = async (body: IngestFileEvent) => {
  const params = {
    Bucket: S3_FILESTORE_BUCKET_NAME,
    Key: `${body.input_file_key}`
  };

  try {
    const data = await s3.getObject(params).promise();

    console.log("Starting processing file " + body.input_file_key + " with " + data.ContentLength + " bytes")

    const type: FileType = getTypeFromName(body.file_name)

    console.log("About to process file of type " + type)
    const text: Markdown = await getMarkdownForFile(data.Body as Buffer, type)

    // text has been processed.

    console.log("Finished processing markdown, total of " + text.length + " bytes")

    // output it to s3 now.
    const outputParams = {
      Bucket: S3_FILESTORE_BUCKET_NAME,
      Key: `${body.output_file_key}`,
      Body: text,
      ContentType: 'text/markdown',
      Tagging: `namespace=${body.namespace_id}`
    };

    console.log(outputParams)

    await s3.putObject(outputParams).promise();

    if (text.length < MAX_BYTES_FOR_MARKDOWN) {
      // also enqueue ingestion
      if (body.job_id) {
        await updateTotalPages(body.job_id, 1, body.ingest_jwt);
      }
      await enqueueIngestion({
        ingest_jwt: body.ingest_jwt,
        title: "File - " + body.file_name,
        url: `https://dashboard.eesel.ai/s/${body.namespace_id}/${body.file_uuid}/${encodeURIComponent(body.file_name)}`,
        user_id: body.user_id,
        body: text.substr(0, MAX_BYTES_FOR_MARKDOWN), // take max 200kb
        sequence_id: body.sequence_id,
        job_id: body.job_id,
      });
    } else {
      let totalChunks = Math.ceil(text.length / CHUNK_SIZE_FOR_LARGE_FILES);
      if (body.job_id) {
        await updateTotalPages(body.job_id, totalChunks, body.ingest_jwt);
      }
      for (let i = 0; i < totalChunks; i++) {
        let start = i * CHUNK_SIZE_FOR_LARGE_FILES;
        let end = start + CHUNK_SIZE_FOR_LARGE_FILES;
        let chunkText = text.substring(start, end);

        await enqueueIngestion({
          ingest_jwt: body.ingest_jwt,
          title: "File - " + body.file_name,
          url: `https://dashboard.eesel.ai/s/${body.namespace_id}/${body.file_uuid}/${encodeURIComponent(body.file_name)}?chunk=${i}`,
          user_id: body.user_id,
          body: chunkText,
          sequence_id: body.sequence_id,
          job_id: body.job_id,
        });
      }
    }
  } catch (ex: any) {
    console.error(`Failed to process ${body.input_file_key}: ${ex.message}`);
  }
}

const MAX_FILE_SIZE = 25 * 1024 * 1024; // 20MB

const processExternalUrl = async (body: IngestFileEvent) => {
  try {
    if (!body.external_url) {
      return;
    }

    console.log("Processing external URL", body.external_url)
    console.log("URL to store", body.url_to_store)

    // Parse the URL to handle query params
    const url = new URL(body.url_to_store || body.external_url);
    const pathname = url.pathname.toLowerCase(); // Extracts the path and converts to lowercase
    const nameParam = url.searchParams.get('name')?.toLowerCase();

    console.log("Params:", url, pathname, nameParam)

    // Check if the file is a PDF or an Atlassian-hosted PDF
    const isAtlassianPDF = url.host.includes("atlassian.net");
    if (!pathname.endsWith(".pdf") && !isAtlassianPDF) {
      console.error("File was not a PDF.", pathname);
      return;
    }

    // Check the file size before downloading
    const headResponse = await fetch(body.external_url, { method: 'HEAD' });
    const contentLength = headResponse.headers.get('content-length');
    if (contentLength && parseInt(contentLength) > MAX_FILE_SIZE) {
      console.error(`File size exceeds the limit of ${MAX_FILE_SIZE} bytes.`);
      return;
    }

    // Download file from the provided URL
    let pdfUrl = body.external_url;

    if (isAtlassianPDF) {
      console.log("Fetching the Atlassian media PDF URL")
      const initialResponse = await fetch(body.external_url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${process.env.ATL_KEY}`
        }
      });
      if (!initialResponse.ok) {
        throw new Error(`Failed to fetch initial URL: ${initialResponse.statusText}`);
      }
      const initialData = await initialResponse.json();
      pdfUrl = initialData.redirectUrl;
    }

    console.log("Attempting to download ", pdfUrl)
    const response = await fetch(pdfUrl, { redirect: 'follow' });


    if (response.redirected) {
      console.log(`Redirected to: ${response.url}`);
    }

    if (!response.ok) {
      throw new Error(`Failed to download file: ${response.statusText}`);
    }
    const data = await response.arrayBuffer(); // Use arrayBuffer for binary data
    // Extract the file name from the URL, ensuring it ends with .pdf
    const urlParts = pdfUrl.split('/');
    let fileName = urlParts[urlParts.length - 1]; // Get the last part of the URL

    console.log("Starting processing file " + pathname + " with " + data.byteLength + " bytes");

    const type = getTypeFromName(body.external_url);

    console.log("About to process file of type " + type);
    // Convert ArrayBuffer to Buffer for Node.js usage
    const buffer = Buffer.from(data);
    const text = await getMarkdownForFile(buffer, type);

    // text has been processed.
    console.log("Finished processing markdown, total of " + text.length + " bytes");

    const urlToStore = body.url_to_store || body.external_url;

    if (text.length < MAX_BYTES_FOR_MARKDOWN) {
      // also enqueue ingestion
      console.log("Enqueuing ingestion for " + urlToStore)
      if (body.job_id) {
        await updateTotalPages(body.job_id, 1, body.ingest_jwt);
      }
      await enqueueIngestion({
        ingest_jwt: body.ingest_jwt,
        title: "File - " + pathname,
        url: urlToStore,
        user_id: body.user_id,
        body: text.substr(0, MAX_BYTES_FOR_MARKDOWN), // take max 200kb
        sequence_id: body.sequence_id,
        job_id: body.job_id,
      });
    } else {
      let totalChunks = Math.ceil(text.length / CHUNK_SIZE_FOR_LARGE_FILES);
      if (body.job_id) {
        await updateTotalPages(body.job_id, totalChunks, body.ingest_jwt);
      }
      for (let i = 0; i < totalChunks; i++) {
        let start = i * CHUNK_SIZE_FOR_LARGE_FILES;
        let end = start + CHUNK_SIZE_FOR_LARGE_FILES;
        let chunkText = text.substring(start, end);

        console.log("Enqueuing ingestion for " + urlToStore + "?chunk=" + i)
        await enqueueIngestion({
          ingest_jwt: body.ingest_jwt,
          title: "File - " + fileName,
          url: `${urlToStore}?chunk=${i}`,
          user_id: body.user_id,
          body: chunkText,
          sequence_id: body.sequence_id,
          job_id: body.job_id,
        });
      }
    }
  } catch (ex) {
    console.error(`Failed to process ${body.external_url || "unknown_external_url"}: ${ex}`);
  }
}

const convertFileToMd: Handler<SQSEvent> = async (event: SQSEvent) => {
  for (const record of event.Records) {
    const body: IngestFileEvent = JSON.parse(record.body);
    let retryCount = 0;
    try {
      retryCount = Number(record.attributes.ApproximateReceiveCount);
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error(`Failed to parse retry count: ${error.message}. Payload: ${JSON.stringify(body)}`);
      } else {
        console.error(`Failed to parse retry count: ${String(error)}. Payload: ${JSON.stringify(body)}`); // Handle non-Error cases
      }
    }

    if (retryCount > 3) {
      console.error(`Message has been retried ${retryCount} times. Failing the message. Payload: ${JSON.stringify(body)}`);
      return { statusCode: 200, body: 'Message processing failed after max retries.' };
    }

    if (body.is_external_url) {
      console.log("External URL");
      await processExternalUrl(body);
    } else {
      console.log("Uploaded File");
      await processUploadedFile(body);
    }
  }

  return {
    statusCode: 200,
    body: 'Success!'
  };
};

module.exports = { handler: convertFileToMd };
