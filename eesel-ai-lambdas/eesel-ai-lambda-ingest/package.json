{"name": "eesel-ai-crawler-lambda", "version": "0.0.1", "description": "eesel ai crawler", "dependencies": {"@types/turndown": "^5.0.1", "joplin-turndown-plugin-gfm": "^1.0.12", "node-fetch": "^2.7.0", "turndown": "^7.1.2"}, "devDependencies": {"@apify/tsconfig": "^0.1.0", "@types/node": "^18.0.0", "ts-node": "^10.8.0", "typescript": "^5.0.0"}, "scripts": {"build": "npx tsc && cp src/*.js dist/", "package": "zip -r deployment.zip dist/*.js node_modules/", "upload": "aws s3 cp deployment.zip s3://code.eesel/crawler/ingest.zip --profile eeselAiDeployer", "update-config": "aws lambda update-function-configuration --function-name eeselAiCrawlerIngest --environment \"Variables={SERVER_URL=https://oracle.eesel.app}\" --profile eeselAiDeployer", "update-lambda": "aws lambda update-function-code --function-name eeselAiCrawlerIngest --s3-bucket code.eesel --s3-key crawler/ingest.zip --profile eeselAiDeployer", "deploy": "npm run build && npm run package && npm run upload && npm run update-config && npm run update-lambda", "upload:local": "aws s3 cp deployment.zip s3://code.eesel/crawler/ingest.zip --profile localstack", "create-lambda:local": "aws lambda create-function --function-name eeselAiCrawlerIngest --runtime nodejs18.x --handler dist/index.handler --role arn:aws:iam::000000000000:role/lambda-role --code S3Bucket=\"code.eesel\",S3Key=\"crawler/ingest.zip\" --region us-east-1 --profile localstack", "update-lambda:local": "aws lambda update-function-code --function-name eeselAiCrawlerIngest --s3-bucket code.eesel --s3-key crawler/ingest.zip --region us-east-1 --profile localstack", "deploy:local": "npm run build && npm run package && npm run upload:local && npm run update-lambda:local"}, "author": "It's not you it's me", "license": "ISC"}