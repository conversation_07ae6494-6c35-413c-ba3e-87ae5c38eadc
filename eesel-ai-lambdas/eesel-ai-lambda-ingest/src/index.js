const INGEST_URL = `${process.env.SERVER_URL}/ingest`;
const SCRAPE_STATUS_URL = `${process.env.SERVER_URL}/scrape-status`;

function cleanSourceURL(url) {
    let parsedUrl = new URL(url);

    if (parsedUrl.hostname.includes('vfm.de')) {
        let params = parsedUrl.searchParams;

        params.delete('autologin_id');
        params.delete('logintype');
    }

    return parsedUrl.toString();
}

const handler = async (event) => {
    const batches = {};
    for (let record of event.Records) {
        const { url, title, body, ingest_jwt, user_id, sequence_id, job_id } = JSON.parse(record.body);

        if (!batches[ingest_jwt]) {
            batches[ingest_jwt] = { records: [], user_id: user_id };
        }

        const recordObj = {
            pageTitle: title,
            pageBody: body,
            source: cleanSourceURL(url),
            user_id,
            job_id,

        };

        if (sequence_id) {
            recordObj.sequence_id = sequence_id;
        }

        batches[ingest_jwt].records.push(recordObj);
    }

    for (let [jwt, data] of Object.entries(batches)) {
        const headers = {
            'Authorization': `Bearer ${jwt}`,
            'Content-Type': 'application/json'
        };

        if (data.records[0]?.sequence_id) {
            headers['X-Sequence-Id'] = data.records[0].sequence_id;
        }

        console.log(JSON.stringify(data.records))
        console.log(JSON.stringify(jwt))

        const response = await fetch(INGEST_URL, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(data.records)
        });

        if (response.status !== 200) {
            console.log(`Error occurred while sending data: ${await response.text()}`);
        } else {
            console.log("Batch sent successfully");
        }
    }

    return {
        statusCode: 200,
        body: JSON.stringify({ message: 'Processing completed' })
    };
};

module.exports = { handler };
