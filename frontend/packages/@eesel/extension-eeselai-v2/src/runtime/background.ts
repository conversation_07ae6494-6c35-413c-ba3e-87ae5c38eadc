/// <reference lib="webworker" />

import { SIDEPANEL_KEY } from '@/utils/constants';
import { MessageTypes } from './messages';
import { v4 as uuidv4 } from 'uuid';
import logger, { initGlobalConsoleInterceptor } from '../utils/fileLogger';
import type { 
  ExtensionMessage,
  ExtensionResponse,
  MessageSender
} from '../types/extension-types';

// State variables
let isSidePanelOpen = false;

// Initialize logger
initGlobalConsoleInterceptor();
logger.info('Extension background script starting...', { timestamp: new Date().toISOString() });

// Set up context menus when extension is installed
chrome.runtime.onInstalled.addListener(() => {
  logger.info('Extension installed/updated');
  
  // Create debug menu items
  chrome.contextMenus.create({
    id: 'logs-menu',
    title: 'Debug Logs',
    contexts: ['action']
  });

  chrome.contextMenus.create({
    id: 'download-logs',
    parentId: 'logs-menu',
    title: 'Download Logs',
    contexts: ['action']
  });

  chrome.contextMenus.create({
    id: 'clear-logs',
    parentId: 'logs-menu',
    title: 'Clear Logs',
    contexts: ['action']
  });

  chrome.contextMenus.create({
    id: 'log-size',
    parentId: 'logs-menu',
    title: 'Show Log Size',
    contexts: ['action']
  });

  logger.info('Context menu items created');
});

// Handle context menu clicks
chrome.contextMenus.onClicked.addListener((info) => {
  const iconUrl = chrome.runtime.getURL('./assets/eesel-browser-icon-48.png');
  console.log('[Debug] Context menu clicked:', info.menuItemId, 'using icon:', iconUrl);
  
  switch (info.menuItemId) {
    case 'download-logs':
      console.log('[Debug] Attempting to download logs...');
      logger.downloadLogs()
        .then(() => {
          console.log('[Debug] Download succeeded');
          chrome.notifications.create({
            type: 'basic',
            iconUrl,
            title: 'Logs Downloaded',
            message: 'Debug logs have been downloaded successfully.'
          });
        })
        .catch((error: Error) => {
          console.error('[Debug] Download failed:', error);
          chrome.notifications.create({
            type: 'basic',
            iconUrl,
            title: 'Download Failed',
            message: `Failed to download logs: ${error.message}`
          });
        });
      break;

    case 'clear-logs':
      console.log('[Debug] Attempting to clear logs...');
      logger.flush()
        .then(() => {
          console.log('[Debug] Flushed pending logs before clearing');
          return logger.clearLogs();
        })
        .then(() => {
          console.log('[Debug] Clear succeeded');
          chrome.notifications.create({
            type: 'basic',
            iconUrl,
            title: 'Logs Cleared',
            message: 'Debug logs have been cleared successfully.'
          });
        })
        .catch((error: Error) => {
          console.error('[Debug] Clear failed:', error);
          chrome.notifications.create({
            type: 'basic',
            iconUrl,
            title: 'Clear Failed',
            message: `Failed to clear logs: ${error.message}`
          });
        });
      break;

    case 'log-size':
      console.log('[Debug] Attempting to get log size...');
      logger.flush()
        .then(() => {
          console.log('[Debug] Flushed pending logs before getting size');
          return logger.getLogSize();
        })
        .then((sizeInBytes: number) => {
          console.log('[Debug] Get size succeeded:', sizeInBytes, 'bytes');
          const sizeInKB = (sizeInBytes / 1024).toFixed(2);
          const sizeInMB = (sizeInBytes / (1024 * 1024)).toFixed(2);
          chrome.notifications.create({
            type: 'basic',
            iconUrl,
            title: 'Log Size',
            message: `Current log size: ${sizeInKB}KB (${sizeInMB}MB)`
          });
        })
        .catch((error: Error) => {
          console.error('[Debug] Get size failed:', error);
          chrome.notifications.create({
            type: 'basic',
            iconUrl,
            title: 'Size Check Failed',
            message: `Failed to check log size: ${error.message}`
          });
        });
      break;
  }
});

chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'CHECK_READY') {
    sendResponse({ ready: true });
  }
});

// Handle connections from sidepanel
chrome.runtime.onConnect.addListener((port) => {
  if (port.name === SIDEPANEL_KEY) {
    logger.info('Sidepanel connected');
    isSidePanelOpen = true;

    port.onDisconnect.addListener(() => {
      logger.info('Sidepanel disconnected');
      isSidePanelOpen = false;
    });
  }
});

// Get sidepanel state
function getSidePanelState(): boolean {
  return isSidePanelOpen;
}

// Handle messages from content scripts
chrome.runtime.onMessage.addListener(function handleMessage(
  message: ExtensionMessage, 
  sender: MessageSender,
  sendResponse: (response: ExtensionResponse) => void
): boolean {
  if (message.type === MessageTypes.FORWARD_CONTEXT_SIDEPANEL) {
    if (sender.tab?.id) {
      const sessionId = uuidv4();
      chrome.runtime.sendMessage({
        type: MessageTypes.REQUEST_CONTEXT,
        url: message.url,
        params: message.params,
        sessionId
      });
      sendResponse({ success: true });
    } else {
      sendResponse({ success: false, error: 'No tab ID found' });
    }
  }
  // Indicate we want to use sendResponse asynchronously
  return true;
});

let lastExecutionTimes: { [url: string]: number } = {};

chrome.tabs.onUpdated.addListener((tabId, info, tab) => {
  if (!tab.active || !getSidePanelState()) {
    console.debug('onUpdated: Tab is not active.');
    return;
  }

  const now = Date.now();
  const url = tab.url || '';
  const timeSinceLastExecution = now - (lastExecutionTimes[url] || 0);

  if (timeSinceLastExecution >= 500) {
    lastExecutionTimes[url] = now;
    if (url.startsWith('http://') || url.startsWith('https://')) {
      chrome.tabs.sendMessage(tab.id as number, {
        type: MessageTypes.FORWARD_CONTEXT_SIDEPANEL,
        url: url,
        params: {
          limit: true,
        },
      });
    }
    else {
      chrome.runtime.sendMessage({
        type: MessageTypes.CLEAR_CONTEXT,
      });
      console.debug('onUpdated: URL is not a valid webpage.');
    }
  }
});

chrome.tabs.onActivated.addListener((activeInfo) => {
  if (!getSidePanelState()) {
    return;
  }
  chrome.tabs.query({}, (tabs) => {
    if (chrome.runtime.lastError) {
      console.warn('Error querying tabs:', chrome.runtime.lastError);
      return;
    }

    const activeTab = tabs.find((tab) => tab.id === activeInfo.tabId);
    if (!activeTab) {
      console.warn('Invalid Tab');
      return;
    }

    if (activeTab.url?.startsWith('http://') || activeTab.url?.startsWith('https://')) {
      chrome.tabs.sendMessage(activeTab?.id as number, {
        type: MessageTypes.FORWARD_CONTEXT_SIDEPANEL,
        url: activeTab?.url,
        params: {
          limit: true,
        },
      });
    }
    else {
      chrome.runtime.sendMessage({
        type: MessageTypes.CLEAR_CONTEXT,
      });
      console.debug('onActivated: URL is not a valid webpage.');
    }
  });
});

chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === MessageTypes.GET_NAMESPACES) {
    console.log('[Eesel Gmail] Background: Fetching namespaces...');
    fetch('https://dashboard.eesel.ai/api/proxy/namespaces', {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
      },
    })
      .then((res) => {
        console.log('[Eesel Gmail] Background: Namespaces response status:', res.status);
        return res.json();
      })
      .then((data) => {
        console.log('[Eesel Gmail] Background: Namespaces data:', data);
        if (!data.namespaces) {
          console.error('[Eesel Gmail] Background: No namespaces in response data');
          sendResponse({ error: 'Invalid response format' });
          return;
        }
        sendResponse({ namespaces: data.namespaces });
      })
      .catch((err) => {
        console.error('[Eesel Gmail] Background: Error fetching namespaces:', err);
        sendResponse({ error: 'Failed to fetch namespaces' });
      });
    return true;
  }

  if (message.type === MessageTypes.REQUEST_CONTEXT) {
    console.log('[Eesel Gmail] Background: Processing AI request...');
    console.log('[Eesel Gmail] Using namespace ID:', message.namespaceId);

    // Create a promise to handle the API call
    const handleRequest = async () => {
      try {
        // First, get the user's authentication token
        const tokenResponse = await fetch('https://dashboard.eesel.ai/api/proxy/token', {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
        });

        if (!tokenResponse.ok) {
          console.error('[Eesel Gmail] Failed to get auth token:', tokenResponse.status);
          return { text: 'Error: Authentication failed. Please sign in again.' };
        }

        const tokenData = await tokenResponse.json();
        if (!tokenData.token) {
          console.error('[Eesel Gmail] No token in response');
          return { text: 'Error: Authentication failed. Please sign in again.' };
        }

        console.log('[Eesel Gmail] Got auth token successfully');

        const sessionId = uuidv4();

        // Use the correct endpoint format based on webapp-dashboard
        let url: string;
        if (message.namespaceId) {
          // Use namespace-specific query endpoint
          url = `https://oracle.eesel.app/namespaces/${encodeURIComponent(message.namespaceId)}/query?query=${encodeURIComponent(message.message)}&session_id=${sessionId}`;
          console.log('[Eesel Gmail] Using namespace-specific endpoint:', message.namespaceId);
        } else {
          console.warn('[Eesel Gmail] No namespace ID provided, this will likely fail');
          url = `https://oracle.eesel.app/assistant/invoke?session_id=${sessionId}`;
        }

        // Use the correct payload structure for /namespaces/{id}/query endpoint
        // Based on server-side code, it expects: history, query in the body
        const payload = {
          history: [], // Empty history for now, could be enhanced later
          query: message.message,
        };

        console.log('[Eesel Gmail] Making API call to:', url);
        console.log('[Eesel Gmail] Payload:', payload);

        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${tokenData.token}`,
            'x-eesel-message-request-version': 'v2', // Required header from webapp-dashboard
          },
          credentials: 'include',
          body: JSON.stringify(payload),
        });

        // Log the response details
        console.log('[Eesel Gmail] API Response Status:', response.status);
        console.log('[Eesel Gmail] API Response Headers:', Object.fromEntries(response.headers.entries()));

        const responseText = await response.text();
        console.log('[Eesel Gmail] Raw API response text:', responseText);
        console.log('[Eesel Gmail] Response text length:', responseText.length);

        // Check if response was successful
        if (!response.ok) {
          console.error('[Eesel Gmail] API request failed with status:', response.status);
          console.error('[Eesel Gmail] Response text:', responseText);
          return { text: `Error: API request failed with status ${response.status}. ${responseText}` };
        }

        // Try to parse the response as JSON
        let data;
        try {
          data = JSON.parse(responseText);
          console.log('[Eesel Gmail] Successfully parsed JSON response:', data);
        } catch (parseError) {
          console.error('[Eesel Gmail] Failed to parse API response as JSON:', parseError);
          console.error('[Eesel Gmail] Response text that failed to parse:', responseText);
          console.error('[Eesel Gmail] Parse error details:', parseError instanceof Error ? parseError.message : String(parseError));
          return { text: `Error: Invalid JSON response from AI service. Raw response: ${responseText.substring(0, 200)}...` };
        }
        
        if (!data || typeof data !== 'object') {
          console.error('[Eesel Gmail] Invalid response format:', data);
          return { text: 'Error: Invalid response format from AI service.' };
        }

        const aiResponseText = data.message || data.text || 'No reply from AI.';
        return { text: aiResponseText };
      } catch (err) {
        console.error('[Eesel Gmail] API call error:', err);
        return { text: 'Sorry, there was an error contacting Eesel AI.' };
      }
    };

    // Execute the request and send response
    handleRequest().then(sendResponse);
    
    // Return true to indicate we will send a response asynchronously
    return true;
  }

  if (message.type === MessageTypes.CHECK_AUTH) {
    console.log('[Eesel] Background: Checking authentication...');
    fetch('https://dashboard.eesel.ai/api/proxy/token', {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
    })
      .then((res) => {
        console.log('[Eesel] Background: Auth check response status:', res.status);
        if (res.ok) {
          return res.json();
        } else {
          throw new Error(`HTTP ${res.status}: ${res.statusText}`);
        }
      })
      .then((tokenData) => {
        console.log('[Eesel] Background: Token data received:', tokenData);
        if (tokenData.token) {
          sendResponse({ 
            success: true, 
            token: tokenData.token, 
            user: tokenData.user || {} 
          });
        } else {
          sendResponse({ success: false, error: 'No token in response' });
        }
      })
      .catch((err) => {
        console.log('[Eesel] Background: Auth check failed:', err.message);
        sendResponse({ success: false, error: err.message });
      });
    return true; // Keep message channel open for async response
  }
});
