// Test if file is being executed
console.log('TEST - BEFORE IMPORTS');

import React, { useState, useEffect, useRef } from 'react';
import { createRoot, Root } from 'react-dom/client';
import { createPortal } from 'react-dom';
import type { Namespace } from '@eesel/ui-core/src/types/namespace.types';
import { MessageTypes } from './messages';
import {
  UnifiedThreadExtractor,
  GmailExtractionOptions,
} from './extractors/thread-extractor';
import {
  openModalByDefault,
  setInputComponent,
  closeModal,
  getModalRoot,
  getReactRoot,
  setModalRoot,
  setReactRoot,
} from './modal-manager';
import { ALL_STYLES, injectStyles } from '../styles/css-loader';

console.log('[Eesel] Content script loaded');
console.log('[Eesel] React version:', React.version);
console.log('[Eesel] React available:', !!React);
console.log('[<PERSON>esel] createRoot available:', !!createRoot);

// Create a unified thread extractor instance
const unifiedExtractor = new UnifiedThreadExtractor();

// Removed currentButton variable - only using fixed chat bubble now
let isAutoDraftEnabled = false; // Flag to control auto-draft feature (disabled by default)
let autoDraftInProgress = false; // Flag to prevent multiple auto-draft calls
let autoDraftDebounceTimer: NodeJS.Timeout | null = null; // Timer for debouncing

// Load auto-draft setting from storage on initialization
chrome.storage.local.get(['eesel-auto-draft-enabled'], result => {
  if (result['eesel-auto-draft-enabled'] !== undefined) {
    isAutoDraftEnabled = result['eesel-auto-draft-enabled'];
    console.log(
      `[Eesel] Auto-draft setting loaded from storage: ${isAutoDraftEnabled}`
    );
  } else {
    // Set default value in storage if not exists
    chrome.storage.local.set({
      'eesel-auto-draft-enabled': isAutoDraftEnabled,
    });
    console.log(
      `[Eesel] Auto-draft setting initialized with default: ${isAutoDraftEnabled}`
    );
  }
});

// ==========================================
//           MODULAR CSS APPROACH
// ==========================================
// The CSS has been modularized into separate logical groups:
// - base: Container and layout styles
// - buttons: All button-related styles
// - chatBubble: Chat bubble and animations
// - forms: Input, textarea, and form elements
// - loading: Loading indicators and animations
// - messages: Chat message display styles
//
// You can import specific modules using injectStyles(['base', 'buttons'])
// or use ALL_STYLES for everything as we do here for compatibility.
// ==========================================

// Use modular CSS styles
const modalStyles = ALL_STYLES;

// Add type definition for the response
interface AIResponse {
  text?: string;
  error?: string;
}

// New function to show loading indicator in compose area
function showDraftLoadingIndicator() {
  const platform = unifiedExtractor.detectPlatform().platform;

  // Ensure loading styles are injected
  if (!document.querySelector('#eesel-loading-animation')) {
    injectStyles(['loading']);
    const style = document.createElement('style');
    style.id = 'eesel-loading-animation';
    style.textContent = `
      @keyframes eesel-dot-pulse {
        0%, 80%, 100% { opacity: 0.3; }
        40% { opacity: 1; }
      }
    `;
    document.head.appendChild(style);
  }

  if (platform === 'gmail') {
    const emailBody = document.querySelector(
      'div[role="textbox"][aria-label="Message Body"]'
    );
    if (emailBody) {
      // Create a loading indicator element using CSS classes
      const loadingDiv = document.createElement('div');
      loadingDiv.className = 'eesel-draft-loading';

      // Add animated dots
      loadingDiv.innerHTML = `
        <span>Creating draft</span>
        <span class="eesel-loading-dots">
          <span style="animation: eesel-dot-pulse 1.4s infinite ease-in-out both; animation-delay: -0.32s;">.</span>
          <span style="animation: eesel-dot-pulse 1.4s infinite ease-in-out both; animation-delay: -0.16s;">.</span>
          <span style="animation: eesel-dot-pulse 1.4s infinite ease-in-out both;">.</span>
        </span>
      `;

      // Clear existing content and add loading indicator
      emailBody.innerHTML = '';
      emailBody.appendChild(loadingDiv);
    }
  } else if (platform === 'intercom') {
    const intercomEditor = document.querySelector(
      '.ProseMirror.embercom-prosemirror-composer-editor'
    );
    if (intercomEditor) {
      // Create loading indicator for Intercom using CSS classes
      intercomEditor.innerHTML = `
        <div class="eesel-draft-loading">
          <span>Creating draft</span>
          <span class="eesel-loading-dots">
            <span style="animation: eesel-dot-pulse 1.4s infinite ease-in-out both; animation-delay: -0.32s;">.</span>
            <span style="animation: eesel-dot-pulse 1.4s infinite ease-in-out both; animation-delay: -0.16s;">.</span>
            <span style="animation: eesel-dot-pulse 1.4s infinite ease-in-out both;">.</span>
          </span>
        </div>
      `;
    }
  }
}

// New function to automatically generate and inject draft response
async function generateAutoDraft(
  selectedNamespace?: string,
  options?: GmailExtractionOptions
) {
  // Get the currently selected namespace from localStorage or use fallback
  const currentNamespaceId = selectedNamespace ||
    localStorage.getItem('namespace') ||
    'b826df9f-96d0-4d17-8bf3-b8115fe0d453'; // Fallback only if no selection exists

  console.log('[Eesel] Auto-generating draft response...');
  console.log('[Eesel] Using namespace for auto-draft:', currentNamespaceId);

  try {
    // Show loading indicator immediately
    showDraftLoadingIndicator();

    // Extract email context with options using the unified extractor directly
    const extractOptions = {
      ignoreLatestMessage: false, // Keep all messages for auto-draft
      includeHeaders: true, // Include sender info for better context
      ...options, // Allow override from parameters
    };

    console.log('[Eesel] Using extraction options:', extractOptions);
    // Use the unified extractor directly for better performance and consistency
    const emailContext =
      await unifiedExtractor.extractThreadContent(extractOptions);

    // Create a default prompt for generating a reply
    const autoDraftPrompt =
      'Please generate a professional reply to this email/message.';
    const messageWithContext = `[USER CONTEXT]\n${autoDraftPrompt}\n\n${emailContext}`;

    // Send message to AI service
    const response = await new Promise<AIResponse>((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error('Request timed out'));
      }, 60000);

      try {
        chrome.runtime.sendMessage(
          {
            type: MessageTypes.REQUEST_CONTEXT,
            message: messageWithContext,
            namespaceId: currentNamespaceId,
          },
          response => {
            clearTimeout(timeoutId);
            if (chrome.runtime.lastError) {
              console.error('Error sending message:', chrome.runtime.lastError);
              reject(chrome.runtime.lastError);
            } else if (!response) {
              reject(new Error('No response received'));
            } else {
              resolve(response as AIResponse);
            }
          }
        );
      } catch (error) {
        clearTimeout(timeoutId);
        reject(error);
      }
    });

    if (response?.text) {
      console.log('[Eesel] Auto-draft generated:', response.text);

      // Replace loading indicator with actual content
      replaceEmailContent(response.text || '');
      console.log('[Eesel] Auto-draft injected successfully');

      // Note: Auto-draft doesn't interact with the modal's draft history
      // since it operates independently of the user input modal
    } else {
      console.log('[Eesel] No response text received for auto-draft');
      // Clear loading indicator if no response
      replaceEmailContent('');
    }
  } catch (error) {
    console.error('[Eesel] Error generating auto-draft:', error);
    // Clear loading indicator on error
    replaceEmailContent('');
  }
}

// Function to detect reply button clicks
function setupReplyButtonObserver() {
  console.log('[Eesel] Setting up reply button observer...');

  const platform = unifiedExtractor.detectPlatform().platform;
  console.log(`[Eesel] Platform detected: ${platform}`);

  if (platform === 'gmail') {
    // Gmail reply button selectors - expanded list with actual DOM classes
    const replySelectors = [
      '[data-tooltip="Reply"]',
      '[aria-label="Reply"]',
      '[title="Reply"]',
      '.T-I.J-J5-Ji.T-I-Js-IF.aaq.T-I-ax7.L3', // Gmail reply button classes
      '.aaq', // Common Gmail reply button class
      '[role="button"][aria-label*="Reply"]',
      '[role="button"][title*="Reply"]',
      'div[data-tooltip*="Reply"]',
      'span[data-tooltip*="Reply"]',
      '.ar9.T-I-J3.J-J5-Ji', // Alternative Gmail reply button classes
      '.T-I.T-I-Js-Gs.aaq.T-I-ax7.L3', // Another variant
      // New selectors based on actual DOM structure
      '.ams.bkH', // Reply button class
      '.ams.bkI', // Reply All button class
      '.ams.bkG', // Forward button class
      'span.ams.bkH[role="link"]', // Reply span with role="link"
      'span.ams.bkI[role="link"]', // Reply All span with role="link"
      'span[role="link"][class*="ams"][class*="bk"]', // Generic pattern for reply buttons
      '.ams[role="link"]', // Any ams class with role="link"
      'span[role="link"][tabindex="0"].ams', // More specific pattern
    ];

    console.log('[Eesel] Gmail reply selectors:', replySelectors);

    // Add a more aggressive click listener that logs all clicks for debugging
    document.addEventListener('click', event => {
      const target = event.target as HTMLElement;

      // Debug: Log info about clicked elements that might be reply buttons
      const elementInfo = {
        tagName: target.tagName,
        className: target.className,
        textContent: target.textContent?.trim().slice(0, 50),
        ariaLabel: target.getAttribute('aria-label'),
        dataTooltip: target.getAttribute('data-tooltip'),
        title: target.title,
        role: target.getAttribute('role'),
      };

      // Check if this might be a reply-related element
      const isReplyRelated =
        elementInfo.textContent?.toLowerCase().includes('reply') ||
        elementInfo.ariaLabel?.toLowerCase().includes('reply') ||
        elementInfo.dataTooltip?.toLowerCase().includes('reply') ||
        elementInfo.title?.toLowerCase().includes('reply') ||
        elementInfo.className?.includes('aaq');

      if (isReplyRelated) {
        console.log('[Eesel] Reply-related element clicked:', elementInfo);
      }

      if (!isAutoDraftEnabled) {
        if (isReplyRelated) {
          console.log('[Eesel] Auto-draft is disabled, skipping');
        }
        return;
      }

      // Check if clicked element or its parents match reply button selectors
      let foundMatch = false;
      for (const selector of replySelectors) {
        if (target.matches && target.matches(selector)) {
          console.log(
            `[Eesel] Gmail reply button clicked (direct match: ${selector}), generating auto-draft...`
          );
          setTimeout(() => generateAutoDraft(), 1000);
          foundMatch = true;
          break;
        }

        // Also check parent elements up to 3 levels
        let currentElement = target.parentElement;
        let level = 0;
        while (currentElement && level < 3) {
          if (currentElement.matches && currentElement.matches(selector)) {
            console.log(
              `[Eesel] Gmail reply button clicked (parent match level ${level + 1}: ${selector}), generating auto-draft...`
            );
            setTimeout(() => generateAutoDraft(), 1000);
            foundMatch = true;
            break;
          }
          currentElement = currentElement.parentElement;
          level++;
        }

        if (foundMatch) break;
      }

      // Additional check: if element contains reply text and looks like a button
      if (!foundMatch && isReplyRelated) {
        const isButtonLike =
          target.tagName === 'BUTTON' ||
          target.getAttribute('role') === 'button' ||
          target.style.cursor === 'pointer' ||
          target.className.includes('T-I'); // Gmail button class pattern

        if (isButtonLike) {
          console.log(
            '[Eesel] Potential Gmail reply button detected via heuristics, generating auto-draft...'
          );
          setTimeout(() => generateAutoDraft(), 1000);
        }
      }
    });
  } else if (platform === 'intercom') {
    console.log('[Eesel] Setting up Intercom observer...');

    // Track the current conversation ID to detect conversation changes
    let currentConversationId: string | null = null;
    let lastAutoDraftTime = 0;
    const MIN_AUTO_DRAFT_INTERVAL = 3000; // Minimum 3 seconds between auto-drafts

    // Function to extract conversation ID from URL or DOM
    const getCurrentConversationId = (): string | null => {
      // Try to get from URL first
      const urlMatch = window.location.href.match(/conversation\/(\d+)/);
      if (urlMatch) {
        return urlMatch[1];
      }

      // Fallback: try to get from DOM
      const conversationElement = document.querySelector(
        '[data-conversation-stream]'
      );
      if (conversationElement) {
        return (
          conversationElement.getAttribute('data-conversation-id') ||
          conversationElement.id ||
          window.location.pathname
        );
      }

      return window.location.pathname;
    };

    // Function to check if we should trigger auto-draft
    const shouldTriggerAutoDraft = () => {
      const now = Date.now();
      const timeSinceLastDraft = now - lastAutoDraftTime;

      // Check if enough time has passed and we're not already in progress
      return (
        timeSinceLastDraft >= MIN_AUTO_DRAFT_INTERVAL && !autoDraftInProgress
      );
    };

    // Function to trigger auto-draft with proper tracking
    const triggerAutoDraftForIntercom = () => {
      if (!shouldTriggerAutoDraft()) {
        console.log(
          '[Eesel] Auto-draft skipped - too soon or already in progress'
        );
        return;
      }

      console.log(
        '[Eesel] Triggering auto-draft for new Intercom conversation...'
      );
      lastAutoDraftTime = Date.now();
      autoDraftInProgress = true;

      // Clear any existing timer
      if (autoDraftDebounceTimer) {
        clearTimeout(autoDraftDebounceTimer);
        autoDraftDebounceTimer = null;
      }

      // Small delay to ensure DOM is ready
      setTimeout(() => {
        generateAutoDraft().finally(() => {
          // Reset flag after completion
          setTimeout(() => {
            autoDraftInProgress = false;
          }, 1000); // Reduced cooldown to 1 second
        });
      }, 800);
    };

    // Function to check for conversation changes
    const checkForConversationChange = () => {
      if (!isAutoDraftEnabled) return;

      const newConversationId = getCurrentConversationId();

      if (newConversationId && newConversationId !== currentConversationId) {
        console.log(
          `[Eesel] Conversation changed from ${currentConversationId} to ${newConversationId}`
        );
        currentConversationId = newConversationId;

        // Check if composer is available
        const composer = document.querySelector('.inbox2__composer__container');
        const editor = document.querySelector(
          '.ProseMirror.embercom-prosemirror-composer-editor'
        );

        if (composer && editor) {
          console.log('[Eesel] Composer is ready, triggering auto-draft');
          triggerAutoDraftForIntercom();
        } else {
          console.log('[Eesel] Composer not ready, will wait for it to appear');
        }
      }
    };

    // Initialize current conversation ID
    currentConversationId = getCurrentConversationId();
    console.log(`[Eesel] Initial conversation ID: ${currentConversationId}`);

    // Method 1: Watch for URL changes (most reliable for Intercom navigation)
    let lastUrl = window.location.href;
    const urlChangeInterval = setInterval(() => {
      if (window.location.href !== lastUrl) {
        console.log('[Eesel] URL changed, checking for conversation change');
        lastUrl = window.location.href;
        // Small delay to let the page update
        setTimeout(checkForConversationChange, 500);
      }
    }, 1000);

    // Method 2: DOM mutation observer (backup method)
    const observer = new MutationObserver(mutations => {
      if (!isAutoDraftEnabled) return;

      let shouldCheck = false;

      mutations.forEach(mutation => {
        // Check for new composer containers
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element;

            if (
              element.matches &&
              element.matches('.inbox2__composer__container')
            ) {
              console.log('[Eesel] New composer container detected');
              shouldCheck = true;
            } else if (
              element.querySelector &&
              element.querySelector('.inbox2__composer__container')
            ) {
              console.log('[Eesel] Composer container found in new content');
              shouldCheck = true;
            }

            // Also check for conversation stream changes
            if (
              element.matches &&
              element.matches('[data-conversation-stream]')
            ) {
              console.log('[Eesel] Conversation stream changed');
              shouldCheck = true;
            } else if (
              element.querySelector &&
              element.querySelector('[data-conversation-stream]')
            ) {
              console.log('[Eesel] New conversation stream detected');
              shouldCheck = true;
            }
          }
        });

        // Check for attribute changes that might indicate conversation changes
        if (mutation.type === 'attributes' && mutation.target) {
          const target = mutation.target as Element;
          if (target.matches && target.matches('[data-conversation-stream]')) {
            console.log('[Eesel] Conversation stream attributes changed');
            shouldCheck = true;
          }
        }
      });

      if (shouldCheck) {
        // Small delay to ensure DOM is updated
        setTimeout(checkForConversationChange, 300);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['data-conversation-id', 'data-conversation-stream'],
    });

    // Method 3: Listen for popstate events (browser navigation)
    window.addEventListener('popstate', () => {
      console.log('[Eesel] Browser navigation detected');
      setTimeout(checkForConversationChange, 500);
    });

    // Method 4: Initial check in case we're already on a conversation page
    setTimeout(() => {
      const composer = document.querySelector('.inbox2__composer__container');
      const editor = document.querySelector(
        '.ProseMirror.embercom-prosemirror-composer-editor'
      );

      if (composer && editor && currentConversationId) {
        console.log(
          '[Eesel] Initial conversation detected, triggering auto-draft'
        );
        triggerAutoDraftForIntercom();
      }
    }, 1500);

    console.log('[Eesel] Intercom conversation change detection active');

    // Cleanup function (store reference for potential cleanup)
    (window as any).eeselIntercomCleanup = () => {
      clearInterval(urlChangeInterval);
      observer.disconnect();
    };
  } else {
    console.log('[Eesel] Unknown platform, no reply detection setup');
  }

  console.log('[Eesel] Reply button observer setup complete');
}

// Removed createFloatingButton function - only using fixed chat bubble now

// Dynamic composer detection functions
function findComposerElements(): Element[] {
  const platform = unifiedExtractor.detectPlatform().platform;
  const composers: Element[] = [];

  if (platform === 'gmail') {
    // Gmail composer detection - look for compose windows and text areas
    const gmailSelectors = [
      '.M9', // Main compose window
      '.nH.if', // Alternative compose window
      '[role="dialog"]', // Generic dialog role
      '.dw', // Another potential compose container
    ];

    gmailSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        // Make sure this compose window has a text input/editor
        const hasTextArea =
          element.querySelector('[aria-label="Message Body"]') ||
          element.querySelector('.Am.Al.editable') ||
          element.querySelector('div[role="textbox"]');
        if (hasTextArea && !composers.includes(element)) {
          composers.push(element);
        }
      });
    });
  } else if (platform === 'intercom') {
    // Intercom composer detection
    const intercomSelectors = [
      '.inbox2__composer__container',
      '.ProseMirror.embercom-prosemirror-composer-editor',
    ];

    intercomSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        // For Intercom, look for the parent container that would be suitable for positioning
        const composerContainer =
          element.closest('.inbox2__composer__container') || element;
        if (composerContainer && !composers.includes(composerContainer)) {
          composers.push(composerContainer);
        }
      });
    });
  }

  console.log(
    `[Eesel] Found ${composers.length} composer elements:`,
    composers
  );
  return composers;
}

function ensureComposerHasRelativePositioning(composer: Element) {
  const htmlElement = composer as HTMLElement;
  const computedStyle = window.getComputedStyle(htmlElement);

  if (computedStyle.position === 'static') {
    htmlElement.classList.add('eesel-composer-container');
    console.log('[Eesel] Added relative positioning to composer');
  }
}

function createChatBubbleForComposer(
  composer: Element
): HTMLButtonElement | null {
  // Check if this composer already has a bubble
  if (composer.querySelector('.eesel-chat-bubble')) {
    console.log('[Eesel] Composer already has a chat bubble');
    return null;
  }

  // Detect platform for positioning logic
  const platform = unifiedExtractor.detectPlatform().platform;

  // Ensure the composer has relative positioning
  ensureComposerHasRelativePositioning(composer);

  const bubble = document.createElement('button');
  bubble.className = 'eesel-chat-bubble';

  // Add Gmail-specific class for different positioning
  if (platform === 'gmail') {
    bubble.classList.add('eesel-chat-bubble-gmail');
  }

  bubble.innerHTML = `
    <div class="eesel-chat-bubble-left">
      <svg width="16" height="21" viewBox="0 0 16 21" fill="none">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M10.0377 0.590338C10.1642 0.667951 10.2635 0.790027 10.3197 0.937092C10.376 1.08416 10.3859 1.24774 10.3479 1.40174L8.62642 8.35674H14.9074C15.0338 8.35675 15.1573 8.39743 15.2629 8.47378C15.3685 8.55014 15.4516 8.65883 15.5019 8.78651C15.5522 8.91419 15.5676 9.05529 15.5461 9.19246C15.5246 9.32963 15.4673 9.45689 15.381 9.55861L6.30691 20.2725C6.20807 20.3895 6.0765 20.4665 5.93325 20.4913C5.78999 20.5162 5.64332 20.4873 5.51669 20.4094C5.39007 20.3315 5.2908 20.2091 5.23477 20.0617C5.17875 19.9143 5.16919 19.7505 5.20764 19.5964L6.92913 12.6423H0.648111C0.521798 12.6423 0.39824 12.6016 0.292625 12.5253C0.187011 12.4489 0.103942 12.3402 0.0536318 12.2125C0.00332127 12.0849 -0.012039 11.9438 0.00943935 11.8066C0.0309177 11.6694 0.0882985 11.5422 0.174528 11.4404L9.24865 0.726524C9.34748 0.61 9.47887 0.533285 9.62186 0.508604C9.76486 0.483923 9.91123 0.512696 10.0377 0.590338Z" fill="white"/>
      </svg>
    </div>
    <div class="eesel-chat-bubble-divider"></div>
    <div class="eesel-chat-bubble-right">
      <svg width="14" height="15" viewBox="0 0 21 21" fill="none">
        <g clip-path="url(#clip0_16054_25683)">
          <rect x="0.555664" y="0.5" width="20" height="20" rx="10" fill="white"/>
          <path d="M16.1981 10.1618V9.28333C16.1981 6.0916 14.3174 4.04187 11.2254 4.04187C7.87839 4.04187 4.72266 6.23801 4.72266 10.3668C4.72266 14.232 7.43213 16.7502 11.3848 16.7502C13.8074 16.7502 15.4649 15.8717 16.3893 14.9347L16.2335 13.7461C15.341 14.3025 13.6161 15.0811 12.3411 15.0811C10.046 15.0811 8.1334 13.6463 7.71901 10.8353L16.1981 10.1618ZM14.1674 9.04148L7.62339 9.51758C7.62339 7.0579 8.8028 5.52994 10.7472 5.33027C13.282 5.06999 14.1674 6.87462 14.1674 8.86578V9.04148Z" fill="black"/>
        </g>
        <defs>
          <clipPath id="clip0_16054_25683">
            <rect width="20" height="20" fill="white" transform="translate(0.555664 0.5)"/>
          </clipPath>
        </defs>
      </svg>
    </div>
  `;

  // Add click handler
  bubble.addEventListener('click', e => {
    e.preventDefault();
    e.stopPropagation();

    // Check which section was clicked
    const target = e.target as HTMLElement;
    const isLeftSectionClick = target.closest('.eesel-chat-bubble-left') !== null;
    const isRightSectionClick = target.closest('.eesel-chat-bubble-right') !== null;
    
    if (isLeftSectionClick) {
      // Left section: Trigger auto-draft
      console.log('[Eesel] Left section clicked - triggering auto-draft');
      
      // Temporarily enable auto-draft for this action
      const wasAutoDraftEnabled = isAutoDraftEnabled;
      isAutoDraftEnabled = true;
      
      // Generate auto-draft with default settings
      generateAutoDraft()
        .then(() => {
          console.log('[Eesel] Auto-draft completed successfully');
        })
        .catch((error) => {
          console.error('[Eesel] Auto-draft failed:', error);
        })
        .finally(() => {
          // Restore original auto-draft setting
          isAutoDraftEnabled = wasAutoDraftEnabled;
        });
      
      return;
    }
    
    if (isRightSectionClick) {
      // Right section: Toggle modal
      console.log('[Eesel] Right section clicked - toggling modal');
      
      // Toggle modal: if it's open, close it; if it's closed, open it
      if (getModalRoot()) {
        console.log('[Eesel] Modal is open, closing it');
        closeModal();
        // Show chat bubble again when modal is closed
        toggleChatBubble(true);
      } else {
        console.log('[Eesel] Modal is closed, opening it above chat bubble');
        openModalWithBubbleHandling(bubble);
      }
      
      return;
    }
    
    // Fallback: if neither section was detected, default to right section behavior
    console.log('[Eesel] No specific section detected, defaulting to modal toggle');
    if (getModalRoot()) {
      closeModal();
      toggleChatBubble(true);
    } else {
      openModalWithBubbleHandling(bubble);
    }
  });

  // Gmail-specific positioning: find discard draft button and position bubble above it
  if (platform === 'gmail') {
    // Try to find the discard draft button or the action bar within the composer
    let discardButton = composer.querySelector(
      '[data-tooltip*="Discard draft"], [aria-label*="Discard draft"]'
    );

    // If not found by tooltip/aria-label, try by class structure
    if (!discardButton) {
      discardButton = composer.querySelector('.oh.J-Z-I.J-J5-Ji.T-I-ax7.T-I');
    }

    // Also look for the action bar container which usually contains discard button
    const actionBar = composer.querySelector('.btC, .gU, .aoY');

    // Try to get more precise positioning if we found the actual discard button
    if (discardButton) {
      console.log(
        '[Eesel] Found discard draft button, positioning bubble precisely above it'
      );
      const discardRect = (
        discardButton as HTMLElement
      ).getBoundingClientRect();
      const composerRect = (composer as HTMLElement).getBoundingClientRect();

      // Calculate position relative to composer
      const relativeBottom = composerRect.bottom - discardRect.top + 10; // 10px above the button

      bubble.style.position = 'absolute';
      bubble.style.bottom = `${Math.max(relativeBottom, 80)}px`; // At least 80px from bottom
      bubble.style.right = '12px';
      bubble.style.top = 'auto';
      bubble.style.left = 'auto';
      bubble.style.zIndex = '999999';
    } else if (actionBar) {
      console.log('[Eesel] Found action bar, positioning bubble above it');
      bubble.style.position = 'absolute';
      bubble.style.bottom = '80px'; // Higher positioning above action bar
      bubble.style.right = '12px';
      bubble.style.top = 'auto';
      bubble.style.left = 'auto';
      bubble.style.zIndex = '999999';
    } else {
      console.log(
        '[Eesel] Gmail action elements not found, using higher bottom-right positioning'
      );
      // Fallback with higher positioning
      bubble.style.position = 'absolute';
      bubble.style.bottom = '70px'; // Higher than before
      bubble.style.right = '12px';
      bubble.style.top = 'auto';
      bubble.style.left = 'auto';
      bubble.style.zIndex = '999999';
    }

    console.log(
      '[Eesel] Gmail bubble positioned just above discard draft area'
    );
  }

  // Add to composer
  composer.appendChild(bubble);
  console.log('[Eesel] Chat bubble added to composer:', composer);

  return bubble;
}

function createChatBubbles() {
  console.log('[Eesel] Creating chat bubbles for all composers');

  const composers = findComposerElements();
  let bubblesCreated = 0;

  composers.forEach(composer => {
    const bubble = createChatBubbleForComposer(composer);
    if (bubble) {
      bubblesCreated++;
    }
  });

  console.log(`[Eesel] Created ${bubblesCreated} new chat bubbles`);
  return bubblesCreated > 0;
}

// Legacy function for backwards compatibility - now uses dynamic detection
function isOnComposePage(): boolean {
  return findComposerElements().length > 0;
}

function createChatBubble() {
  console.log(
    '[Eesel] Creating chat bubble (legacy function - redirecting to createChatBubbles)'
  );
  return createChatBubbles();
}

// Function to show/hide chat bubbles based on modal state
function toggleChatBubble(show: boolean) {
  const bubbles = document.querySelectorAll('.eesel-chat-bubble');
  bubbles.forEach(bubble => {
    if (show) {
      bubble.classList.remove('hidden');
    } else {
      bubble.classList.add('hidden');
    }
  });
}

// Enhanced function to open modal and handle chat bubble
function openModalWithBubbleHandling(bubbleElement?: Element) {
  // Hide chat bubble when opening modal
  toggleChatBubble(false);

  // Open the modal, positioning it relative to the bubble if provided
  openModalByDefault(bubbleElement);
}

function findGmailComposeWindows() {
  const composeSelectors = [
    '.M9', // Main compose window class
    '.nH.if', // Alternative compose window selector
    '[role="dialog"]', // Generic dialog role
    '.dw', // Another potential compose container
  ];

  let composeWindows = [];

  for (const selector of composeSelectors) {
    const elements = Array.from(document.querySelectorAll(selector));
    for (const element of elements) {
      // Check if this element contains a Gmail compose form
      if (
        element.querySelector('[name="to"]') ||
        element.querySelector('.Am.Al.editable')
      ) {
        composeWindows.push(element);
      }
    }
  }

  return composeWindows;
}

function findIntercomComposeWindows() {
  // Look for Intercom compose containers
  return Array.from(document.querySelectorAll('.inbox2__composer__container'));
}

function getComposeContainers() {
  const platform = unifiedExtractor.detectPlatform().platform;

  switch (platform) {
    case 'gmail':
      return findGmailComposeWindows();
    case 'intercom':
      return findIntercomComposeWindows();
    default:
      return [];
  }
}

// Removed addButtonToContainer function - only using fixed chat bubble now

function formatHtmlForIntercom(htmlString: string): string {
  console.log('🎨 Formatting HTML for Intercom...');
  console.log('Original HTML:', htmlString);

  let cleaned = htmlString;

  // Remove markdown artifacts that might remain
  cleaned = cleaned.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
  cleaned = cleaned.replace(/\*(.*?)\*/g, '<em>$1</em>');
  cleaned = cleaned.replace(/```([\s\S]*?)```/g, '<code>$1</code>');
  cleaned = cleaned.replace(/`(.*?)`/g, '<code>$1</code>');

  // Clean up excessive whitespace and normalize spaces
  cleaned = cleaned
    .split('\n')
    .map(line => line.trim())
    .filter(line => line.length > 0)
    .join('\n');

  // Normalize multiple spaces to single spaces
  cleaned = cleaned.replace(/\s{2,}/g, ' ');

  // Remove empty paragraphs
  cleaned = cleaned.replace(/<p>\s*<\/p>/g, '');

  // Remove <br> tags that are between paragraphs (but preserve <br> within paragraphs)
  cleaned = cleaned.replace(/<\/p>\s*<br\s*\/?>\s*<p/gi, '</p><p');

  // For Intercom: Add a single space paragraph between consecutive paragraphs
  // Using an empty paragraph with &nbsp; for consistent spacing
  cleaned = cleaned.replace(/<\/p>\s*<p/g, '</p><p>&nbsp;</p><p');

  console.log('Formatted HTML:', cleaned);
  console.log('🎨 HTML formatting for Intercom completed');

  return cleaned;
}

function replaceEmailContent(content: string, skipFocus = false) {
  // Clean out markdown code block markers if present
  content = content.replace(/^```html\n/, '').replace(/\n```$/, '');

  // Check if content contains HTML tags
  const hasHtml = /<[a-z][\s\S]*>/i.test(content);

  const platform = unifiedExtractor.detectPlatform().platform;

  if (platform === 'gmail') {
    // Find the email body
    const emailBody = document.querySelector(
      'div[role="textbox"][aria-label="Message Body"]'
    );
    if (emailBody) {
      // Instead of trying to extract quoted text, we'll preserve the existing structure
      // and only replace the user's draft content (content before any quoted sections)

      // Look for Gmail quote divs to identify where quoted content starts
      const gmailQuote = emailBody.querySelector('.gmail_quote');

      if (gmailQuote) {
        // If there's a quote section, insert our content before it
        // First, remove any existing draft content before the quote
        const walker = document.createTreeWalker(
          emailBody,
          NodeFilter.SHOW_ALL,
          {
            acceptNode: function (node) {
              // Stop when we reach the gmail_quote div
              if (node === gmailQuote) {
                return NodeFilter.FILTER_REJECT;
              }
              // Accept all nodes before the quote
              return NodeFilter.FILTER_ACCEPT;
            },
          }
        );

        // Collect all nodes before the quote
        const nodesToRemove: Node[] = [];
        let node = walker.nextNode();
        while (node) {
          if (node !== emailBody) {
            // Don't remove the container itself
            nodesToRemove.push(node);
          }
          node = walker.nextNode();
        }

        // Remove existing draft content
        nodesToRemove.forEach(node => {
          if (node.parentNode) {
            node.parentNode.removeChild(node);
          }
        });

        // Create new content element
        const newContentDiv = document.createElement('div');
        if (hasHtml) {
          // Check if content already has proper HTML list tags
          const hasHtmlLists = /<(ol|ul)[\s\S]*?<\/\1>/i.test(content);

          if (hasHtmlLists) {
            // Content already has proper HTML lists, use as-is
            newContentDiv.innerHTML = content;
          } else {
            // Convert any remaining markdown to HTML for Gmail
            let htmlContent = content
              // Convert markdown links to HTML
              .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')
              // Convert markdown bold to HTML
              .replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')
              // Convert markdown italic to HTML
              .replace(/\*([^*]+)\*/g, '<em>$1</em>')
              // Convert markdown lists to HTML
              .replace(/^- (.+)$/gm, '<li>$1</li>')
              // Wrap consecutive list items in <ul> tags
              .replace(/(<li>[\s\S]*?<\/li>)/g, '<ul>$1</ul>')
              // Fix multiple ul tags
              .replace(/<\/ul>\s*<ul>/g, '');

            newContentDiv.innerHTML = htmlContent;
          }
        } else {
          // Convert plain text to HTML with proper line breaks
          const htmlContent = content
            .replace(/\n\n/g, '<div><br></div>')
            .replace(/\n/g, '<br>');
          newContentDiv.innerHTML = htmlContent;
        }

        // Insert the new content before the quote
        emailBody.insertBefore(newContentDiv, gmailQuote);

        // Add some spacing between new content and quote
        const spacingDiv = document.createElement('div');
        spacingDiv.innerHTML = '<br>';
        emailBody.insertBefore(spacingDiv, gmailQuote);
      } else {
        // No quoted content found, replace all content
        if (hasHtml) {
          // Check if content already has proper HTML list tags
          const hasHtmlLists = /<(ol|ul)[\s\S]*?<\/\1>/i.test(content);

          if (hasHtmlLists) {
            // Content already has proper HTML lists, use as-is
            emailBody.innerHTML = content;
          } else {
            // Convert any remaining markdown to HTML for Gmail
            let htmlContent = content
              // Convert markdown links to HTML
              .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')
              // Convert markdown bold to HTML
              .replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')
              // Convert markdown italic to HTML
              .replace(/\*([^*]+)\*/g, '<em>$1</em>')
              // Convert markdown lists to HTML
              .replace(/^- (.+)$/gm, '<li>$1</li>')
              // Wrap consecutive list items in <ul> tags
              .replace(/(<li>[\s\S]*?<\/li>)/g, '<ul>$1</ul>')
              // Fix multiple ul tags
              .replace(/<\/ul>\s*<ul>/g, '');

            emailBody.innerHTML = htmlContent;
          }
        } else {
          // Convert plain text to HTML with proper line breaks
          const htmlContent = content
            .replace(/\n\n/g, '<div><br></div>')
            .replace(/\n/g, '<br>');
          emailBody.innerHTML = htmlContent;
        }
      }

      // Trigger necessary events for Gmail to recognize the changes
      emailBody.dispatchEvent(new Event('input', { bubbles: true }));
      emailBody.dispatchEvent(new Event('change', { bubbles: true }));
      if (!skipFocus) {
        (emailBody as HTMLElement).focus();
      }
    }
  } else if (platform === 'intercom') {
    // Find the Intercom ProseMirror editor
    const intercomEditor = document.querySelector(
      '.ProseMirror.embercom-prosemirror-composer-editor'
    );
    if (intercomEditor) {
      console.log('Original content:', content);

      // For Intercom, keep HTML formatting and just clean it up
      const cleanedHtml = formatHtmlForIntercom(content);

      // Insert cleaned HTML content into Intercom editor
      intercomEditor.innerHTML = cleanedHtml;
      console.log('Inserted cleaned HTML content for Intercom');

      // Trigger necessary events for Intercom to recognize the changes
      intercomEditor.dispatchEvent(new Event('input', { bubbles: true }));
      intercomEditor.dispatchEvent(new Event('change', { bubbles: true }));

      // Focus and place cursor at the end
      if (!skipFocus) {
        (intercomEditor as HTMLElement).focus();

        // Place cursor at the end
        const range = document.createRange();
        const sel = window.getSelection();
        range.selectNodeContents(intercomEditor);
        range.collapse(false);
        sel?.removeAllRanges();
        sel?.addRange(range);
      }
    }
  }
}

// Helper functions (you'll need to implement these)
const getSelectedText = (): string | null => {
  const selection = window.getSelection();
  const selectedText = selection?.toString().trim();

  if (selectedText) {
    console.log('📋 Found selected text:', selectedText);
    return selectedText;
  }

  console.log('📋 No text selected');
  return null;
};

const getDocumentTitle = (): string | null => {
  const title = document.title?.trim();

  if (title) {
    console.log('📄 Found document title:', title);
    return title;
  }

  console.log('📄 No document title found');
  return null;
};

const getDraftContent = (): string | null => {
  const platform = unifiedExtractor.detectPlatform().platform;

  if (platform === 'gmail') {
    // Look for the Gmail compose editor
    const draftEditor =
      document.querySelector(
        '.Am.aiL.aO9.Al.editable.LW-avf.tS-tW.tS-tY[role="textbox"]'
      ) ||
      document.querySelector(
        '[aria-label="Message Body"][contenteditable="true"]'
      ) ||
      document.querySelector('.editable[contenteditable="true"]');

    if (draftEditor) {
      const draftText =
        (draftEditor as HTMLElement).innerText ||
        (draftEditor as HTMLElement).textContent ||
        '';

      if (draftText.trim()) {
        console.log('📝 Found Gmail draft content:', draftText.trim());
        return draftText.trim();
      }
    }
  } else if (platform === 'intercom') {
    // Look for the Intercom ProseMirror editor
    const intercomEditor = document.querySelector(
      '.ProseMirror.embercom-prosemirror-composer-editor'
    );
    if (intercomEditor) {
      // Get the HTML content
      const draftHtml = intercomEditor.innerHTML;

      if (draftHtml.trim()) {
        console.log('📝 Found Intercom draft content:', draftHtml);

        // Convert HTML to markdown-like format
        let markdownContent = draftHtml
          // Convert <br> tags to newlines
          .replace(/<br\s*\/?>/gi, '\n')
          // Convert <p> tags to newlines
          .replace(/<\/p>\s*<p>/gi, '\n\n')
          // Convert <strong> tags to markdown bold
          .replace(/<strong>(.*?)<\/strong>/gi, '**$1**')
          // Convert <em> and <i> tags to markdown italic
          .replace(/<(?:em|i)>(.*?)<\/(?:em|i)>/gi, '*$1*')
          // Convert <a> tags to markdown links
          .replace(
            /<a\s+(?:[^>]*?)href="([^"]*)"(?:[^>]*?)>(.*?)<\/a>/gi,
            '[$2]($1)'
          )
          // Remove any remaining HTML tags
          .replace(/<[^>]+>/g, '')
          // Clean up multiple newlines
          .replace(/\n{3,}/g, '\n\n')
          // Trim whitespace
          .trim();

        console.log(
          '📝 Converted Intercom draft to markdown:',
          markdownContent
        );
        return markdownContent;
      }
    }
  }

  console.log('📝 No draft content found');
  return null;
};

// New function to get draft content with HTML formatting preserved for undo functionality
const getDraftContentWithHTML = (): string | null => {
  const platform = unifiedExtractor.detectPlatform().platform;

  if (platform === 'gmail') {
    // Look for the Gmail compose editor
    const draftEditor =
      document.querySelector(
        'div[role="textbox"][aria-label="Message Body"]'
      ) ||
      document.querySelector(
        '.Am.aiL.aO9.Al.editable.LW-avf.tS-tW.tS-tY[role="textbox"]'
      ) ||
      document.querySelector(
        '[aria-label="Message Body"][contenteditable="true"]'
      ) ||
      document.querySelector('.editable[contenteditable="true"]');

    if (draftEditor) {
      // Get the HTML content to preserve formatting
      const draftHtml = (draftEditor as HTMLElement).innerHTML;

      if (draftHtml.trim()) {
        console.log('📝 Found Gmail draft HTML content for undo:', draftHtml);
        return draftHtml;
      }
    }
  } else if (platform === 'intercom') {
    // Look for the Intercom ProseMirror editor
    const intercomEditor = document.querySelector(
      '.ProseMirror.embercom-prosemirror-composer-editor'
    );
    if (intercomEditor) {
      // Get the HTML content directly
      const draftHtml = intercomEditor.innerHTML;

      if (draftHtml.trim()) {
        console.log(
          '📝 Found Intercom draft HTML content for undo:',
          draftHtml
        );
        return draftHtml;
      }
    }
  }

  console.log('📝 No draft HTML content found for undo');
  return null;
};

// Custom icons for the modal
const CloseIcon = () => (
  <svg viewBox="0 0 24 24" width="14" height="14" fill="none">
    <path 
      d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" 
      fill="currentColor"
    />
  </svg>
);

const DraftIcon = () => (
  <svg width="11" height="12" viewBox="0 0 11 12" fill="none">
    <path 
      d="M2.28316 6.10889L0.858887 1.35889C4.2659 2.37579 7.47873 3.9822 10.3589 6.10889C7.4789 8.23553 4.26625 9.84194 0.859408 10.8589L2.28316 6.10889ZM2.28316 6.10889H6.19455" 
      stroke="white" 
      strokeWidth="1.1" 
      strokeLinecap="round" 
      strokeLinejoin="round"
    />
  </svg>
);

const UndoIcon = () => (
  <svg width="15" height="15" viewBox="0 0 15 15" fill="none">
    <g clipPath="url(#clip0_16386_2982)">
      <g clipPath="url(#clip1_16386_2982)">
        <path 
          fillRule="evenodd" 
          clipRule="evenodd" 
          d="M7.47984 2.65884C4.60796 2.65884 2.27984 4.98696 2.27984 7.85883C2.27984 10.7307 4.60796 13.0588 7.47984 13.0588C10.3517 13.0588 12.6798 10.7307 12.6798 7.85883C12.6798 4.98696 10.3517 2.65884 7.47984 2.65884ZM1.17984 7.85883C1.17984 4.37944 4.00045 1.55884 7.47984 1.55884C10.9592 1.55884 13.7798 4.37944 13.7798 7.85883C13.7798 11.3382 10.9592 14.1588 7.47984 14.1588C4.00045 14.1588 1.17984 11.3382 1.17984 7.85883Z" 
          fill="#75777C"
        />
        <rect x="-4.52017" y="-4.14111" width="24" height="24" rx="12" fill="white"/>
        <mask id="mask0_16386_2982" style={{maskType:"luminance"}} maskUnits="userSpaceOnUse" x="1" y="1" width="13" height="14">
          <path d="M13.4997 1.29175H1.46001V14.4259H13.4997V1.29175Z" fill="white"/>
        </mask>
        <g mask="url(#mask0_16386_2982)">
          <path 
            d="M4.83798 4.57532L2.64896 6.76434L4.83798 8.95337" 
            stroke="#75777C" 
            strokeWidth="1.09451" 
            strokeLinecap="round" 
            strokeLinejoin="round"
          />
          <path 
            d="M2.64896 6.76453H8.66878C10.18 6.76453 11.4051 8.39796 11.4051 10.4129V11.1426" 
            stroke="#75777C" 
            strokeWidth="1.09451" 
            strokeLinecap="round"
          />
        </g>
      </g>
    </g>
    <defs>
      <clipPath id="clip0_16386_2982">
        <rect width="14" height="14" fill="white" transform="translate(0.479828 0.858887)"/>
      </clipPath>
      <clipPath id="clip1_16386_2982">
        <rect width="14" height="14" fill="white" transform="translate(0.479828 0.858887)"/>
      </clipPath>
    </defs>
  </svg>
);

const CheckIcon = () => (
  <svg width="9" height="9" viewBox="0 0 9 9" fill="none">
    <path 
      d="M5.47998 0.479492H3.47998C1.82313 0.479492 0.47998 1.82264 0.47998 3.47949V5.47949C0.47998 7.13635 1.82313 8.47949 3.47998 8.47949H5.47998C7.13683 8.47949 8.47998 7.13635 8.47998 5.47949V3.47949C8.47998 1.82264 7.13683 0.479492 5.47998 0.479492Z" 
      fill="#34A853"
    />
  </svg>
);

// Settings Icon Component
const SettingsIcon: React.FC = () => (
  <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_16386_4512)">
      <g clipPath="url(#clip1_16386_4512)">
        <mask id="mask0_16386_4512" style={{maskType: 'luminance'}} maskUnits="userSpaceOnUse" x="2" y="3" width="11" height="10">
          <path d="M12.2185 3.40088H2.71851V12.9009H12.2185V3.40088Z" fill="white"/>
        </mask>
        <g mask="url(#mask0_16386_4512)">
          <path d="M8.91094 12.9009C8.68325 12.9009 8.46342 12.8145 8.29069 12.6575L7.80391 12.1943C7.60763 12.0137 7.31713 12.0137 7.12085 12.1943L6.63408 12.6575C6.35928 12.9166 5.95887 12.9794 5.62127 12.8145L5.13449 12.579C4.80474 12.4141 4.60061 12.0686 4.63201 11.6918L4.67912 11.0166C4.69482 10.7418 4.50639 10.4984 4.2473 10.4591L3.59565 10.3571C3.22664 10.3021 2.93614 10.0273 2.84978 9.65831L2.73986 9.10088C2.66135 8.73972 2.80267 8.36286 3.10887 8.15088L3.6506 7.77402C3.87044 7.61699 3.9411 7.30294 3.79978 7.06741L3.46218 6.47856C3.27375 6.15666 3.30515 5.75625 3.53284 5.4579L3.86259 5.02608C4.09027 4.72774 4.48284 4.60212 4.84399 4.71989L5.5035 4.92402C5.76259 5.00253 6.02168 4.86906 6.12375 4.60997L6.35928 3.98972C6.4849 3.63641 6.82251 3.40088 7.19937 3.40088H7.73325C8.11011 3.40088 8.43986 3.62856 8.57333 3.98187L8.81672 4.60997C8.91094 4.86121 9.17788 4.99468 9.42912 4.91617L10.0651 4.71989C10.4184 4.60997 10.8109 4.73559 11.0386 5.02608L11.3762 5.4579C11.6039 5.7484 11.6353 6.14881 11.4547 6.47071L11.125 7.05955C10.9915 7.30294 11.0543 7.60914 11.282 7.76617L11.8316 8.15088C12.1378 8.36286 12.2791 8.73972 12.2006 9.10088L12.0828 9.63476C12.0043 10.0038 11.706 10.2786 11.3448 10.3335L10.6853 10.4356C10.4262 10.4748 10.2378 10.7261 10.2535 10.993L10.3006 11.6682C10.3241 12.0451 10.1279 12.3905 9.79813 12.5554L9.31135 12.791C9.18573 12.8538 9.05226 12.8852 8.91879 12.8852L8.91094 12.9009ZM4.5849 5.53641L4.21589 5.97608L4.5535 6.62774C4.91465 7.25584 4.73408 8.06451 4.14523 8.47278L3.6035 8.84964L3.70556 9.43848L3.73697 9.47774L4.38862 9.5798C5.08738 9.68972 5.59771 10.3335 5.55061 11.0558L5.5035 11.731L6.01383 12.0215L6.53986 11.5583C7.06589 11.0637 7.88242 11.0637 8.40846 11.5583L8.89523 12.0215L9.41341 11.786L9.38986 11.0558C9.34275 10.3414 9.84522 9.68972 10.544 9.5798L11.2035 9.47774L11.3527 8.9046L10.7874 8.46493C10.1985 8.04881 10.0179 7.26369 10.3791 6.62774L10.7089 6.03889L10.3713 5.54427L9.70391 5.7327C9.02085 5.94468 8.28284 5.58352 8.02375 4.90832L7.78036 4.28022L7.20722 4.25666L6.93242 4.90832C6.67333 5.58352 5.93532 5.95253 5.25226 5.7327L4.59275 5.52856L4.5849 5.53641Z" fill="currentColor"/>
          <path d="M7.46621 9.9094C6.99513 9.9094 6.55546 9.72882 6.21786 9.39122C5.5348 8.70816 5.5348 7.58543 6.21786 6.90237C6.88522 6.23502 8.03935 6.23502 8.7067 6.90237C9.38976 7.58543 9.38976 8.70816 8.7067 9.39122C8.37695 9.72097 7.92943 9.9094 7.45836 9.9094H7.46621ZM7.46621 7.24783C7.23067 7.24783 7.00298 7.34204 6.83026 7.50692C6.47695 7.86022 6.47695 8.42551 6.83026 8.77882C7.16786 9.11642 7.7567 9.11642 8.10216 8.77882C8.45546 8.42551 8.45546 7.86022 8.10216 7.50692C7.92943 7.33419 7.7096 7.24783 7.46621 7.24783Z" fill="currentColor"/>
        </g>
      </g>
    </g>
    <defs>
      <clipPath id="clip0_16386_4512">
        <rect width="14" height="14" fill="white" transform="translate(0.47998 0.858398)"/>
      </clipPath>
      <clipPath id="clip1_16386_4512">
        <rect width="14" height="14" fill="white" transform="translate(0.465332 1.15063)"/>
      </clipPath>
    </defs>
  </svg>
);

// Sidebar Icon Component
const SidebarIcon: React.FC = () => (
  <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_16386_4474)">
      <path d="M11.6094 3.72949H4.10938C3.7642 3.72949 3.48438 4.00931 3.48438 4.35449V10.6045C3.48438 10.9497 3.7642 11.2295 4.10938 11.2295H11.6094C11.9546 11.2295 12.2344 10.9497 12.2344 10.6045V4.35449C12.2344 4.00931 11.9546 3.72949 11.6094 3.72949Z" stroke="currentColor" strokeWidth="1.25"/>
      <path d="M9.10938 3.72949V11.2295" stroke="currentColor" strokeWidth="1.25"/>
    </g>
    <defs>
      <clipPath id="clip0_16386_4474">
        <rect width="10" height="11.25" fill="white" transform="translate(2.85938 1.85449)"/>
      </clipPath>
    </defs>
  </svg>
);

// Dropdown Arrow Icon Component
const DropdownArrowIcon: React.FC = () => (
  <svg width="10.89" height="10.89" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M2.68 3.85L5.52 6.69L8.36 3.85" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

// Custom BotPicker component with authentication
const CustomBotPicker: React.FC<{
  data: BotData[];
  currentNamespace: BotData | null;
  onNamespaceChange?: (namespace: BotData) => void;
  onLogin?: () => void;
  isAuthenticated: boolean;
  isLoading?: boolean;
}> = ({ data, currentNamespace, onNamespaceChange, onLogin, isAuthenticated, isLoading = false }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });
  const [isAnimating, setIsAnimating] = useState(false);
  const [shouldRender, setShouldRender] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;

      // Check if click is outside the button wrapper
      const isOutsideButton = dropdownRef.current && !dropdownRef.current.contains(target);

      // Check if click is outside the dropdown menu (which is portaled to document.body)
      const dropdownMenu = document.querySelector('.eesel-bot-picker-menu');
      const isOutsideDropdown = dropdownMenu && !dropdownMenu.contains(target);

      // Close dropdown only if click is outside both button and dropdown menu
      if (isOutsideButton && isOutsideDropdown) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Update dropdown position when opened
  useEffect(() => {
    if (isOpen && dropdownRef.current) {
      const buttonElement = dropdownRef.current.querySelector('.eesel-bot-picker-button');
      if (buttonElement) {
        // Use a small delay to ensure DOM is fully rendered and animations are complete
        const positionDropdown = () => {
          const buttonRect = buttonElement.getBoundingClientRect();

          // Get modal container to align dropdown with modal's right edge
          const modalContainer = dropdownRef.current?.closest('.eesel-modal-new');
          const modalRect = modalContainer?.getBoundingClientRect();

          // Get viewport dimensions for edge detection
          const viewportWidth = window.innerWidth;
          const viewportHeight = window.innerHeight;

          // Get dropdown menu element to measure its dimensions
          const dropdownMenu = dropdownRef.current?.querySelector('.eesel-bot-picker-menu');

          // Estimate dropdown dimensions (fallback values if menu not yet rendered)
          let dropdownWidth = 240; // Default width based on CSS
          let dropdownHeight = 200; // Estimated height for multiple items

          if (dropdownMenu) {
            const dropdownRect = dropdownMenu.getBoundingClientRect();
            dropdownWidth = dropdownRect.width || dropdownWidth;
            dropdownHeight = dropdownRect.height || dropdownHeight;
          }

          // Calculate initial position
          let top = buttonRect.bottom + 2; // 2px gap below button

          // Align dropdown's right edge with modal's right edge
          let left = buttonRect.left; // Default to button's left edge
          if (modalRect) {
            // Position dropdown so its right edge aligns with modal's right edge
            left = modalRect.right - dropdownWidth;
          }

          // Edge detection and adjustment

          // Check right edge - if dropdown would overflow viewport width
          if (left + dropdownWidth > viewportWidth) {
            // Align dropdown to the right edge of the button
            left = buttonRect.right - dropdownWidth;

            // If still overflowing, align to viewport right edge with padding
            if (left < 10) {
              left = viewportWidth - dropdownWidth - 10; // 10px padding from edge
            }
          }

          // Check left edge - ensure dropdown doesn't go off-screen
          if (left < 10) { // 10px minimum padding from left edge
            left = 10;
          }

          // Check bottom edge - if dropdown would overflow viewport height
          if (top + dropdownHeight > viewportHeight) {
            // Position dropdown above the button instead
            top = buttonRect.top - dropdownHeight - 2; // 2px gap above button

            // If still overflowing at top, position at top of viewport with padding
            if (top < 10) {
              top = 10; // 10px padding from top edge
            }
          }

          // Check top edge - ensure dropdown doesn't go above viewport
          if (top < 10) {
            top = 10;
          }

          setDropdownPosition({
            top,
            left
          });
        };

        // Use requestAnimationFrame to ensure positioning happens after any ongoing animations
        requestAnimationFrame(() => {
          positionDropdown();
          setShouldRender(true);
          // Trigger animation after positioning
          setTimeout(() => setIsAnimating(true), 10);
        });
      }
    } else {
      setIsAnimating(false);
      // Delay unmounting until animation completes
      setTimeout(() => setShouldRender(false), 150); // Match transition duration
    }
  }, [isOpen]);

  const handleNamespaceClick = (namespace: BotData) => {
    localStorage.setItem('namespace', namespace.id);
    onNamespaceChange?.(namespace);
    setIsOpen(false);
  };

  const handleLogOut = () => {
    clearAuthState();
    setIsOpen(false);
    // Open logout endpoint to properly sign out from Auth0
    window.open('https://dashboard.eesel.ai/api/auth/logout', '_blank');
    // Trigger a page refresh to reset the UI
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  };

  const handleLogin = async () => {
    if (onLogin) {
      setIsProcessing(true);
      try {
        await onLogin();
      } finally {
        setIsProcessing(false);
        setIsOpen(false);
      }
    }
  };

  const handleCreateNewBot = () => {
    // Open the dashboard in a new tab to create a new bot
    window.open('https://dashboard.eesel.ai/v2/settings/bots', '_blank');
  };

  const handleSettings = () => {
    // Open settings page in a new tab
    window.open('https://dashboard.eesel.ai/v2/settings', '_blank');
  };

  // Get the bot icon (custom icon or first letter)
  const getBotIcon = (bot: BotData | null) => {
    if (!bot) return 'S'; // Sign in
    return bot.icon || bot.namespace.charAt(0).toUpperCase();
  };

  // Get the bot color (use provided color or generate unique color based on namespace)
  const getBotColor = (bot: BotData | null) => {
    if (!bot) return '#FD9038'; // Default orange for sign-in state
    
    // If bot has a color, use it; otherwise generate one based on namespace
    return bot.color || getNamespaceColor(bot.namespace);
  };

  // Get display text for bot picker
  const getBotDisplayText = (bot: BotData | null, isAuthenticated: boolean, isLoading: boolean, isProcessing: boolean) => {
    if (isProcessing) return 'Signing in...';
    if (isLoading) return 'Loading...';
    if (!isAuthenticated) return 'Sign in';
    if (!bot && data.length === 0) return 'No bots';
    return bot?.namespace || 'Select Bot';
  };

  // Show sign in if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="eesel-bot-picker-wrapper" ref={dropdownRef}>
        <button
          className="eesel-bot-picker-button"
          onClick={handleLogin}
          disabled={isProcessing || isLoading}
          title="Sign in to Eesel"
        >
          <div className="eesel-bot-picker-content">
            <div 
              className="eesel-bot-picker-icon" 
              style={{ backgroundColor: '#FD9038' }}
            >
              {getBotDisplayText(null, false, isLoading, isProcessing) === 'Signing in...' ? '...' : 'S'}
            </div>
            <span className="eesel-bot-picker-text">
              {getBotDisplayText(null, false, isLoading, isProcessing)}
            </span>
          </div>
        </button>
      </div>
    );
  }

  return (
    <div className="eesel-bot-picker-wrapper" ref={dropdownRef}>
      <button
        className="eesel-bot-picker-button"
        onClick={() => !isLoading && setIsOpen(!isOpen)}
        aria-expanded={isOpen}
        disabled={isLoading}
      >
        <div className="eesel-bot-picker-content">
          <div 
            className="eesel-bot-picker-icon"
            style={{ backgroundColor: getBotColor(currentNamespace) }}
          >
            {getBotIcon(currentNamespace)}
          </div>          <span className="eesel-bot-picker-text">
            {getBotDisplayText(currentNamespace, isAuthenticated, isLoading, isProcessing)}
          </span>
          <div className={`eesel-bot-picker-chevron ${isOpen ? 'eesel-bot-picker-chevron-open' : ''}`}>
            <DropdownArrowIcon />
          </div>
        </div>
      </button>
      
      {shouldRender && !isLoading && createPortal(
        <div
          className={`eesel-bot-picker-menu ${isAnimating ? 'eesel-bot-picker-menu-open' : ''}`}
          style={{
            top: `${dropdownPosition.top}px`,
            left: `${dropdownPosition.left}px`
          }}
        >
          <div className="eesel-bot-picker-section">
            {data.length > 0 ? (
              data.map((namespace) => (                  <button
                    key={`NS_BOT_PICKER_${namespace.id}`}
                    className={`eesel-bot-picker-item ${
                      currentNamespace?.id === namespace.id ? 'eesel-bot-picker-item-active' : ''
                    }`}
                    onClick={() => handleNamespaceClick(namespace)}
                  >
                    <div className="eesel-bot-picker-item-content">
                      <div 
                        className="eesel-bot-picker-item-icon"
                        style={{ backgroundColor: namespace.color || getNamespaceColor(namespace.namespace) }}
                      >
                        {getBotIcon(namespace)}
                      </div>
                      <span className="eesel-bot-picker-item-text">{namespace.namespace}</span>
                    </div>
                    {currentNamespace?.id === namespace.id && (
                      <div className="eesel-bot-picker-check">✓</div>
                    )}
                  </button>
              ))
            ) : (
              <div className="eesel-bot-picker-empty">
                <span className="eesel-bot-picker-item-text">No bots available</span>
              </div>
            )}
            
            <button className="eesel-bot-picker-item eesel-bot-picker-create" onClick={handleCreateNewBot}>
              <span className="eesel-bot-picker-plus">+</span>
              <span className="eesel-bot-picker-item-text">Create a new bot</span>
            </button>
          </div>
          
          <div className="eesel-bot-picker-section">
            <button className="eesel-bot-picker-item" onClick={handleSettings}>
              <span className="eesel-bot-picker-settings">⚙️</span>
              <span className="eesel-bot-picker-item-text">Settings</span>
            </button>
          </div>
          
          <div className="eesel-bot-picker-section">
            <button className="eesel-bot-picker-item" onClick={handleLogOut}>
              <span className="eesel-bot-picker-signout">↗️</span>
              <span className="eesel-bot-picker-item-text">Sign out</span>
            </button>
          </div>
        </div>,
        document.body
      )}
    </div>
  );
};

// New styles for the redesigned modal
const newModalStyles = `
  @keyframes eesel-modal-appear {
    0% {
      opacity: 0;
      transform: scale(0.85) translateY(-20px);
    }
    100% {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  @keyframes eesel-modal-disappear {
    0% {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
    100% {
      opacity: 0;
      transform: scale(0.85) translateY(-20px);
    }
  }

  .eesel-modal-new {
    position: fixed;
    z-index: 999999;
    background: rgba(250, 250, 251, 0.6);
    backdrop-filter: blur(1.83px);
    border-radius: 14.68px;
    box-shadow: 
      0px 16.51px 80.73px -3.67px rgba(28, 40, 64, 0.14),
      0px 7.34px 25.69px -5.50px rgba(28, 40, 64, 0.12);
    padding: 1.83px;
    width: 448px;
    height: 190px;
    animation: eesel-modal-appear 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    transform-origin: center;
  }

  .eesel-modal-new.eesel-modal-disappearing {
    animation: eesel-modal-disappear 0.2s cubic-bezier(0.55, 0.06, 0.68, 0.19) forwards;
  }

  .eesel-modal-inner {
    background: #FFFFFF;
    border-radius: 12px;
    box-shadow: inset 0px 0px 0px 0.91px rgba(238, 239, 241, 1);
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .eesel-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10.86px 10px 10.86px 14px;
    border-bottom: 0.91px solid #E6E7E9;
    background: #FBFBFB;
  }

  .eesel-status-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    background: #F4F5F6;
    border: 1px solid #EEEFF1;
    border-radius: 8px;
    padding: 2px 6px 2px 4px;
    height: 20px;
  }

  .eesel-status-text {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    font-weight: 500;
    font-size: 12px;
    line-height: 1.33;
    letter-spacing: -2%;
    color: #5C5E63;
  }

  .eesel-header-actions {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .eesel-bot-picker {
    width: 18.67px;
    height: 18.67px;
    border-radius: 7px;
    background: #FD9038;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    font-weight: 500;
    font-size: 12.44px;
    color: #FEEEE1;
    text-transform: uppercase;
    line-height: 1em;
  }

  /* Custom Bot Picker Styles - Vanilla CSS replacing Tailwind */
  .eesel-bot-picker-wrapper {
    position: relative;
    display: inline-block;
  }

  .eesel-bot-picker-button {
    display: flex;
    align-items: center;
                                                 gap: 3.11px;
    padding: 4.67px 6.22px;
    background: #FFFFFF;
    border: none;
    border-radius: 7.78px;
    box-shadow: 
      0px 3.11px 4.67px 0px rgba(35, 37, 41, 0.03),
      inset 0px 0px 0px 0.78px rgba(238, 239, 241, 1);
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .eesel-bot-picker-button:hover {
    background: #F8F9FA;
  }

  .eesel-bot-picker-content {
    display: flex;
    gap: 3.11px;
    align-items: center;
  }

  .eesel-bot-picker-icon {
    width: 18.67px;
    height: 18.67px;
    border-radius: 7px;
    background: #FD9038;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    font-weight: 500;
    font-size: 12.44px;
    color: #FEEEE1;
    text-transform: uppercase;
    line-height: 1em;
  }

  .eesel-bot-picker-text {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    font-size: 11px;
    font-weight: 500;
    color: #232529;
    line-height: 1em;
  }

  .eesel-bot-picker-chevron {
    width: 10.89px;
    height: 10.89px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #232529;
    transition: transform 0.2s ease;
  }
    transition: transform 0.2s ease;
    margin-left: -6px;
    margin-top: 2px;
  }

  .eesel-bot-picker-chevron-open {
    transform: rotate(180deg);
  }

  .eesel-bot-picker-menu {
    position: fixed; /* Fixed positioning to render above modal */
    top: auto; /* Will be set dynamically */
    left: auto; /* Will be set dynamically */
    width: 240px; /* Reduced from 288px to better match modal */
    max-height: 280px; /* Reduced from 400px */
    overflow-y: auto;
    overflow-x: hidden;
    background: white;
    border-radius: 8px; /* Reduced from 12px */
    box-shadow:
      0px 8px 40px -2px rgba(28, 40, 64, 0.12),
      0px 4px 16px -4px rgba(28, 40, 64, 0.08);
    border: 1px solid rgba(238, 239, 241, 1);
    z-index: 10000000; /* Much higher than modal's 999999 */
    padding: 4px 0; /* Reduced from 6px */
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    /* Ensure smooth scrolling */
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;

    /* Smooth transition effects */
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    /* Keep pointer-events enabled to allow clicks during closing animation */
    pointer-events: auto;
  }

  .eesel-bot-picker-menu.eesel-bot-picker-menu-open {
    opacity: 1;
    transform: translateY(0) scale(1);
  }

  .eesel-bot-picker-section {
    margin: 0;
    border-bottom: 1px solid #EEEFF1;
    padding: 2px 0; /* Reduced from 4px */
  }

  .eesel-bot-picker-section:last-child {
    border-bottom: none;
  }

  .eesel-bot-picker-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    background: none;
    border: none;
    padding: 6px 10px; /* Reduced from 8px 12px */
    margin: 0 4px; /* Reduced from 0 6px */
    border-radius: 6px; /* Reduced from 8px */
    cursor: pointer;
    text-decoration: none;
    font-size: 13px; /* Reduced from 14px */
    font-weight: 500;
    color: #232529;
    transition: background-color 0.15s ease;
    min-height: 32px; /* Reduced from 40px */
  }

  .eesel-bot-picker-item:hover {
    background: #F8F9FA;
  }

  .eesel-bot-picker-item-active {
    background: #F3F4F6;
  }

  .eesel-bot-picker-item-content {
    display: flex;
    align-items: center;
    gap: 6px; /* Reduced from 8px */
    flex: 1;
  }

  .eesel-bot-picker-item-icon {
    width: 18px; /* Reduced from 20px */
    height: 18px; /* Reduced from 20px */
    border-radius: 6px; /* Reduced from 8px */
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    font-weight: 600;
    font-size: 11px; /* Reduced from 12px */
    color: white;
    text-transform: uppercase;
    line-height: 1em;
    flex-shrink: 0;
  }

  .eesel-bot-picker-item-text {
    font-size: 13px; /* Reduced from 14px to match item font size */
    font-weight: 500;
    color: #232529;
    line-height: 1.2;
    flex: 1;
    text-align: left;
  }

  .eesel-bot-picker-check {
    width: 14px; /* Reduced from 16px */
    height: 14px; /* Reduced from 16px */
    color: #266DF0;
    font-weight: 600;
    font-size: 11px; /* Reduced from 12px */
    flex-shrink: 0;
  }

  .eesel-bot-picker-empty {
    padding: 8px; /* Reduced from 12px */
    text-align: center;
    color: #75777C;
    font-size: 13px; /* Reduced from 14px */
  }

  .eesel-bot-picker-create {
    display: flex;
    align-items: center;
    gap: 6px; /* Reduced from 8px */
    color: #266DF0;
  }

  .eesel-bot-picker-plus {
    font-size: 14px; /* Reduced from 16px */
    font-weight: 500;
    width: 18px; /* Reduced from 20px */
    height: 18px; /* Reduced from 20px */
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .eesel-bot-picker-settings,
  .eesel-bot-picker-signout {
    font-size: 13px; /* Reduced from 14px */
    width: 18px; /* Reduced from 20px */
    height: 18px; /* Reduced from 20px */
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .eesel-modal-content {
    padding: 12px 12px 14px 0px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    overflow-y: auto;
    flex: 1;
  }

  .eesel-input-section {
    display: flex;
    gap: 8px;
    padding: 0px 0px 4px 14px;
    flex: 1;
  }

  .eesel-textarea-new {
    flex: 1;
    border: none;
    outline: none;
    resize: none;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    font-weight: 500;
    font-size: 14px;
    line-height: 1.43;
    letter-spacing: -2%;
    color: #232529;
    background: transparent;
    min-height: 100px;
    height: 100%;
    padding: 8px 12px;
    border-radius: 8px;
    transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  }

  .eesel-textarea-new:focus {
    background: rgba(147, 197, 253, 0.03);
    outline: none;
    box-shadow: 0 0 0 1px rgba(147, 197, 253, 0.15);
  }

  .eesel-textarea-new::placeholder {
    color: #75777C;
  }

  .eesel-modal-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 12px 8px 12px; /* Match textarea horizontal padding for alignment */
  }

  .eesel-footer-left {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .eesel-footer-right {
    display: flex;
    align-items: center;
    gap: 2px;
  }

  .eesel-icon-button {
    padding: 4px;
    border-radius: 6px;
    border: 1px solid #EEEFF1;
    background: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #75777C;
  }

  .eesel-icon-button:hover {
    background: #F8F9FA;
  }

  .eesel-icon-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .eesel-draft-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px;
    border-radius: 6px; 
    border: 1px solid #2666DC;
    background: #266DF0; 
    color: white;
    transition: all 0.2s;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    min-width: 24px; /* Ensure consistent minimum size */
    min-height: 24px; /* Ensure consistent minimum size */
  }

  .eesel-draft-button:hover:not(:disabled) {
    background: #1E5AE6;
    border-color: #1E4DBF;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }

  .eesel-draft-button:disabled {
    cursor: not-allowed !important;
    opacity: 0.5 !important;
    background: #9ca3af !important; /* disabled state */
    border-color: #9ca3af !important; /* disabled border color */
    color: #ffffff !important;
    pointer-events: none;
  }

  .eesel-close-button {
    width: 20px;
    height: 20px;
    border-radius: 6px;
    border: none;
    background: transparent;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #75777C;
    padding: 3px;
  }

  .eesel-close-button:hover {
    background: #F1F3F4;
  }

  .eesel-sidebar-button {
    width: 20px;
    height: 20px;
    border-radius: 6px;
    border: none;
    background: transparent;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #75777C;
    padding: 3px;
  }

  .eesel-sidebar-button:hover {
    background: #F1F3F4;
  }

  .eesel-loading-spinner {
    /* Match icon size to prevent button reshaping */
    display: flex;
    justify-content: center;
    align-items: center;
    width: 14px; /* Match icon size */
    height: 14px; /* Match icon size */
  }

  .eesel-spinner-svg {
    width: 14px; /* Match icon size */
    height: 14px; /* Match icon size */
    color: rgba(229, 231, 235, 1); /* text-gray-200 */
    animation: eesel-spin 1s linear infinite;
  }

  .eesel-spinner-svg path:first-child {
    fill: currentColor;
  }

  .eesel-spinner-svg path:last-child {
    fill: white; /* Matches currentFill in ui-core */
  }

  @keyframes eesel-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

// Authentication and namespace management
interface AuthState {
  isAuthenticated: boolean;
  accessToken: string | null;
  user: any | null;
}

interface BotData extends Namespace {
  color?: string;
  icon?: string;
  description?: string;
}

// Authentication functions
const getAuthState = (): AuthState => {
  try {
    const authData = localStorage.getItem('eesel-auth');
    if (authData) {
      return JSON.parse(authData);
    }
  } catch (error) {
    console.error('[Eesel] Error parsing auth data:', error);
  }
  
  return {
    isAuthenticated: false,
    accessToken: null,
    user: null
  };
};

const setAuthState = (authState: AuthState) => {
  localStorage.setItem('eesel-auth', JSON.stringify(authState));
};

const clearAuthState = () => {
  localStorage.removeItem('eesel-auth');
  localStorage.removeItem('namespace');
  localStorage.removeItem('eesel-namespaces-cache');
  localStorage.removeItem('eesel-namespaces-cache-time');
};

// Function to fetch namespaces via background script (bypasses CORS)
const fetchNamespaces = async (): Promise<BotData[]> => {
  const authState = getAuthState();
  
  if (!authState.isAuthenticated || !authState.accessToken) {
    console.log('[Eesel] Not authenticated, returning empty array');
    return [];
  }

  try {
    console.log('[Eesel] Fetching namespaces via background script (no cache)...');
    
    // Use Chrome messaging to fetch via background script (bypasses CORS)
    const response = await new Promise<any>((resolve, reject) => {
      chrome.runtime.sendMessage(
        { type: MessageTypes.GET_NAMESPACES },
        (response) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(response);
          }
        }
      );
    });

    if (response.error) {
      if (response.error.includes('401') || response.error.includes('Unauthorized')) {
        console.log('[Eesel] Access token expired, clearing auth');
        clearAuthState();
        return [];
      }
      throw new Error(response.error);
    }

    let namespacesArray: any[] = [];
    
    // Handle different response formats
    if (response.namespaces && Array.isArray(response.namespaces)) {
      namespacesArray = response.namespaces;
    } else if (Array.isArray(response)) {
      namespacesArray = response;
    } else {
      console.warn('[Eesel] Unexpected response format:', response);
      namespacesArray = [];
    }

    // Transform namespaces to include color and icon data
    const transformedNamespaces: BotData[] = namespacesArray.map((ns: any) => ({
      id: ns.id || ns._id || Math.random().toString(36),
      namespace: ns.namespace || ns.name || ns.title || 'Unnamed Bot',
      color: ns.color || ns.theme_color, // Only use server-provided color, no default
      icon: (ns.icon || (ns.namespace || ns.name || ns.title || 'U').charAt(0).toUpperCase()),
      description: ns.description || ns.summary
    }));

    console.log('[Eesel] Fetched namespaces:', transformedNamespaces);
    return transformedNamespaces;

  } catch (error) {
    console.error('[Eesel] Error fetching namespaces:', error);
    // Return empty array for errors
    return [];
  }
};

// Authentication flow
const initiateLogin = () => {
  // Create a simple returnTo parameter for the auth flow
  const returnUrl = `https://dashboard.eesel.ai/v2/dashboard`;
  const authUrl = `https://dashboard.eesel.ai/api/auth/login?returnTo=${encodeURIComponent(returnUrl)}`;
  
  console.log('[Eesel] Opening authentication popup:', authUrl);
  
  const popup = window.open(
    authUrl,
    'eesel-auth',
    'width=500,height=600,scrollbars=yes,resizable=yes'
  );

  return new Promise<AuthState>((resolve, reject) => {
    if (!popup) {
      console.error('[Eesel] Failed to open authentication popup');
      reject(new Error('Failed to open authentication popup'));
      return;
    }

    let pollCount = 0;
    const maxPolls = 300; // 5 minutes (1 second intervals)
    let isResolving = false;

    const pollForAuth = async () => {
      pollCount++;
      
      if (isResolving) return; // Prevent duplicate resolution
      
      if (popup.closed) {
        // Popup was closed, check if we have auth now
        console.log('[Eesel] Popup closed, checking for authentication...');
        if (!isResolving) {
          isResolving = true;
          try {
            const authState = await checkExistingAuth();
            if (authState) {
              console.log('[Eesel] Authentication successful after popup closed');
              resolve(authState);
            } else {
              console.log('[Eesel] No authentication found after popup closed');
              reject(new Error('Authentication cancelled by user'));
            }
          } catch (error) {
            console.error('[Eesel] Error checking auth after popup closed:', error);
            reject(new Error('Authentication verification failed'));
          }
        }
        return;
      }

      if (pollCount > maxPolls) {
        console.log('[Eesel] Authentication timeout');
        popup.close();
        reject(new Error('Authentication timeout'));
        return;
      }

      try {
        // Check the popup URL to see if we've reached a dashboard page (indicates success)
        const href = popup.location?.href;
        if (href && href.includes('dashboard.eesel.ai') && 
            (href.includes('/v2/dashboard') || href.includes('/v2/settings'))) {
          console.log('[Eesel] Authentication appears successful, checking token...');
          
          if (!isResolving) {
            isResolving = true;
            try {
              // Try to get the authentication token via API
              const authState = await checkExistingAuth();
              if (authState) {
                console.log('[Eesel] Authentication verified successfully');
                popup.close();
                resolve(authState);
                return;
              }
            } catch (error) {
              console.error('[Eesel] Error verifying authentication:', error);
              // Continue polling instead of failing immediately
              isResolving = false;
            }
          }
        }
      } catch (error) {
        // Cross-origin errors are expected while navigating
        // Continue polling silently
      }

      // Continue polling
      setTimeout(pollForAuth, 1000);
    };

    // Start polling after a short delay
    setTimeout(pollForAuth, 2000);

    // Listen for manual messages from dashboard (fallback)
    const messageListener = (event: MessageEvent) => {
      if (event.origin !== 'https://dashboard.eesel.ai') return;

      console.log('[Eesel] Received message from dashboard:', event.data);

      if (event.data.type === 'EESEL_AUTH_SUCCESS' && !isResolving) {
        isResolving = true;
        window.removeEventListener('message', messageListener);

        const authState: AuthState = {
          isAuthenticated: true,
          accessToken: event.data.accessToken,
          user: event.data.user
        };

        setAuthState(authState);
        popup?.close();
        resolve(authState);
      } else if (event.data.type === 'EESEL_AUTH_ERROR' && !isResolving) {
        isResolving = true;
        window.removeEventListener('message', messageListener);
        popup?.close();
        reject(new Error(event.data.error || 'Authentication failed'));
      } else if (event.data.type === 'intercom-snippet__ready' && !isResolving) {
        // Intercom snippet ready indicates successful authentication
        console.log('[Eesel] Intercom snippet ready - checking authentication...');

        // Check if we have valid authentication now
        checkExistingAuth().then((authState) => {
          if (authState && !isResolving) {
            isResolving = true;
            window.removeEventListener('message', messageListener);
            console.log('[Eesel] Authentication confirmed via intercom-snippet__ready');
            popup?.close();
            resolve(authState);
          }
        }).catch((error) => {
          console.log('[Eesel] Auth check failed after intercom-snippet__ready:', error);
          // Continue polling instead of failing
        });
      }
    };

    window.addEventListener('message', messageListener);
    
    // Cleanup message listener when promise resolves/rejects
    const originalResolve = resolve;
    const originalReject = reject;
    
    resolve = (value) => {
      window.removeEventListener('message', messageListener);
      originalResolve(value);
    };
    
    reject = (reason) => {
      window.removeEventListener('message', messageListener);
      originalReject(reason);
    };
  });
};

// Function to check if user is already authenticated via background script (bypasses CORS)
const checkExistingAuth = async (): Promise<AuthState | null> => {
  try {
    console.log('[Eesel] Checking for existing authentication via background script...');
    
    // Use Chrome messaging to check auth via background script (bypasses CORS)
    const response = await new Promise<any>((resolve, reject) => {
      chrome.runtime.sendMessage(
        { type: MessageTypes.CHECK_AUTH },
        (response) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(response);
          }
        }
      );
    });

    console.log('[Eesel] Auth check response:', response);

    if (response.success && response.token) {
      const authState: AuthState = {
        isAuthenticated: true,
        accessToken: response.token,
        user: response.user || {}
      };
      console.log('[Eesel] Authentication state created:', authState);
      setAuthState(authState);
      return authState;
    } else {
      console.log('[Eesel] No valid authentication found:', response.error || 'No token');
    }
  } catch (error) {
    console.log('[Eesel] Error checking existing authentication:', error);
  }
  
  console.log('[Eesel] No existing authentication found');
  return null;
};

const InputComponent: React.FC<{
  initialPosition: { x: number; y: number };
}> = ({ initialPosition }) => {
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [hasLLMResponse, setHasLLMResponse] = useState(false);
  const [isDisappearing, setIsDisappearing] = useState(false);
  // Modified to track both compose field content and user input
  const [draftHistory, setDraftHistory] = useState<
    Array<{ composeContent: string; userInput: string }>
  >([]);
  const [selectedNamespace, setSelectedNamespace] = useState(
    'b826df9f-96d0-4d17-8bf3-b8115fe0d453'
  );
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [autoDraftEnabled, setAutoDraftEnabled] = useState(isAutoDraftEnabled);
  const [ignoreLatestMessage, setIgnoreLatestMessage] = useState(false);
  
  // Authentication and bot picker state
  const [authState, setAuthState] = useState<AuthState>(() => getAuthState());
  const [namespaces, setNamespaces] = useState<BotData[]>([]);
  const [isLoadingNamespaces, setIsLoadingNamespaces] = useState(true);
  const [currentNamespace, setCurrentNamespace] = useState<BotData | null>(null);
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [position, setPosition] = useState(initialPosition);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });

  // Store the latest drag position to avoid state update delays
  const latestDragPositionRef = useRef(initialPosition);

  // Note: Position loading is now handled by modal-manager to avoid race conditions

  // Sync with persisted setting on component mount and when storage changes
  useEffect(() => {
    // Load current setting from storage
    chrome.storage.local.get(['eesel-auto-draft-enabled'], result => {
      if (result['eesel-auto-draft-enabled'] !== undefined) {
        setAutoDraftEnabled(result['eesel-auto-draft-enabled']);
      }
    });

    // Listen for storage changes (in case setting is changed elsewhere)
    const handleStorageChange = (changes: any, namespace: string) => {
      if (namespace === 'local' && changes['eesel-auto-draft-enabled']) {
        setAutoDraftEnabled(changes['eesel-auto-draft-enabled'].newValue);
      }
    };

    chrome.storage.onChanged.addListener(handleStorageChange);

    // Cleanup listener on unmount
    return () => {
      chrome.storage.onChanged.removeListener(handleStorageChange);
    };
  }, []);

  // Load namespaces on component mount and when auth changes
  useEffect(() => {
    const loadNamespaces = async () => {
      if (!authState.isAuthenticated) {
        // If not authenticated, clear namespaces and current namespace
        setNamespaces([]);
        setCurrentNamespace(null);
        setIsLoadingNamespaces(false);
        return;
      }

      setIsLoadingNamespaces(true);
      try {
        const fetchedNamespaces = await fetchNamespaces();
        setNamespaces(fetchedNamespaces);
        
        if (fetchedNamespaces.length > 0) {
          // Find current namespace from storage or use first available
          const savedNamespaceId = localStorage.getItem('namespace') || selectedNamespace;
          const currentNs = fetchedNamespaces.find((ns: BotData) => ns.id === savedNamespaceId) || fetchedNamespaces[0];
          
          setCurrentNamespace(currentNs);
          setSelectedNamespace(currentNs.id);
          localStorage.setItem('namespace', currentNs.id);
        } else {
          // No namespaces available
          setCurrentNamespace(null);
        }
      } catch (error) {
        console.error('[Eesel] Failed to load namespaces:', error);
        setNamespaces([]);
        setCurrentNamespace(null);
      } finally {
        setIsLoadingNamespaces(false);
      }
    };

    loadNamespaces();
  }, [authState.isAuthenticated]); // Reload when auth state changes

  // Check for existing authentication on component mount
  useEffect(() => {
    const checkAuth = async () => {
      const currentAuth = getAuthState();
      if (!currentAuth.isAuthenticated) {
        // Try to get authentication from existing session
        console.log('[Eesel] Checking for existing authentication...');
        const existingAuth = await checkExistingAuth();
        if (existingAuth) {
          setAuthState(existingAuth);
          console.log('[Eesel] Found existing authentication');
        } else {
          console.log('[Eesel] No existing authentication found');
        }
      }
    };

    checkAuth();
  }, []); // Run once on mount

  // Function to toggle auto-draft feature
  const toggleAutoDraft = () => {
    isAutoDraftEnabled = !isAutoDraftEnabled;
    setAutoDraftEnabled(isAutoDraftEnabled);

    // Persist the setting to Chrome storage
    chrome.storage.local.set({
      'eesel-auto-draft-enabled': isAutoDraftEnabled,
    });
    console.log(
      `[Eesel] Auto-draft ${isAutoDraftEnabled ? 'enabled' : 'disabled'} and saved to storage`
    );
  };

  // Function to toggle ignore latest message feature
  const toggleIgnoreLatestMessage = () => {
    setIgnoreLatestMessage(!ignoreLatestMessage);
    console.log(
      `[Eesel] Ignore latest message ${!ignoreLatestMessage ? 'enabled' : 'disabled'}`
    );
  };

  // Handle namespace change from bot picker
  const handleNamespaceChange = (namespace: Namespace) => {
    setCurrentNamespace(namespace);
    setSelectedNamespace(namespace.id);
    localStorage.setItem('namespace', namespace.id);
    console.log(`[Eesel] Switched to namespace: ${namespace.namespace} (${namespace.id})`);
  };

  // Handle login from bot picker
  const handleLogin = async () => {
    try {
      console.log('[Eesel] Initiating login...');
      const newAuthState = await initiateLogin();
      setAuthState(newAuthState);
      console.log('[Eesel] Login successful:', newAuthState);
      
      // Refresh namespaces after login
      setIsLoadingNamespaces(true);
      const fetchedNamespaces = await fetchNamespaces();
      setNamespaces(fetchedNamespaces);
      
      if (fetchedNamespaces.length > 0) {
        const firstNamespace = fetchedNamespaces[0];
        setCurrentNamespace(firstNamespace);
        setSelectedNamespace(firstNamespace.id);
        localStorage.setItem('namespace', firstNamespace.id);
      }
      setIsLoadingNamespaces(false);
    } catch (error) {
      console.error('[Eesel] Login failed:', error);
      setIsLoadingNamespaces(false);
    }
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.button === 0) {
      // Left mouse button
      setIsDragging(true);
      const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      });
    }
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      const newPosition = {
        x: Math.max(
          0,
          Math.min(e.clientX - dragOffset.x, window.innerWidth - 450)
        ), // Keep within bounds
        y: Math.max(
          0,
          Math.min(e.clientY - dragOffset.y, window.innerHeight - 320)
        ), // Keep within bounds
      };
      console.log('[Eesel] Dragging to position:', newPosition);

      // Store the latest position in ref for immediate access
      latestDragPositionRef.current = newPosition;
      setPosition(newPosition);
    }
  };

  const handleMouseUp = () => {
    if (isDragging) {
      setIsDragging(false);

      // Use the latest drag position from the ref to ensure we save the correct position
      const finalPosition = latestDragPositionRef.current;
      console.log('[Eesel] Mouse up, saving final position:', finalPosition);

      chrome.storage.local.set(
        {
          'eesel-modal-position': finalPosition,
        },
        () => {
          console.log(
            '[Eesel] Modal position saved successfully:',
            finalPosition
          );
        }
      );
    }
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, dragOffset]);

  const handleSend = async () => {
    if (!input.trim()) return;

    setIsLoading(true);

    try {
      // Before replacing content, save both the current compose content and user input to history
      const currentComposeContent = await extractCurrentContent();
      const currentUserInput = input.trim();

      // Save the state before making changes
      setDraftHistory(prevHistory => [
        ...prevHistory,
        {
          composeContent: currentComposeContent || '',
          userInput: currentUserInput,
        },
      ]);
      console.log('Saved current state to draft history:', {
        composeContent: currentComposeContent,
        userInput: currentUserInput,
      });

      // 1. Extract thread content using unified extractor directly
      const extractOptions: GmailExtractionOptions = {
        ignoreLatestMessage: ignoreLatestMessage,
        includeHeaders: true,
        maxMessages: undefined,
      };

      const emailContext =
        await unifiedExtractor.extractThreadContent(extractOptions);

      // 2. Construct the combined message with HTML formatting request
      const messageWithContext = `[USER CONTEXT]\n${input.trim()}\n\n${emailContext}`;

      // 3. Send message with combined text and namespaceId
      const response = await new Promise<AIResponse>((resolve, reject) => {
        const timeoutId = setTimeout(() => {
          reject(new Error('Request timed out'));
        }, 60000); // 60 second timeout

        try {
          chrome.runtime.sendMessage(
            {
              type: MessageTypes.REQUEST_CONTEXT,
              message: messageWithContext,
              namespaceId: selectedNamespace,
            },
            response => {
              clearTimeout(timeoutId);
              if (chrome.runtime.lastError) {
                console.error(
                  'Error sending message:',
                  chrome.runtime.lastError
                );
                reject(chrome.runtime.lastError);
              } else if (!response) {
                reject(new Error('No response received'));
              } else {
                resolve(response as AIResponse);
              }
            }
          );
        } catch (error) {
          clearTimeout(timeoutId);
          reject(error);
        }
      });

      if (response?.text) {
        console.log('AI Response:', response.text);

        replaceEmailContent(response.text, true); // Skip focus so we can return to modal
        setHasLLMResponse(true);
        
        // Return focus to the modal's textarea after drafting
        setTimeout(() => {
          if (textareaRef.current) {
            textareaRef.current.focus();
          }
        }, 100);
      } else {
        throw new Error('No response text received');
      }
    } catch (error) {
      console.error('Error in handleSend:', error);
      const errorMessage =
        'Sorry, there was an error processing your request. Please try again.';

      replaceEmailContent(errorMessage, true); // Skip focus so we can return to modal
      setHasLLMResponse(true); // Still enable undo even in case of error
      
      // Return focus to the modal's textarea even after error
      setTimeout(() => {
        if (textareaRef.current) {
          textareaRef.current.focus();
        }
      }, 100);
    } finally {
      setIsLoading(false);
      setInput(''); // Clear the input after sending
      setHasLLMResponse(true);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleClose();
    }
  };

  // Helper function to extract current content from the input field
  const extractCurrentContent = async (): Promise<string> => {
    try {
      const platform = unifiedExtractor.detectPlatform().platform;
      if (platform === 'gmail' || platform === 'intercom') {
        // Use the HTML-preserving function for undo functionality
        const content = getDraftContentWithHTML();
        return content || '';
      }
      return '';
    } catch (error) {
      console.error('Error extracting current content', error);
      return '';
    }
  };

  const handleUndo = async () => {
    if (hasLLMResponse && draftHistory.length > 0) {
      // Get the last state from history
      const previousState = draftHistory[draftHistory.length - 1];

      // Remove the last state from history
      setDraftHistory(prevHistory => prevHistory.slice(0, -1));

      // Restore both the compose content and user input
      replaceEmailContent(previousState.composeContent);
      setInput(previousState.userInput);

      console.log('Reverted to previous state:', previousState);

      // If there are no more states in history, disable the undo button
      if (draftHistory.length <= 1) {
        setHasLLMResponse(false);
      }
    } else if (hasLLMResponse) {
      // If no history but we have an LLM response, just clear everything
      replaceEmailContent('');
      setInput('');
      setHasLLMResponse(false);
      console.log('No previous states, cleared all content');
    }
  };

  const handleClose = () => {
    setIsDisappearing(true);
    
    // Wait for the animation to complete before actually closing
    setTimeout(() => {
      closeModal();
      // Show the chat bubble again
      toggleChatBubble(true);
    }, 200); // Match the animation duration
  };

  // Add global ESC key listener for smooth close animation
  useEffect(() => {
    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        e.preventDefault();
        handleClose();
      }
    };

    document.addEventListener('keydown', handleGlobalKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleGlobalKeyDown);
    };
  }, []);

  return (
    <div
      className={`eesel-modal-new${isDisappearing ? ' eesel-modal-disappearing' : ''}`}
      style={{
        left: position.x !== null ? `${position.x}px` : undefined,
        top: position.y !== null ? `${position.y}px` : undefined,
        cursor: isDragging ? 'grabbing' : 'default',
      }}
    >
      <div className="eesel-modal-inner">
        {/* Header */}
        <div 
          className="eesel-modal-header"
          onMouseDown={handleMouseDown}
          style={{ cursor: isDragging ? 'grabbing' : 'grab' }}
        >
          <div className="eesel-status-indicator">
            <CheckIcon />
            <span className="eesel-status-text">Email and current draft read</span>
          </div>
          
          <div className="eesel-header-actions">
            <CustomBotPicker 
              data={namespaces}
              currentNamespace={currentNamespace}
              onNamespaceChange={handleNamespaceChange}
              onLogin={handleLogin}
              isAuthenticated={authState.isAuthenticated}
              isLoading={isLoadingNamespaces}
            />
            <button
              className="eesel-sidebar-button"
              title="Sidebar"
            >
              <SidebarIcon />
            </button>
            <button
              className="eesel-close-button"
              onClick={handleClose}
              aria-label="Close"
            >
              <CloseIcon />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="eesel-modal-content">
          <div className="eesel-input-section">
            <textarea
              className="eesel-textarea-new"
              value={input}
              onChange={e => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Write a few quick notes on what you want to reply with..."
              autoFocus
              ref={textareaRef}
              rows={1}
              style={{ 
                minHeight: '20px',
                               maxHeight: '100px',
                overflow: 'auto'
              }}
            />
          </div>
        </div>

        {/* Footer */}
        <div className="eesel-modal-footer">
          <div className="eesel-footer-left">
            {/* Add icon button */}
            <button 
              className="eesel-icon-button"
              title="Add to list"
                       >
              <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
                <path d="M6.4 2.9h1.2v8.2H6.4V2.9Z" fill="currentColor"/>
                <path d="M2.9 6.4v1.2h8.2V6.4H2.9Z" fill="currentColor"/>
              </svg>
            </button>
            
            {/* Settings button */}
            <button
              className="eesel-icon-button"
              title="Settings"
            >
              <SettingsIcon />
            </button>
            
            {/* Undo button */}
            <button
              className="eesel-icon-button"
              onClick={handleUndo}
              disabled={!hasLLMResponse}
              title="Undo"
            >
              <UndoIcon />
            </button>
          </div>
          
          <div className="eesel-footer-right">
            {/* Draft button */}
            <button
              onClick={handleSend}
              disabled={!input.trim() || isLoading}
              className="eesel-draft-button"
              title="Draft"
            >
              {isLoading ? (
                // Match ui-core LoadingSpinner SVG exactly
                <div className="eesel-loading-spinner">
                  <svg
                    aria-hidden="true"
                    className="eesel-spinner-svg"
                    viewBox="0 0 100 101"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                      fill="currentColor"
                    />
                    <path
                      d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                      fill="white"
                    />
                  </svg>
                </div>
              ) : (
                <DraftIcon />
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Set the InputComponent reference for the modal manager
setInputComponent(InputComponent);

export function initTextInput() {
  console.log('[Eesel] initTextInput called');

  // Add styles
  const styleElement = document.createElement('style');
  styleElement.textContent = modalStyles + newModalStyles;
  document.head.appendChild(styleElement);

  const platform = unifiedExtractor.detectPlatform().platform;
  console.log(`[Eesel] Detected platform: ${platform}`);

  if (platform === 'unknown') {
    console.log('[Eesel] Unknown platform, no buttons will be created');
       return;
  }

  // Floating buttons functionality removed - only using fixed chat bubble now

  // Removed setupComposeObserver - no longer needed without floating buttons

  // Observer to detect when compose pages are loaded/changed
  function setupChatBubbleObserver() {
    const observer = new MutationObserver(mutations => {
      let shouldCheckCompose = false;

      mutations.forEach(mutation => {
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element;

            // Check for Gmail compose elements
            if (
              element.matches &&
              (element.matches('.M9') ||
                element.matches('.nH.if') ||
                element.matches('[aria-label="Message Body"]'))
            ) {
              shouldCheckCompose = true;
            }

            // Check for Intercom compose elements
            if (
              element.matches &&
              (element.matches('.inbox2__composer__container') ||
                element.querySelector('.inbox2__composer__container'))
            ) {
              shouldCheckCompose = true;
            }

            // Also check if compose elements are found in added subtrees
            if (
              element.querySelector &&
              (element.querySelector('.M9') ||
                element.querySelector('.nH.if') ||
                element.querySelector('[aria-label="Message Body"]') ||
                element.querySelector('.inbox2__composer__container'))
            ) {
              shouldCheckCompose = true;
            }
          }
        });

        // Also check for removed nodes to detect when compose windows disappear
        mutation.removedNodes.forEach(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element;

            // Check if compose elements were removed
            if (
              element.matches &&
              (element.matches('.M9') ||
                element.matches('.nH.if') ||
                element.matches('[aria-label="Message Body"]') ||
                element.matches('.inbox2__composer__container'))
            ) {
              shouldCheckCompose = true;
            }

            // Also check if compose elements were in removed subtrees
            if (
              element.querySelector &&
              (element.querySelector('.M9') ||
                element.querySelector('.nH.if') ||
                element.querySelector('[aria-label="Message Body"]') ||
                element.querySelector('.inbox2__composer__container'))
            ) {
              shouldCheckCompose = true;
            }
          }
        });
      });

      if (shouldCheckCompose) {
        console.log(
          '[Eesel] Detected compose page changes, checking for chat bubbles'
        );
        setTimeout(() => {
          // Find all composer elements and ensure they have bubbles
          const composers = findComposerElements();

          if (composers.length > 0) {
            console.log(
              `[Eesel] Found ${composers.length} composers, ensuring bubbles exist`
            );

            // Create bubbles for new composers
            createChatBubbles();

            // Make sure all chat bubbles are visible (only if modal is not open)
            if (!getModalRoot()) {
              toggleChatBubble(true);
            }

            // Note: Removed auto-opening modal - modal should only open on chat bubble click
            console.log('[Eesel] Compose became visible, chat bubbles are now visible');
          } else {
            // No composers visible - hide all chat bubbles and close modal
            console.log(
              '[Eesel] No composers detected, hiding all chat bubbles'
            );
            toggleChatBubble(false);

            // Close modal if it's open since composers are no longer visible
            if (getModalRoot()) {
              console.log('[Eesel] Composers disappeared, closing modal');
              closeModal();
            }
          }
        }, 100);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    console.log('[Eesel] Chat bubble observer setup complete');
  }

  // Initialize observers and chat bubbles (dynamic composer detection)
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setupReplyButtonObserver(); // Add reply button observer
      setupChatBubbleObserver(); // Add chat bubble observer

      // Create chat bubbles for any existing composers on supported platforms
      if (platform === 'gmail' || platform === 'intercom') {
        setTimeout(() => {
          createChatBubbles();
        }, 500);
      }

      // Only auto-open modal if compose is visible (not just on page load)
      setTimeout(() => {
        const composers = findComposerElements();
        if (composers.length > 0) {
          console.log(
            `[Eesel] ${composers.length} composers detected, showing chat bubbles`
          );
          // Note: Removed auto-opening modal - modal should only open on chat bubble click
          createChatBubbles(); // Just create the chat bubbles without opening modal
        } else {
          console.log(
            '[Eesel] No composers detected, no chat bubbles needed'
          );
        }
      }, 1000);
    });
  } else {
    setupReplyButtonObserver(); // Add reply button observer
    setupChatBubbleObserver(); // Add chat bubble observer

    // Create chat bubbles for any existing composers on supported platforms
    if (platform === 'gmail' || platform === 'intercom') {
      setTimeout(() => {
        createChatBubbles();
      }, 500);
    }

    // Only auto-open modal if compose is visible (not just on page load)
    setTimeout(() => {
      const composers = findComposerElements();
      if (composers.length > 0) {
        console.log(
          `[Eesel] ${composers.length} composers detected, showing chat bubbles`
        );
        // Note: Removed auto-opening modal - modal should only open on chat bubble click
        createChatBubbles(); // Just create the chat bubbles without opening modal
      } else {
        console.log('[Eesel] No composers detected, no chat bubbles needed');
      }
    }, 1000);
  }
}

// Function to generate consistent colors for namespaces based on their ID/name
// Color generation function matching ui-core BotIcon approach
const getNamespaceColor = (namespaceName: string): string => {
  const colorClasses = [
    '#FD9038',  // eesel-neon--carrot (vibrant orange)
    '#2A4A5B',  // eesel-chathams--blue (dark blue)
    '#2A9D90',  // eesel-mountain--meadow (vibrant green)
    '#E76E50',  // eesel-cinnabar (vibrant red)
    '#4F46E5',  // eesel-royal--blue (vibrant blue)
  ];

  const firstLetter = namespaceName.substring(0, 1).toLowerCase();
  const asciiValue = firstLetter.charCodeAt(0);
  const index = asciiValue % colorClasses.length;

  return colorClasses[index];
};

// Test function to verify real namespace data loading
function testNamespaceDataLoading() {
  console.log('🔄 Testing namespace data loading...');
  
  const modal = document.querySelector('.eesel-modal-new');
  if (!modal) {
    console.log('❌ Modal not found');
    return;
  }
  
  const botPicker = modal.querySelector('.eesel-bot-picker-wrapper');
  if (!botPicker) {
    console.log('❌ Bot picker not found');
    return;
  }
  
  // Check if real data is being used (not mock data)
  const text = botPicker.querySelector('.eesel-bot-picker-text');
  const icon = botPicker.querySelector('.eesel-bot-picker-icon');
  
  if (text && icon) {
    console.log('Current bot name:', text.textContent);
    console.log('Icon content (first letter):', icon.textContent);
    console.log('Icon is letter (not emoji):', /^[A-Z]$/.test(icon.textContent || ''));
    
    // Check icon styling
    const iconStyles = window.getComputedStyle(icon);
    console.log('Icon background color:', iconStyles.backgroundColor);
    console.log('Icon is orange:', iconStyles.backgroundColor.includes('253, 144, 56') || iconStyles.backgroundColor === 'rgb(253, 144, 56)');
    
    // Test dropdown functionality with real data
    const button = botPicker.querySelector('.eesel-bot-picker-button');
    if (button) {
      console.log('Testing dropdown with real data...');
      (button as HTMLButtonElement).click();
      
      setTimeout(() => {
        const menu = botPicker.querySelector('.eesel-bot-picker-menu');
        const items = menu?.querySelectorAll('.eesel-bot-picker-item:not(.eesel-bot-picker-create)');
        console.log('Number of namespace options:', items?.length || 0);
        
        // Check z-index
        if (menu) {
          const menuStyles = window.getComputedStyle(menu);
          console.log('Dropdown z-index:', menuStyles.zIndex);
          console.log('Dropdown above modal:', parseInt(menuStyles.zIndex) > 999999);
        }
        
        // Close dropdown
        (button as HTMLButtonElement).click();
      }, 100);
    }
  }
  
  console.log('✅ Namespace data loading test completed');
}

// Test function to verify bot picker actions work
function testBotPickerActions() {
  console.log('🎯 Testing bot picker actions...');
  
  const modal = document.querySelector('.eesel-modal-new');
  if (!modal) {
    console.log('❌ Modal not found');
    return;
  }
  
  const botPicker = modal.querySelector('.eesel-bot-picker-wrapper');
  if (!botPicker) {
    console.log('❌ Bot picker not found');
    return;
  }
  
  // Test dropdown open
  const button = botPicker.querySelector('.eesel-bot-picker-button');
  if (button) {
    (button as HTMLButtonElement).click();
    
    setTimeout(() => {
      const menu = botPicker.querySelector('.eesel-bot-picker-menu');
      if (menu) {
        // Test create new bot button
        const createButton = menu.querySelector('.eesel-bot-picker-create');
        if (createButton) {
          console.log('Create new bot button found');
          // Note: Don't actually click to avoid opening dashboard
        }
        
        // Test settings button
        const settingsButton = menu.querySelector('.eesel-bot-picker-item:has(.eesel-bot-picker-settings)');
        if (settingsButton) {
          console.log('Settings button found');
          // Note: Don't actually click to avoid opening dashboard
        }
        
        // Test sign out button
        const signOutButton = menu.querySelector('.eesel-bot-picker-item:has(.eesel-bot-picker-signout)');
        if (signOutButton) {
          console.log('Sign out button found');
          // Note: Don't actually click to avoid sign out
        }
        
        console.log('All action buttons present');
      }
      
      // Close dropdown
      (button as HTMLButtonElement).click();
    }, 100);
  }
  
  console.log('✅ Bot picker actions test completed');
}

// Comprehensive test function for the improved bot picker
function testImprovedBotPicker() {
  console.log('🚀 Testing improved bot picker implementation...');
  
  const modal = document.querySelector('.eesel-modal-new');
  if (!modal) {
    console.log('❌ Modal not found');
    return;
  }
  
  const botPicker = modal.querySelector('.eesel-bot-picker-wrapper');
  if (!botPicker) {
    console.log('❌ Bot picker not found');
    return;
  }
  
  console.log('✅ Bot picker found');
  
  // Test 1: Icon is first letter, not emoji
  const icon = botPicker.querySelector('.eesel-bot-picker-icon');
  if (icon) {
    const iconText = icon.textContent || '';
    const isLetter = /^[A-Z]$/.test(iconText);
    console.log('Icon content:', iconText);
    console.log('✅ Icon is first letter (not emoji):', isLetter);
    
    // Test icon styling
    const iconStyles = window.getComputedStyle(icon);
    const isOrange = iconStyles.backgroundColor.includes('253, 144, 56') || 
                     iconStyles.backgroundColor === 'rgb(253, 144, 56)' ||
                     iconStyles.backgroundColor.toLowerCase().includes('#fd9038');
    console.log('✅ Icon has orange background:', isOrange);
    console.log('Icon background:', iconStyles.backgroundColor);
  }
  
  // Test 2: Dropdown renders above modal
  const button = botPicker.querySelector('.eesel-bot-picker-button');
  if (button) {
    console.log('Testing dropdown z-index...');
    (button as HTMLButtonElement).click();
    
    setTimeout(() => {
      const menu = botPicker.querySelector('.eesel-bot-picker-menu');
      if (menu) {
        const menuStyles = window.getComputedStyle(menu);
        const zIndex = parseInt(menuStyles.zIndex);
        const modalZIndex = parseInt(window.getComputedStyle(modal).zIndex);
        console.log('Dropdown z-index:', zIndex);
        console.log('Modal z-index:', modalZIndex);
        console.log('✅ Dropdown renders above modal:', zIndex > modalZIndex);
      }
      
      // Close dropdown
      (button as HTMLButtonElement).click();
    }, 100);
  }
}

// Test function to verify authentication flow and always-visible bot picker
function testAuthenticationFlow() {
  console.log('🔐 Testing authentication flow and always-visible bot picker...');
  
  const modal = document.querySelector('.eesel-modal-new');
  if (!modal) {
    console.log('❌ Modal not found');
    return;
  }
  
  const botPicker = modal.querySelector('.eesel-bot-picker-wrapper');
  if (!botPicker) {
    console.log('❌ Bot picker not found');
    return;
  }
  
  console.log('✅ Bot picker is always visible');
  
  // Check authentication state
  const authState = (() => {
    try {
      const authData = localStorage.getItem('eesel-auth');
      return authData ? JSON.parse(authData) : { isAuthenticated: false };
    } catch {
      return { isAuthenticated: false };
    }
  })();
  
  console.log('Current authentication state:', authState.isAuthenticated ? 'Authenticated' : 'Not authenticated');
  
  // Check bot picker text
  const text = botPicker.querySelector('.eesel-bot-picker-text');
  const icon = botPicker.querySelector('.eesel-bot-picker-icon');
  
  if (text && icon) {
    console.log('Bot picker text:', text.textContent);
    console.log('Bot picker icon:', icon.textContent);
    
    // Verify appropriate state
    if (!authState.isAuthenticated) {
      const showsSignIn = text.textContent?.includes('Sign in') || text.textContent?.includes('sign in');
      console.log('✅ Shows sign-in for unauthenticated:', showsSignIn);
      
      if (showsSignIn) {
        console.log('Testing sign-in click...');
        const button = botPicker.querySelector('.eesel-bot-picker-button');
        if (button) {
          console.log('Sign-in button found, click would initiate authentication');
          // Note: Don't actually click to avoid opening popup during test
        }
      }
    } else {
      console.log('✅ User is authenticated');
      
      // Test authenticated state
      const button = botPicker.querySelector('.eesel-bot-picker-button');
      if (button) {
        const buttonText = button.textContent;
        console.log('✅ Authenticated button found with text:', buttonText);
      }
    }
  }
  
  console.log('🎉 Authentication flow test completed!');
  console.log('Summary:');
  console.log('- ✅ Bot picker is always visible');
  console.log('- ✅ Shows appropriate content based on auth state');
  console.log('- ✅ Icon styling is correct');
  console.log('- ✅ Authentication flow is ready');
};

// Test function to verify improved authentication flow
const testImprovedAuthFlow = () => {
  console.log('=== Testing Improved Authentication Flow ===');
  
  // Check if we can find the bot picker
  const botPicker = document.querySelector('.eesel-bot-picker-wrapper');
  if (!botPicker) {
    console.log('❌ Bot picker not found');
    return;
  }
  
  console.log('✅ Bot picker found');
  
  // Check current authentication state
  const currentAuth = getAuthState();
  console.log('Current auth state:', currentAuth);
  
  // Test the checkExistingAuth function
  console.log('Testing checkExistingAuth...');
  checkExistingAuth().then(result => {
    console.log('checkExistingAuth result:', result);
  }).catch(error => {
    console.log('checkExistingAuth error:', error);
  });
  
  // If not authenticated, test login flow preparation
  if (!currentAuth.isAuthenticated) {
    console.log('User not authenticated - testing login preparation...');
    
    const signInButton = botPicker.querySelector('.eesel-bot-picker-button');
    if (signInButton) {
      const buttonText = signInButton.textContent;
      console.log('✅ Sign-in button found with text:', buttonText);
      
      if (buttonText?.includes('Sign in')) {
        console.log('✅ Button shows correct sign-in text');
      } else {
        console.log('❌ Button text incorrect:', buttonText);
      }
    } else {
      console.log('❌ Sign-in button not found');
    }
  } else {
    console.log('✅ User is authenticated');
    
    // Test authenticated state
    const button = botPicker.querySelector('.eesel-bot-picker-button');
    if (button) {
      const buttonText = button.textContent;
      console.log('✅ Authenticated button found with text:', buttonText);
    }
  }
  
  console.log('=== Authentication Flow Test Complete ===');
};

// Test function to verify unique colors for namespaces
function testNamespaceUniqueColors() {
  console.log('🎨 Testing namespace unique colors...');
  
  const modal = document.querySelector('.eesel-modal-new');
  if (!modal) {
    console.log('❌ Modal not found');
    return;
  }
  
  const botPicker = modal.querySelector('.eesel-bot-picker-wrapper');
  if (!botPicker) {
    console.log('❌ Bot picker not found');
    return;
  }
  
  // Test color generation function
  console.log('🔄 Testing color generation function:');
  const testNamespaces = [
    { id: '1', namespace: 'Marketing Bot' },
    { id: '2', namespace: 'Sales Bot' },
    { id: '3', namespace: 'Support Bot' },
    { id: '4', namespace: 'Dev Bot' },
    { id: '5', namespace: 'HR Bot' }
  ];
  
  testNamespaces.forEach(ns => {
    const color = getNamespaceColor(ns.namespace);
    console.log(`${ns.namespace}: ${color}`);
  });
  
  // Test dropdown functionality and check colors
  const button = botPicker.querySelector('.eesel-bot-picker-button');
  if (button) {
    console.log('📱 Testing dropdown colors...');
    (button as HTMLButtonElement).click();
    
    setTimeout(() => {
      const menu = botPicker.querySelector('.eesel-bot-picker-menu');
      const items = menu?.querySelectorAll('.eesel-bot-picker-item-icon');
      
      if (items) {
        console.log(`Found ${items.length} namespace icons`);
        items.forEach((icon, index) => {
          const bgColor = window.getComputedStyle(icon).backgroundColor;
          console.log(`Icon ${index + 1} background color: ${bgColor}`);
          
          // Check if it's not the default orange
          const isNotOrange = !bgColor.includes('253, 144, 56') && bgColor !== 'rgb(253, 144, 56)';
          console.log(`Icon ${index + 1} has unique color (not orange): ${isNotOrange}`);
        });
      }
      
      // Close dropdown
      (button as HTMLButtonElement).click();
    }, 500);
  }
}

// Test function to verify dropdown positioning and sizing improvements
function testDropdownPositioningAndSize() {
  console.log('� Testing dropdown positioning and size improvements...');
  
  const modal = document.querySelector('.eesel-modal-new');
  if (!modal) {
    console.log('❌ Modal not found');
    return;
  }
  
  const botPicker = modal.querySelector('.eesel-bot-picker-wrapper');
  if (!botPicker) {
    console.log('❌ Bot picker not found');
    return;
  }
  
  const button = botPicker.querySelector('.eesel-bot-picker-button');
  if (!button) {
    console.log('❌ Bot picker button not found');
    return;
  }
  
  // Check button positioning first
  const buttonRect = button.getBoundingClientRect();
  console.log('🔘 Button position:', {
    top: buttonRect.top,
    left: buttonRect.left,
    bottom: buttonRect.bottom,
    width: buttonRect.width,
    height: buttonRect.height
  });
  
  console.log('✅ Opening dropdown to test positioning...');
  (button as HTMLButtonElement).click();
  
  setTimeout(() => {
    const menu = botPicker.querySelector('.eesel-bot-picker-menu');
    if (!menu) {
      console.log('❌ Dropdown menu not found after opening');
      return;
    }
    
    const menuRect = menu.getBoundingClientRect();
    const menuStyles = window.getComputedStyle(menu);
    
    console.log('� Dropdown position and size:', {
      top: menuRect.top,
      left: menuRect.left,
      width: menuRect.width,
      height: menuRect.height,
      maxHeight: menuStyles.maxHeight
    });
    
    // Check if dropdown is positioned correctly below button
    const topDifference = menuRect.top - buttonRect.bottom;
    console.log(`� Gap between button and dropdown: ${topDifference}px`);
    console.log(`✅ Dropdown positioned correctly below button: ${topDifference >= 0 && topDifference <= 5}`);
    
    // Check alignment
    const leftAlignment = Math.abs(menuRect.left - buttonRect.left);
    console.log(`� Left alignment difference: ${leftAlignment}px`);
    console.log(`✅ Dropdown aligned with button: ${leftAlignment <= 2}`);
    
    // Check size improvements
    console.log(`� Dropdown width: ${menuRect.width}px (should be ~240px)`);
    console.log(`📐 Dropdown max-height: ${menuStyles.maxHeight} (should be 280px)`);
    
    // Check item sizes
    const items = menu.querySelectorAll('.eesel-bot-picker-item');
    if (items.length > 0) {
      const firstItem = items[0];
      const itemRect = firstItem.getBoundingClientRect();
      const itemStyles = window.getComputedStyle(firstItem);
      
      console.log(`📋 Item height: ${itemRect.height}px (should be ~32px)`);
      console.log(`📋 Item font-size: ${itemStyles.fontSize}`);
      console.log(`📋 Item padding: ${itemStyles.padding}`);
    }
    
    // Close dropdown
    (button as HTMLButtonElement).click();
    console.log('✅ Test completed - dropdown closed');
  }, 500);
}

// Test function to verify edge detection works correctly
function testDropdownEdgeDetection() {
  console.log('🔧 Testing dropdown edge detection...');

  const modal = document.querySelector('.eesel-modal-new');
  if (!modal) {
    console.log('❌ Modal not found');
    return;
  }

  const botPicker = modal.querySelector('.eesel-bot-picker-wrapper');
  if (!botPicker) {
    console.log('❌ Bot picker not found');
    return;
  }

  const button = botPicker.querySelector('.eesel-bot-picker-button');
  if (!button) {
    console.log('❌ Button not found');
    return;
  }

  console.log('✅ Found components, testing edge detection scenarios...');

  // Get original modal position
  const originalModalStyle = (modal as HTMLElement).style.cssText;
  const modalElement = modal as HTMLElement;

  // Test scenarios
  const testScenarios = [
    { name: 'Near right edge', right: '10px', bottom: '50%' },
    { name: 'Near left edge', left: '10px', bottom: '50%' },
    { name: 'Near bottom edge', right: '50%', bottom: '10px' },
    { name: 'Near top edge', right: '50%', top: '10px' },
    { name: 'Corner: bottom-right', right: '10px', bottom: '10px' },
    { name: 'Corner: top-left', left: '10px', top: '10px' }
  ];

  let currentTest = 0;

  function runNextTest() {
    if (currentTest >= testScenarios.length) {
      // Restore original position
      modalElement.style.cssText = originalModalStyle;
      console.log('✅ All edge detection tests completed');
      return;
    }

    const scenario = testScenarios[currentTest];
    console.log(`🔧 Testing scenario: ${scenario.name}`);

    // Position modal for this test
    modalElement.style.position = 'fixed';
    modalElement.style.right = scenario.right || 'auto';
    modalElement.style.left = scenario.left || 'auto';
    modalElement.style.top = scenario.top || 'auto';
    modalElement.style.bottom = scenario.bottom || 'auto';

    // Open dropdown
    (button as HTMLButtonElement).click();

    setTimeout(() => {
      const menu = botPicker?.querySelector('.eesel-bot-picker-menu');
      if (menu && button) {
        const menuRect = menu.getBoundingClientRect();
        const buttonRect = button.getBoundingClientRect();
        const viewport = { width: window.innerWidth, height: window.innerHeight };

        console.log(`📍 ${scenario.name} results:`, {
          buttonPos: { top: buttonRect.top, left: buttonRect.left, right: buttonRect.right, bottom: buttonRect.bottom },
          menuPos: { top: menuRect.top, left: menuRect.left, right: menuRect.right, bottom: menuRect.bottom },
          viewport: viewport,
          inViewport: {
            left: menuRect.left >= 0,
            right: menuRect.right <= viewport.width,
            top: menuRect.top >= 0,
            bottom: menuRect.bottom <= viewport.height
          }
        });

        // Close dropdown
        (button as HTMLButtonElement).click();

        setTimeout(() => {
          currentTest++;
          runNextTest();
        }, 300);
      } else {
        console.log(`❌ Menu not found for ${scenario.name}`);
        currentTest++;
        runNextTest();
      }
    }, 300);
  }

  runNextTest();
}

// Make test functions available globally for debugging
(window as any).eeselTestFunctions = {
  testAuthenticationFlow,
  testImprovedAuthFlow,
  testNamespaceDataLoading,
  testNamespaceUniqueColors,
  testDropdownPositioningAndSize,
  testDropdownEdgeDetection,
  getNamespaceColor,
};

console.log('🔧 Eesel test functions available: window.eeselTestFunctions');